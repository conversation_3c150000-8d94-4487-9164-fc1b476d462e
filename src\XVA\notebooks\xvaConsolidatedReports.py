# Databricks notebook source
# MAGIC %md
# MAGIC `xvaCuratedDataset_ResAppNIPVReports` is a comprehensive PySpark pipeline designed to process and consolidate various XVA  reports into curated Delta tables for downstream analytics or reporting. Here's a detailed documentation summary by section:
# MAGIC
# MAGIC ---
# MAGIC
# MAGIC ## 📌 **General Setup**
# MAGIC
# MAGIC * **Libraries Imported**: Standard PySpark SQL functions, datetime utilities, and Spark session creation.
# MAGIC * **Spark Configuration**: Legacy time parser policy enabled for backward compatibility with date formats.
# MAGIC
# MAGIC ---
# MAGIC
# MAGIC ## 📊 **ResApp4 – Net Commodity Delta**
# MAGIC
# MAGIC * **Sources**:
# MAGIC
# MAGIC   * `f"{bronze_catalog}.{schema_name}.vwdeltapv`
# MAGIC   * `f"{bronze_catalog}.{schema_name}.quantificommoditydeltanetbycounterpartyandcommodity`
# MAGIC * **Key Steps**:
# MAGIC
# MAGIC   * Date formatting with a UDF.
# MAGIC   * Union of datasets with aligned column names.
# MAGIC   * Aggregation by `business_date` and `index`.
# MAGIC   * Final result includes `asset_class`, and is saved to `{gold_catalog}.{schema_name}.resapp4`.
# MAGIC
# MAGIC ---
# MAGIC
# MAGIC ## 💱 **ResApp11 – Net FX Sensitivity**
# MAGIC
# MAGIC * **Sources**:
# MAGIC
# MAGIC   * `f"{bronze_catalog}.{schema_name}.xvasummaryrisksensitivitiesnetbycounterparty`
# MAGIC   * `f"{bronze_catalog}.{schema_name}.eodfxsensitivityreport`
# MAGIC * **Transformations**:
# MAGIC
# MAGIC   * FX curve names cleaned by removing "USD".
# MAGIC   * Business date converted to `dd-MMM-yyyy`.
# MAGIC   * Aggregated FX sensitivities stored in `{gold_catalog}.{schema_name}.resapp11`.
# MAGIC
# MAGIC ---
# MAGIC
# MAGIC ## 💵 **ResApp12 – Net PV01 (Rho) Sensitivity**
# MAGIC
# MAGIC * **Sources**:
# MAGIC
# MAGIC   * `f"{bronze_catalog}.{schema_name}.xvasummaryrisksensitivitiesnetbycounterparty`
# MAGIC   * `f"{bronze_catalog}.{schema_name}.eodpvrhosensitivityreport`
# MAGIC * **Transformations**:
# MAGIC
# MAGIC   * Interest rate curve names cleaned.
# MAGIC   * Same date formatting and aggregation as ResApp11.
# MAGIC   * Result written to `{gold_catalog}.{schema_name}.resapp12`.
# MAGIC
# MAGIC ---
# MAGIC
# MAGIC ## 🧾 **ResApp9 – CS01 by Credit Curve**
# MAGIC
# MAGIC * **Sources**:
# MAGIC
# MAGIC   * `f"{bronze_catalog}.{schema_name}.eodcsreport`
# MAGIC   * `f"{bronze_catalog}.{schema_name}.xvacsreportnetbycounterparty`
# MAGIC * **Steps**:
# MAGIC
# MAGIC   * Harmonize field names and structures.
# MAGIC   * Merge and write to `{gold_catalog}.{schema_name}.ResApp9`.
# MAGIC
# MAGIC ---
# MAGIC
# MAGIC ## 🌍 **vega6Report – Commodity Vega**
# MAGIC
# MAGIC * **Sources**:
# MAGIC
# MAGIC   * `f"{bronze_catalog}.{schema_name}.vwmosvegastrikesurface`
# MAGIC   * `f"{bronze_catalog}.{schema_name}.quantificommodityvegareport`
# MAGIC * **Transformations**:
# MAGIC
# MAGIC   * Date conversion with UDF.
# MAGIC   * Union by index and expiry, then grouped by `index_name` and `expiry`.
# MAGIC   * Output written to `{gold_catalog}.{schema_name}.vega6Report`.
# MAGIC
# MAGIC ---
# MAGIC
# MAGIC ## 🧮 **xva\_eodApp2 – Quantifi PnL Report**
# MAGIC
# MAGIC * **Source**: `staging.xvaplreportcounterpartylevel`
# MAGIC * **Transformations**:
# MAGIC
# MAGIC   * Renaming `fba_balance` → `fva_balance`.
# MAGIC   * Calculation of:
# MAGIC
# MAGIC     * Daily Moves (`cva_daily_move`, `fva_daily_move`)
# MAGIC     * MTD/YTD for CVA and FVA
# MAGIC * **Output**: `{gold_catalog}.{schema_name}.eodApp2`
# MAGIC
# MAGIC ---
# MAGIC
# MAGIC ## 🛡️ **IPV Reports – CS01 and Credit Curves**
# MAGIC
# MAGIC * **Sources**:
# MAGIC
# MAGIC   * `f"{bronze_catalog}.{schema_name}.eodcsreport`, `f"{bronze_catalog}.{schema_name}.eodcsipvreport`, `f"{bronze_catalog}.{schema_name}.creditcurves`, `f"{bronze_catalog}.{schema_name}.xvacsreportnetbycounterparty`
# MAGIC * **Output Tables**:
# MAGIC
# MAGIC   * `{gold_catalog}.{schema_name}.CDSCS01`
# MAGIC   * `{gold_catalog}.{schema_name}.XVACS01`
# MAGIC
# MAGIC ---
# MAGIC
# MAGIC ## 🧾 **xva\_eod4 – Desk-Level EOD XVA PnL Summary**
# MAGIC
# MAGIC * **Key Features**:
# MAGIC
# MAGIC   * Standardizes date formats with comprehensive regex and fallback logic.
# MAGIC   * Performs data quality checks on `value_date`.
# MAGIC   * Summarizes results for:
# MAGIC
# MAGIC     * App1: `eodcdspnlreport`
# MAGIC     * App2: `xvaplreportcounterpartylevel`
# MAGIC     * App3: Multiple instrument-level explained PnL sources
# MAGIC * **Output**: `{gold_catalog}.{schema_name}.eod4`
# MAGIC
# MAGIC ---
# MAGIC
# MAGIC ## ✅ **Final Notes**
# MAGIC
# MAGIC * Consistent use of `overwriteSchema` ensures schema evolution.
# MAGIC * All curated tables are saved in Delta format.
# MAGIC * Clear modular structure and consistent naming conventions support scalability and debugging.
# MAGIC

# COMMAND ----------

dbutils.widgets.text("env", "")
dbutils.widgets.text("entity", "")

env = dbutils.widgets.get("env").lower()
entity = dbutils.widgets.get("entity").upper()

bronze_catalog = f"mbcl_{env}_bronze"
gold_catalog = f"mbcl_{env}_gold"
schema_name = entity.lower()


# COMMAND ----------

# Declare Libraries
from pyspark.sql.functions import to_date, date_format, col, lag, year, month, sum as spark_sum
from pyspark.sql import SparkSession
from pyspark.sql import functions as F
from pyspark.sql import types as T
from pyspark.sql.window import Window
from pyspark.sql.types import StringType

# COMMAND ----------

# MAGIC %run ./Utils_functions

# COMMAND ----------

import logging

_log = logging.getLogger("Consolidated reports")
if not _log.handlers:
    h = logging.StreamHandler()
    h.setFormatter(logging.Formatter("%(asctime)s [%(levelname)s] %(message)s",
                                     "%Y-%m-%d %H:%M:%S"))
    _log.addHandler(h)
    _log.setLevel(logging.INFO)

# COMMAND ----------

# Spark Custom Settings
# Set the legacy time parser policy
spark.conf.set("spark.sql.legacy.timeParserPolicy", "LEGACY")

# COMMAND ----------

# MAGIC %md
# MAGIC **ResApp4 - Consolidated XVA Desk Net Commodity Delta (By Comm Index)**

# COMMAND ----------

from pyspark.sql import SparkSession
from pyspark.sql.functions import sum as _sum, col, udf, coalesce, lit
from pyspark.sql.types import StringType
from datetime import datetime

# Initialize Spark session
spark = SparkSession.builder.appName("XVA").getOrCreate()

# Check if all required tables are present
used_tables = [
  f"{bronze_catalog}.{schema_name}.vwdeltapv",
  f"{bronze_catalog}.{schema_name}.quantificommoditydeltanetbycounterpartyandcommodity"
]

if tables_exists(used_tables):
    # Load the tables
    df_vwdeltapv = spark.table(f"{bronze_catalog}.{schema_name}.vwdeltapv")
    df_commoditydelta = spark.table(f"{bronze_catalog}.{schema_name}.quantificommoditydeltanetbycounterpartyandcommodity")

    # Define a UDF to convert the date format
    def convert_date(date_str):
        try:
            # Try to parse the date in the original format
            date_obj = datetime.strptime(date_str, "%m/%d/%Y %I:%M:%S %p")
        except ValueError:
            # If it fails, assume it's already in the desired format
            date_obj = datetime.strptime(date_str, "%d-%b-%Y")
        return date_obj.strftime("%d-%b-%Y")

    convert_date_udf = udf(convert_date, StringType())

    # Apply the UDF to the business_date column in both dataframes
    df_vwdeltapv = df_vwdeltapv.withColumn("businessDate", convert_date_udf(df_vwdeltapv["businessDate"]))
    df_commoditydelta = df_commoditydelta.withColumn("businessDate", convert_date_udf(df_commoditydelta["businessDate"]))


    # Rename columns to match for union
    df_commoditydelta = df_commoditydelta.withColumnRenamed("xvacommoditydelta", "combinedDelta") 
    df_commoditydelta = df_commoditydelta.withColumn("assetclass", col("assetclass").cast("string"))


    # Ensure df_combined is defined
    df_combined = df_vwdeltapv.unionByName(df_commoditydelta, allowMissingColumns=True)

    # Replace null values in asset_class with a placeholder
    # df_combined = df_combined.withColumn("asset_class", coalesce(df_combined["asset_class"], lit("Unknown")))

    # Group by and sum the required columns, ignoring asset_class
    df_aggregated = df_combined.groupBy("businessDate", "index") \
                            .agg(_sum("combinedDelta").alias("combined_delta"),
                                    _sum("subgroupinternalwarninglimit").alias("sub_group_internal_warning_limit"),
                                    _sum("subgroupwarninglimit").alias("sub_group_warning_limit"))

    # Join the aggregated results back with the original dataset to add asset_class
    df_result = df_aggregated.join(df_combined.select("businessDate", "index", "assetclass").distinct(), 
                                on=["businessDate", "index"], 
                                how="left")

    # Filter out duplicates 
    df_result = df_result.dropDuplicates(["businessdate", "index"])

    # # Save the merged dataframe as a table
    df_result.write.format("delta").mode("overwrite").option("overwriteSchema", "true").saveAsTable(f"{gold_catalog}.{schema_name}.resapp4")
else:
    _log.info(f"At least one of the following tables is missing: {used_tables}")



# COMMAND ----------

# MAGIC %md
# MAGIC **ResApp11 - Consolidated XVA Desk Net FX Sensitivity**

# COMMAND ----------

# Check if all required tables are present
used_tables = [
  f"{bronze_catalog}.{schema_name}.xvasummaryrisksensitivitiesnetbycounterparty"
]

if tables_exists(used_tables):
    # Pre-convert Business Date
    df_pre = spark.sql(f"""
    SELECT 
        DISTINCT 
        businessdate AS BusinessDate,
        REPLACE(fxcurvename, 'USD', '') as Ccy, 
        xvaFx01 as FXSensitivity
    FROM {bronze_catalog}.{schema_name}.xvasummaryrisksensitivitiesnetbycounterparty
    WHERE fxcurvename is NOT NULL
    """)

    # Convert the date to the desired format
    df_pre = df_pre.withColumn("BusinessDate", date_format(to_date(col("BusinessDate"), "MM/dd/yyyy"), "dd-MMM-yyyy"))

    # Save the merged dataframe as a table
    df_pre.write.format("delta").mode("overwrite").option("overwriteSchema", "true").saveAsTable(f"{bronze_catalog}.{schema_name}.tempxvasummaryrisksensitivitiesnetbycounterparty_fx")
else:
    _log.info(f"At least one of the following tables is missing: {used_tables}")


# COMMAND ----------

# Check if all required tables are present
used_tables = [
  f"{bronze_catalog}.{schema_name}.eodfxsensitivityreport",
  f"{bronze_catalog}.{schema_name}.tempxvasummaryrisksensitivitiesnetbycounterparty_fx",
]

if tables_exists(used_tables):
  df = spark.sql(f"""

  WITH CTE_TEMP AS (
      SELECT 
        DISTINCT  
        BusinessDate as BusinessDate,
        ccy,
        fxSensitivity as FXSensitivity
      FROM {bronze_catalog}.{schema_name}.eodfxsensitivityreport
      
      UNION ALL 

      SELECT 
        DISTINCT 
        BusinessDate,
        Ccy, 
        FXSensitivity
      FROM {bronze_catalog}.{schema_name}.tempxvasummaryrisksensitivitiesnetbycounterparty_fx
  )

  SELECT  
      BusinessDate,
      ccy,
      SUM(FXSensitivity) AS FXSensitivity
  FROM 
      CTE_TEMP 
  GROUP BY 
      ccy, BusinessDate

  """)

  # save the merged dataframe as a table
  df.write.format("delta").mode("overwrite").option("overwriteSchema", "true").saveAsTable(f"{gold_catalog}.{schema_name}.resapp11")
else:
  _log.info(f"At least one of the following tables is missing: {used_tables}")

# COMMAND ----------

# MAGIC %md
# MAGIC **<mark>ResApp12 - Consolidated XVA Desk Net PV01 (Rho) Sensitivity </mark>**

# COMMAND ----------

# Check if all required tables are present
used_tables = [
  f"{bronze_catalog}.{schema_name}.xvasummaryrisksensitivitiesnetbycounterparty"
]

if tables_exists(used_tables):
  # Pre-convert Business Date
  df_pre = spark.sql(f"""
  SELECT 
      DISTINCT 
      businessDate AS BusinessDate,
        REPLACE(rateindex, 'LIBOR.', '')  as Ccy, 
        xvaIr01  as TotalPV0InCCY
  FROM {bronze_catalog}.{schema_name}.xvasummaryrisksensitivitiesnetbycounterparty
  --where fx_curve_name is NOT NULL

  """)

  # Convert the date to the desired format
  df_pre = df_pre.withColumn("BusinessDate", date_format(to_date(col("BusinessDate"), "MM/dd/yyyy"), "dd-MMM-yyyy"))

  # save the merged dataframe as a table
  df_pre.write.format("delta").mode("overwrite").option("overwriteSchema", "true").saveAsTable(f"{bronze_catalog}.{schema_name}.tempxvasummaryrisksensitivitiesnetbycounterparty_pv")
else:
  _log.info(f"At least one of the following tables is missing: {used_tables}")



# COMMAND ----------

# Check if all required tables are present
used_tables = [
  f"{bronze_catalog}.{schema_name}.eodpvrhosensitivityreport",
  f"{bronze_catalog}.{schema_name}.tempxvasummaryrisksensitivitiesnetbycounterparty_pv"
]

if tables_exists(used_tables):
  df = spark.sql(f"""
  WITH CTE_TEMP AS (
      SELECT 
        DISTINCT   BusinessDate,
        ccy,
        basePv01InUsd as TotalPV0InCCY
      FROM {bronze_catalog}.{schema_name}.eodpvrhosensitivityreport

  UNION ALL
  SELECT 
      DISTINCT 
        BusinessDate,
      Ccy, 
      TotalPV0InCCY
  FROM {bronze_catalog}.{schema_name}.tempxvasummaryrisksensitivitiesnetbycounterparty_pv ri
  )

  SELECT distinct
  BusinessDate,
  ccy,
  SUM(TotalPV0InCCY) AS TotalPV0InCCY
  FROM CTE_TEMP 
  group by ccy, BusinessDate

  """)

  # save the merged dataframe as a table
  df.write.format("delta").mode("overwrite").option("overwriteSchema", "true").saveAsTable(f"{gold_catalog}.{schema_name}.resapp12")
else:
  _log.info(f"At least one of the following tables is missing: {used_tables}")




# COMMAND ----------

# MAGIC %md
# MAGIC **ResApp9 - Consolidated XVA Desk Net CS01 Sensitivity Report (By Curve)**

# COMMAND ----------

# Load marketriskcsreportbycreditcurve

# Initialize Spark session
spark = SparkSession.builder \
    .appName("XVA Report") \
    .getOrCreate()

# Check if all required tables are present
used_tables = [
  f"{bronze_catalog}.{schema_name}.eodcsreport",
  f"{bronze_catalog}.{schema_name}.xvacsreportnetbycounterparty"
]

if tables_exists(used_tables):
    # Load the data from the table
    df_ccr = spark.table(f"{bronze_catalog}.{schema_name}.eodcsreport")

    # Select the required columns
    result_df_marketriskcsreportbycreditcurve = df_ccr.select(
        "valueDate",
        "bondReferenceEntity",
        "currency",
        "mitsuisector",
        "referencecreditcurvename",
        "CS01",
        "6m",
        "1y",
        "2y",
        "3y",
        "4y",
        "5y",
        "7y",
        "10y",
        "total"
    )

    # Rename columns
    result_df_marketriskcsreportbycreditcurve = result_df_marketriskcsreportbycreditcurve.withColumnRenamed("bondreferenceentity", "BondReferenceEntity") \
                        .withColumnRenamed("mitsuisector", "MitsuiSector") \
                        .withColumnRenamed("referencecreditcurvename", "ReferenceCreditCurvename") \
                        .withColumnRenamed("total", "Total")




    # Initialize Spark session
    spark = SparkSession.builder \
        .appName("XVA Report") \
        .getOrCreate()

    # Load the data from the table
    df_csr = spark.table(f"{bronze_catalog}.{schema_name}.xvacsreportnetbycounterparty")

    # Select the required columns
    result_df_xvacsreportnetbycounterparty = df_csr.select(
        "valuedate",
        "counterparty",
        "Currency",
        "mitsuisector",
        F.col("ticker").alias("ReferenceCreditCurvename"),
        F.lit("XVA Sens").alias("CS01"),
        "6m",
        "1y",
        "2y",
        "3y",
        "4y",
        "5y",
        "6y",
        "10y",
        "15y",
        "20y",
        "25y",
        "30y",
        "total"
    )

    # Rename columns
    result_df_xvacsreportnetbycounterparty = result_df_xvacsreportnetbycounterparty.withColumnRenamed("counterparty", "BondReferenceEntity") \
                        .withColumnRenamed("mitsui_sector", "MitsuiSector") \
                        .withColumnRenamed("total", "Total")
                        
    result_df_xvacsreportnetbycounterparty = result_df_xvacsreportnetbycounterparty.withColumn("valuedate", date_format(to_date(col("valuedate"), "MM/dd/yyyy"), "dd-MMM-yyyy"))


    # Merge the results into one dataframe, including all columns from both dataframes
    merged_df = result_df_marketriskcsreportbycreditcurve.unionByName(result_df_xvacsreportnetbycounterparty, allowMissingColumns=True)

    merged_df = cast_to_inferred_type(merged_df)


    # save the merged dataframe as a table
    merged_df.write.format("delta").mode("overwrite").option("overwriteSchema", "true").saveAsTable(f"{gold_catalog}.{schema_name}.ResApp9")
else:
    _log.info(f"At least one of the following tables is missing: {used_tables}")

# COMMAND ----------

# MAGIC %md
# MAGIC **vega6Repeort - Consolidated XVA desk Commodity Vega**

# COMMAND ----------

from pyspark.sql import DataFrame
from pyspark.sql.functions import to_date, sum as pyspark_sum, udf, col
from pyspark.sql.types import StringType
from datetime import datetime

# Initialize Spark session
spark = SparkSession.builder \
    .appName("XVA Report") \
    .getOrCreate()

# Check if all required tables are present
used_tables = [
  f"{bronze_catalog}.{schema_name}.vwmosvegastrikesurface",
  f"{bronze_catalog}.{schema_name}.quantificommodityvegareport"
]

if tables_exists(used_tables):
    # Load the data from the table
    df_ccr = spark.table(f"{bronze_catalog}.{schema_name}.vwmosvegastrikesurface")
    df_dcr = spark.table(f"{bronze_catalog}.{schema_name}.quantificommodityvegareport")

    # Define a UDF to convert the date format
    def convert_date(date_str):
        try:
            # Try to parse the date in the original format
            date_obj = datetime.strptime(date_str, "%m/%d/%Y %I:%M:%S %p")
        except ValueError:
            # If it fails, assume it's already in the desired format
            date_obj = datetime.strptime(date_str, "%d-%b-%Y")
        return date_obj.strftime("%Y-%m-%d")

    convert_date_udf = udf(convert_date, StringType())

    # Select the required columns
    result_df_vwmosvegastrikesurface = df_ccr.select(
        "rundate",
        "businessdate",
        "indexname",
        to_date("expiry", 'dd-MMM-yy').alias("expiry"),
        "vega"
    )

    result_df_vwmosvegastrikesurface = result_df_vwmosvegastrikesurface.withColumn("businessdate", convert_date_udf(result_df_vwmosvegastrikesurface["businessdate"]))

    # Select the required columns
    result_df_quantificommodityvegareport = df_dcr.select(
        "date",
        "businessdate",
        "index",
        "expiry",
        col("xvaCommodityVega").alias("commodityvega")
    )

    result_df_quantificommodityvegareport = result_df_quantificommodityvegareport.withColumn("expiry", convert_date_udf(result_df_quantificommodityvegareport["expiry"]))
    result_df_quantificommodityvegareport = result_df_quantificommodityvegareport.withColumn("businessdate", convert_date_udf(result_df_quantificommodityvegareport["businessdate"]))

    # Rename columns
    result_df_quantificommodityvegareport = result_df_quantificommodityvegareport.withColumnRenamed("date", "rundate") \
                .withColumnRenamed("commodityvega", "vega") \
                .withColumnRenamed("index", "indexname")

    # Merge the results into one dataframe, including all columns from both dataframes
    merged_df = result_df_vwmosvegastrikesurface.unionByName(result_df_quantificommodityvegareport, allowMissingColumns=False)

    # Group by index_name and expiry first, then add business_date and sum the Vega values
    grouped_df = merged_df.groupBy("indexname", "expiry").agg(pyspark_sum("vega").alias("vega"))

    # Join grouped_df with the original DataFrame to get business_date
    final_df = grouped_df.join(merged_df, on=["indexname", "expiry"], how="left").select(
    grouped_df["indexname"],
    grouped_df["expiry"],
    grouped_df["vega"],
    merged_df["businessdate"]
    ).distinct()

    # Save the final grouped dataframe as a table
    final_df.write.format("delta").mode("overwrite").option("overwriteSchema", "true").saveAsTable(f"{gold_catalog}.{schema_name}.vega6Report")
else:
    _log.info(f"At least one of the following tables is missing: {used_tables}")

# COMMAND ----------

# MAGIC %md
# MAGIC **eodApp2 - Quantifi PnL Report**

# COMMAND ----------


# Initialize Spark session
spark = SparkSession.builder \
    .appName("XVA Report") \
    .getOrCreate()

# Check if all required tables are present
used_tables = [
  f"{bronze_catalog}.{schema_name}.xvaplreportcounterpartylevel"
]

if tables_exists(used_tables):
  # Load the data from the table
  df_eod2 = spark.table(f"{bronze_catalog}.{schema_name}.xvaplreportcounterpartylevel")
  df_eod2 = cast_to_inferred_type(df_eod2)

  result_df_eod2= df_eod2.select(
    "valueDate",
    "cptyname",
    "cptyrating",
  "masteragreement",
  "xvabalance",
  "cvabalance",
  "fbabalance",
  "xvaNewBusnEffect",
  "xvaterminationeffect",
  "xvacrediteffect",
  "xvacommodityeffect",
  "xvaireffect",
  "xvaFactorLoadingEffectVega",
  "xvafxeffect",
  "xvaunexplainedeffect",
  "Currency"
  )
  result_df_eod2 = result_df_eod2.withColumnRenamed("fbabalance", "fvabalance")
  result_df_eod2 = result_df_eod2.withColumn("valuedate", date_format(to_date(col("valuedate"), "MM/dd/yyyy"), "dd-MMM-yy"))
  result_df_eod2 = result_df_eod2.filter(col("valuedate").isNotNull())

  # # Initialize Spark session
  # spark = SparkSession.builder.appName("CVA Calculations").getOrCreate()

  # Define window specifications
  window_spec = Window.orderBy('valuedate')
  window_spec_mtd = Window.partitionBy('year', 'month').orderBy('valuedate').rowsBetween(Window.unboundedPreceding, Window.currentRow)
  window_spec_ytd = Window.partitionBy('year').orderBy('valuedate').rowsBetween(Window.unboundedPreceding, Window.currentRow)

  # Calculate CVA Daily Move
  df_cva = result_df_eod2.withColumn('cvadailymove', col('cvabalance') - lag(col('cvabalance')).over(window_spec))

  # Calculate FVA Daily Move
  df = df_cva.withColumn('fvadailymove', col('fvabalance') - lag(col('fvabalance')).over(window_spec))

  # Extract year and month from value_date
  df = df.withColumn('valuedate', to_date(col('valuedate'), 'dd-MMM-yy')).withColumn('year', year(col('valuedate'))).withColumn('month', month(col('valuedate')))

  # Calculate CVA MTD using CVA balance
  df_mtd = df.withColumn('cvamtd', spark_sum('cvabalance').over(window_spec_mtd))

  # Calculate CVA YTD using CVA balance
  df = df_mtd.withColumn('cvaytd', spark_sum('cvabalance').over(window_spec_ytd))

  # Calculate FVA MTD using CVA balance
  df = df.withColumn('fvamtd', spark_sum('fvabalance').over(window_spec_mtd))

  # Calculate FVA YTD using CVA balance
  df = df.withColumn('fvaytd', spark_sum('fvabalance').over(window_spec_ytd))

  # save the merged dataframe as a table
  df.write.format("delta").mode("overwrite").option("overwriteSchema", "true").saveAsTable(f"{gold_catalog}.{schema_name}.eodApp2")
else:
  _log.info(f"At least one of the following tables is missing: {used_tables}")

# COMMAND ----------

# MAGIC %md
# MAGIC **IPV Reports**

# COMMAND ----------

# Initialize Spark session
spark = SparkSession.builder \
    .appName("XVA Report") \
    .getOrCreate()

# Check if all required tables are present
used_tables = [
  f"{bronze_catalog}.{schema_name}.eodcsreport",
  f"{bronze_catalog}.{schema_name}.eodcsipvreport"
]

if tables_exists(used_tables):
    # Load the data from the table
    df_ccr = spark.table(f"{bronze_catalog}.{schema_name}.eodcsreport")

    # Select the required columns
    result_df_marketriskcsreportbycreditcurve = df_ccr.select(
        "valuedate",
        F.lit("Credit Spread").alias("TenorData"),
        "bondreferenceentity",
        "currency",
        "mitsuisector",
        "referencecreditcurvename",
        "CS01",
        "6m",
        "1y",
        "2y",
        "3y",
        "4y",
        "5y",
        "7y",
        "10y",
        "total"
    )

    # Rename columns
    result_df_marketriskcsreportbycreditcurve = result_df_marketriskcsreportbycreditcurve.withColumnRenamed("bondreferenceentity", "BondReferenceEntity") \
                        .withColumnRenamed("mitsuisector", "MitsuiSector") \
                        .withColumnRenamed("referencecreditcurvename", "ReferenceCreditCurvename") \
                        .withColumnRenamed("total", "Total")

    # Initialize Spark session
    spark = SparkSession.builder \
        .appName("XVA Report") \
        .getOrCreate()

    # Load the data from the table
    df_csr = spark.table(f"{bronze_catalog}.{schema_name}.eodcsipvreport")

    # Select the required columns
    result_df_xvacsreportnetbycounterparty = df_csr.select(
        "valuedate",
        F.lit("CDS CS01").alias("TenorData"),
        "Currency",
        F.col("mitsuisector").cast(StringType()).alias("mitsuisector"),
        F.col("referenceCreditCurveName").alias("ReferenceCreditCurvename"),
        F.lit("XVA Sens").alias("CS01"),
        "6m",
        "1y",
        "2y",
        "3y",
        "4y",
        "5y",
        "10y",
        F.lit("").alias("15y"),
        F.lit("").alias("20y"),
        F.lit("").alias("25y"),
        F.lit("").alias("30y")
    )

    # Rename columns
    result_df_xvacsreportnetbycounterparty = result_df_xvacsreportnetbycounterparty.withColumnRenamed("counterparty", "BondReferenceEntity") \
                        .withColumnRenamed("mitsuisector", "MitsuiSector") 

    # Merge the results into one dataframe, including all columns from both dataframes
    merged_df = result_df_marketriskcsreportbycreditcurve.unionByName(result_df_xvacsreportnetbycounterparty, allowMissingColumns=True)

    # Convert the value date
    merged_df = merged_df.withColumn("valuedate", F.date_format(F.to_date(F.col("valuedate"), "dd-MMM-yyyy"), "dd-MMM-yy"))

    merged_df = cast_to_inferred_type(merged_df)

    columns_to_cast = ["15y", "20y", "25y", "30y"]
    for col_name in columns_to_cast:
        merged_df = merged_df.withColumn(
            col_name,
            F.when(F.col(col_name).rlike(numeric_double_reggex), F.col(col_name).cast(T.DoubleType()))
            .otherwise(F.lit(None))
        )

    # save the merged dataframe as a table
    merged_df.write.format("delta").mode("overwrite").option("overwriteSchema", "true").saveAsTable(f"{gold_catalog}.{schema_name}.CDSCS01")
else:
    _log.info(f"At least one of the following tables is missing: {used_tables}")

# COMMAND ----------

# Initialize Spark session
spark = SparkSession.builder \
    .appName("XVA Report") \
    .getOrCreate()

# Check if all required tables are present
used_tables = [
  f"{bronze_catalog}.{schema_name}.creditcurves",
  f"{bronze_catalog}.{schema_name}.xvacsreportnetbycounterparty"
]

if tables_exists(used_tables):
    # Load the data from the table
    df_ccs = spark.table(f"{bronze_catalog}.{schema_name}.creditcurves")

    # Select the required columns
    result_df_creditcurves = df_ccs.select(
        F.lit("Credit Spread").alias("TenorData"),
        "CurveTicker",
        "6m",
        "1y",
        "2y",
        "3y",
        "4y",
        "5y",
        "7y",
        "10y"
    )

    # Rename columns
    result_df_creditcurves = result_df_creditcurves.withColumnRenamed("CurveTicker", "ReferenceCreditCurvename")
                    
    # Load the data from the table
    df_csr = spark.table(f"{bronze_catalog}.{schema_name}.xvacsreportnetbycounterparty")

    # Select the required columns
    result_df_xvacsreportnetbycounterparty = df_csr.select(
        F.lit("XVA Sens").alias("TenorData"),
        F.col("ticker").alias("ReferenceCreditCurvename"),
        "6m",
        "1y",
        "2y",
        "3y",
        "4y",
        "5y",
        "6y",
        "10y",
        "15y",
        "20y",
        "25y",
        "30y",
        "total"

    )

    # Rename columns
    result_df_xvacsreportnetbycounterparty = result_df_xvacsreportnetbycounterparty.withColumnRenamed("counterparty", "BondReferenceEntity")

    result_df_xvacsreportnetbycounterparty = cast_to_inferred_type(result_df_xvacsreportnetbycounterparty)
    result_df_creditcurves = cast_to_inferred_type(result_df_creditcurves)

    # Merge the results into one dataframe, including all columns from both dataframes
    merged_df = result_df_creditcurves.unionByName(result_df_xvacsreportnetbycounterparty, allowMissingColumns=True)

    # Display the merged dataframe
    # display(merged_df)

    #  save the merged dataframe as a table
    merged_df.write.format("delta").mode("overwrite").option("overwriteSchema", "true").saveAsTable(f"{gold_catalog}.{schema_name}.XVACS01")
else:
    _log.info(f"At least one of the following tables is missing: {used_tables}")

# COMMAND ----------

# MAGIC %md
# MAGIC **eod4 - EOD XVA Desk pnl Report Desk level**

# COMMAND ----------

# ──────────────────────────────────────────────────────────────────────────────
# Imports
# ──────────────────────────────────────────────────────────────────────────────
from pyspark.sql import SparkSession, DataFrame, functions as F
from pyspark.sql.types import StringType, DateType
from pyspark.sql.utils import AnalysisException
from functools import reduce
from datetime import datetime

spark = SparkSession.getActiveSession() or SparkSession.builder.getOrCreate()

# ──────────────────────────────────────────────────────────────────────────────
# 3. Table-level wrapper (unchanged)
# ──────────────────────────────────────────────────────────────────────────────
def process_table(table_name: str, date_column: str = "valuedate") -> DataFrame:
    bronze_fqn = f"{bronze_catalog}.{schema_name}.{table_name}"  
    try:
        df = spark.table(bronze_fqn)
        df.limit(1).collect() # force evaluation to check if table exists
        return df
    except AnalysisException as e:
        if "TABLE_OR_VIEW_NOT_FOUND" in str(e):
            _log.info("Table missing: %s", table_name)
            return None
        else:
            raise # re-raise if is a different issue

# ──────────────────────────────────────────────────────────────────────────────
# 4. MAIN PROCESSING LOGIC (unchanged)
# ──────────────────────────────────────────────────────────────────────────────
# [Rest of your existing processing logic remains exactly the same]


# ──────────────────────────────────────────────────────────────────────────────
# 4. MAIN PROCESSING LOGIC  (unchanged except for using new helper)
# ──────────────────────────────────────────────────────────────────────────────
# A. App 1  (book C)
app1 = process_table("eodcdspnlreport")       # 'valuedate' column
app1_summary = None
if app1:
    app1 = app1.withColumn("valueDate", F.date_format(F.to_date(F.col("valueDate"), "dd-MMM-yyyy"), "dd-MMM-yy"))
    app1_summary = app1.groupBy().agg(
        F.max("valuedate").alias("Value_Date"),
        F.lit("MBCL").alias("MBCL_Legal_Entity"),
        F.lit("XVA_C").alias("XVA_Book"),
        F.sum("totalPL").alias("Total_P&L"),
        F.lit(None).cast("double").alias("CVA_Move"),
        F.lit(None).cast("double").alias("FVA_Move"),
        F.sum("newTradesPnL").alias("New_Trade_P&L"),
        F.sum("cancelledTradesPnL").alias("Cancelled_Trade_P&L"),
        F.sum("amendedTradesPnL").alias("Amended_Trade_P&L"),
        F.sum("creditSpreadPLChange").alias("Credit_Delta_P&L"),
        F.lit(None).cast("double").alias("Commodity_Delta_P&L"),
        F.sum("fxPnL").alias("FX_P&L"),
        F.sum("irDeltaPnL").alias("IR_P&L"),
        F.lit(None).cast("double").alias("Commodity_Vega_P&L"),
        F.lit(None).cast("double").alias("Gamma_Impact"),
        F.lit(None).cast("double").alias("Fixings_Impact"),
        F.sum("theta").alias("Theta_Impact"),
        F.sum("dailyCashMove").alias("CDS_Cash_move"),
        F.sum("pnLExplained").alias("Explained_P&L"),
        F.sum("pnLUnexplained").alias("Unexplained_P&L"),
    )

# B. App 2  (book QF – uses valueDate column name, so override)
app2  = process_table("xvaplreportcounterpartylevel", date_column="valueDate")
app2_summary = None
if app2:
    segregations = {"MBCL-London": "MBCL", "MBCF-France": "MBCF"}
    app2_summaries = []

    for segregation_val, entity in segregations.items():
        df = app2.filter(F.col("segregation") == segregation_val)
        df = df.withColumn("valueDate", F.date_format(F.to_date(F.col("valueDate"), "MM/dd/yyyy HH:mm"), "dd-MMM-yy"))
        summary = df.groupBy().agg(
            F.max("valueDate").alias("Value_Date"),
            F.lit(entity).alias("MBCL_Legal_Entity"),
            F.lit("QF").alias("XVA_Book"),
            F.sum("xvaPnl").alias("Total_P&L"),
            (F.sum("cvabalance")  - F.sum("cvaBalanceT1")).alias("CVA_Move"),
            (F.sum("fbabalance") + F.sum("fcabalance") - F.sum("fbabalanceT1") - F.sum("fcabalanceT1")).alias("FVA_Move"),
            F.sum("xvaNewBusnEffect").alias("New_Trade_P&L"),
            F.sum("xvaTerminationEffect").alias("Cancelled_Trade_P&L"),
            F.lit(None).cast("double").alias("Amended_Trade_P&L"),
            F.sum("xvaCreditEffect").alias("Credit_Delta_P&L"),
            F.sum("xvaCommodityEffect").alias("Commodity_Delta_P&L"),
            F.sum("xvaFxEffect").alias("FX_P&L"),
            F.sum("xvaIrEffect").alias("IR_P&L"),
            F.sum("xvaFactorLoadingEffectVega").cast("double").alias("Commodity_Vega_P&L"),
            F.lit(None).cast("double").alias("Gamma_Impact"),
            F.lit(None).cast("double").alias("Fixings_Impact"),
            F.lit(None).cast("double").alias("Theta_Impact"),
            F.lit(None).cast("double").alias("CDS_Cash_move"),
            (F.sum("xvaPnl") - F.sum("xvaUnexplainedEffect")).alias("Explained_P&L"),
            F.sum("xvaUnexplainedEffect").alias("Unexplained_P&L"),
        )
        app2_summaries.append(summary)

    app2_summary = reduce(lambda a, b: a.unionByName(b), app2_summaries)

# C. App 3  (instrument-level views, all use valuedate)
app3_sources = [
    ("XVA_M", "xvameodpnlexplainedpnl"),
    ("XVA_E", "xvaeeodpnlexplainedpnl"),
    ("XVA_LM", "xvalmeodpnlexplainedpnl"),
    ("XVA_LE", "xvaleeodpnlexplainedpnl"),
    ("XVA_FE", "xvafeeodpnlexplainedpnl"),
    ("XVA_FM", "xvafmeodpnlexplainedpnl"),
    
]

app3_summaries = []
app3_summary = None
for book, table_name in app3_sources:
    df = process_table(table_name)  # default valuedate
    if df:
        summary = df.groupBy().agg(
            F.max("valuedate").alias("Value_Date"),
            F.lit("MBCL").alias("MBCL_Legal_Entity"),
            F.lit(book).alias("XVA_Book"),
            F.sum("pL").alias("Total_P&L"),
            F.lit(None).cast("double").alias("CVA_Move"),
            F.lit(None).cast("double").alias("FVA_Move"),
            F.lit(None).cast("double").alias("New_Trade_P&L"),
            F.sum("impactofamendmentsbuyoutscancel").alias("Cancelled_Trade_P&L"),
            F.sum("impactOfAmendmentsBuyoutsCancel").alias("Amended_Trade_P&L"),
            F.sum("impactofdelta").alias("Credit_Delta_P&L"),
            F.sum("impactOfDelta").alias("Commodity_Delta_P&L"),
            F.sum("fxgainloss").alias("FX_P&L"),
            F.lit(None).cast("double").alias("IR_P&L"),
            F.sum("impactofvega").cast("double").alias("Commodity_Vega_P&L"),
            F.sum("impactofgamma").alias("Gamma_Impact"),
            F.sum("impactoffixings").cast("double").alias("Fixings_Impact"),
            F.sum("theta").alias("Theta_Impact"),
            F.lit(None).cast("double").alias("CDS_Cash_move"),
            F.sum("plexplained").alias("Explained_P&L"),
            F.sum("plvariance").alias("Unexplained_P&L"),
        )
        app3_summaries.append(summary)

if app3_summaries:
    app3_summary = reduce(lambda a, b: a.unionByName(b), app3_summaries)

all_summaries = [app1_summary, app2_summary, app3_summary]
valid_summaries = [df for df in all_summaries if df is not None]
if valid_summaries:
    # D. Bring everything together
    final_summary = reduce(lambda a, b: a.unionByName(b), valid_summaries)
    final_summary.write.format("delta").mode("overwrite").option("overwriteSchema", "true").saveAsTable(f"{gold_catalog}.{schema_name}.eod4")

