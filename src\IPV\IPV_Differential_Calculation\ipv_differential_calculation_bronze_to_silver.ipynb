{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "edff8ada-9ebe-47cb-82e9-a834e0640407", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["dbutils.widgets.text(\"ipv_silver_catalog\", \"mbcl_dev_silver\", \"ipv_silver_catalog\")\n", "dbutils.widgets.text(\"ipv_silver_schema\", \"ipv\", \"ipv_silver_schema\")\n", "\n", "dbutils.widgets.text(\"ipv_bronze_catalog\", \"mbcl_dev_bronze\", \"ipv_bronze_catalog\")\n", "dbutils.widgets.text(\"ipv_bronze_schema\", \"ipv\", \"ipv_bronze_schema\")\n", "\n", "dbutils.widgets.text(\"reference_gas_table\", \"bronze_reference_gas\", \"reference_gas_table\")\n", "dbutils.widgets.text(\"reference_power_table\", \"bronze_reference_power\", \"reference_power_table\")\n", "dbutils.widgets.text(\"reference_months_strips_table\", \"bronze_reference_months_strips\", \"reference_months_strips_table\")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "eabd1bc0-c7d7-4376-ac13-a3c3c7cd5e95", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["from pyspark.sql import functions as F"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "5c1e4825-81ba-4079-a598-3d129a093e4a", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["ipv_silver_catalog = dbutils.widgets.get(\"ipv_silver_catalog\")\n", "ipv_silver_schema = dbutils.widgets.get(\"ipv_silver_schema\")\n", "\n", "ipv_bronze_catalog = dbutils.widgets.get(\"ipv_bronze_catalog\")\n", "ipv_bronze_schema = dbutils.widgets.get(\"ipv_bronze_schema\")\n", "\n", "reference_gas_table_name = dbutils.widgets.get(\"reference_gas_table\")\n", "reference_power_table_name = dbutils.widgets.get(\"reference_power_table\")\n", "reference_months_strips_table_name = dbutils.widgets.get(\"reference_months_strips_table\")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "fa208275-1612-4717-9c4f-317135fb4d0a", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["AVAILABILITY_TABLE = spark.table(f\"{ipv_silver_catalog}.{ipv_silver_schema}.silver_availability\")\n", "COMMODITIES = ['gas', 'power']\n", "BATCH_PROCESSING_LIMIT = None"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "040867d6-ea4a-46f9-a69d-28fcee6e94cf", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["def add_code_column(df, is_gas):\n", "    if is_gas:\n", "        df = (\n", "            df.withCol<PERSON>n(\n", "                \"code_array\",\n", "                F.array(\n", "                    F.struct(F.col(\"ol_code\").alias(\"code\")),\n", "                    F.struct(F.col(\"gd_code\").alias(\"code\"))\n", "                )\n", "            ).withColumn(\n", "                \"code_struct\", <PERSON><PERSON>explode(\"code_array\")\n", "            ).withColumn(\n", "                \"code\", <PERSON>.col(\"code_struct.code\")\n", "            ).drop(\"ol_code\", \"gd_code\", \"code_array\", \"code_struct\")\n", "        )\n", "        df = df.filter(<PERSON><PERSON>col(\"code\").isNotNull())\n", "    else:\n", "        df = df.withColumnRenamed(\"ol_code\", \"code\")\n", "\n", "    return df"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "667c5151-4f71-48e5-ad2b-db875967e7d2", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["\n", "\n", "# for commodity in COMMODITIES:\n", "#     # compute the keys that are ready for joining (the intersection of settlements and local prices)\n", "#     # in order to avoid partial joins; run only when both sides have that key\n", "#     settlement_prices_ready = (\n", "#         AVAILABILITY_TABLE\n", "#         .where(\n", "#             (F.col(\"table_name\") == f\"{ipv_silver_catalog}.{ipv_silver_schema}.silver_settlement_prices_{commodity}\")\n", "#             & (F.col(\"commodity\") == commodity)\n", "#         )\n", "#         .select(\"code\", \"strip_date\", \"trade_date\")\n", "#         .dropDuplicates()\n", "#     )\n", "\n", "#     local_values_ready = (\n", "#         AVAILABILITY_TABLE\n", "#         .where(\n", "#             (F.col(\"table_name\") == f\"{ipv_silver_catalog}.{ipv_silver_schema}.silver_market_price_{commodity}\")\n", "#             & (F.col(\"commodity\") == commodity)\n", "#         )\n", "#         .select(\"code\", \"strip_date\", \"trade_date\")\n", "#         .dropDuplicates()\n", "#     )\n", "\n", "#     ready = settlement_prices_ready.join(local_values_ready, [\"code\", \"strip_date\", \"trade_date\"], \"inner\")\n", "\n", "#     # remove keys already processed (to make the join idempotent)\n", "#     processed = (\n", "#         spark.table(f\"{ipv_silver_catalog}.{ipv_silver_schema}.silver_ipv_difference_processed_keys\")\n", "#         .filter(F.col(\"commodity\") == commodity)\n", "#         .select(\"code\", \"strip_date\", \"trade_date\")\n", "#         .dropDuplicates()\n", "#     )\n", "\n", "#     unprocessed = ready.join(processed, [\"code\", \"strip_date\", \"trade_date\"], \"left_anti\")\n", "\n", "#     if BATCH_PROCESSING_LIMIT:\n", "#         unprocessed = unprocessed.limit(BATCH_PROCESSING_LIMIT)\n", "\n", "#     unprocessed.createOrReplaceTempView(f\"ready_unprocessed_keys_{commodity}\")\n", "    \n"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "ad3ef53c-c646-4625-9fcc-d097ee21ec1d", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["def get_ready_unprocessed_keys(availability_table, processed_table, catalog, schema, commodity, batch_limit=None):\n", "    # Compute keys ready for joining (intersection of settlements and local prices)\n", "    settlement_prices_ready = (\n", "        availability_table\n", "        .where(\n", "            (<PERSON>.col(\"table_name\") == f\"{catalog}.{schema}.silver_settlement_prices_{commodity}\")\n", "            & (F.col(\"commodity\") == commodity)\n", "        )\n", "        .select(\"code\", \"strip_date\", \"trade_date\")\n", "        .dropDuplicates()\n", "    )\n", "\n", "    local_values_ready = (\n", "        availability_table\n", "        .where(\n", "            (<PERSON>.col(\"table_name\") == f\"{catalog}.{schema}.silver_market_price_{commodity}\")\n", "            & (F.col(\"commodity\") == commodity)\n", "        )\n", "        .select(\"code\", \"strip_date\", \"trade_date\")\n", "        .dropDuplicates()\n", "    )\n", "\n", "    ready = settlement_prices_ready.join(local_values_ready, [\"code\", \"strip_date\", \"trade_date\"], \"inner\")\n", "    \n", "    processed = (\n", "        processed_table\n", "        .filter(F.col(\"commodity\") == commodity)\n", "        .select(\"commodity\", \"code\", \"strip_date\", \"trade_date\")\n", "        .dropDuplicates()\n", "    )\n", "    ready = ready.withColumn(\"commodity\", F.lit(commodity))\n", "\n", "    unprocessed = ready.join(processed, [\"code\", \"strip_date\", \"trade_date\"], \"left_anti\")\n", "\n", "    if batch_limit:\n", "        unprocessed = unprocessed.limit(batch_limit)\n", "    return unprocessed\n", "\n", "\n", "def prepare_silver_tables(spark, catalog, schema, commodity, is_gas, add_code_column):\n", "    local_values = spark.table(f\"{catalog}.{schema}.silver_market_price_{commodity}\")\n", "    local_values = (\n", "        local_values\n", "        .withColumnRenamed(\"ol_code\", \"code\")\n", "        .withColumnRenamed(\"contract_date\", \"strip_date\")\n", "    )\n", "    settlement_prices = spark.table(f\"{catalog}.{schema}.silver_settlement_prices_{commodity}\")\n", "    settlement_prices = add_code_column(settlement_prices, is_gas)\n", "    settlement_prices = (\n", "        settlement_prices\n", "        .drop(\"strip_date\")\n", "        .withColumnRenamed(\"strip_timestamp\", \"strip_date\")\n", "    )\n", "    return local_values, settlement_prices\n", "\n", "\n", "def process_and_write_ipv_diff(\n", "    spark, \n", "    local_values, \n", "    settlement_prices, \n", "    keys, \n", "    diff_final_columns, \n", "    catalog, \n", "    schema, \n", "    commodity\n", "):\n", "    settlement_prices_increment = (\n", "        settlement_prices\n", "        .join(keys, [\"code\", \"strip_date\", \"trade_date\"], \"inner\")\n", "    )\n", "    local_values_increment = (\n", "        local_values\n", "        .join(keys, [\"code\", \"strip_date\", \"trade_date\"], \"inner\")\n", "    )\n", "    joined_increment = (\n", "        settlement_prices_increment.alias(\"a\")\n", "        .join(local_values_increment.alias(\"b\"), [\"code\", \"strip_date\", \"trade_date\"], \"inner\")\n", "        .withColumn(\"load_ts\", F.current_timestamp())\n", "    )\n", "\n", "    joined_increment.display()\n", "    \n", "    ipv_diff_df = (\n", "        joined_increment\n", "        .select(*diff_final_columns)\n", "        .withColumn(\"ipv_settlement_and_local_value_diff\", <PERSON><PERSON>col(\"settlement_price\") - <PERSON>.col(\"mbcl_value\"))\n", "    )\n", "    ipv_diff_df.display()\n", "    ipv_diff_df.write.format(\"delta\").mode(\"append\").saveAsTable(f\"{catalog}.{schema}.silver_ipv_difference_{commodity}\")\n", "\n", "    # Mark keys as processed\n", "    (\n", "        keys\n", "        .withColumn(\"processed_ts\", F.current_timestamp())\n", "        .select(\"commodity\", \"code\", \"strip_date\", \"trade_date\", \"processed_ts\")\n", "        .write.format(\"delta\").mode(\"append\").saveAsTable(f\"{catalog}.{schema}.silver_ipv_difference_processed_keys\")\n", "    )"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "b57334c7-dd00-4962-899c-e6bfb7f218a9", "showTitle": false, "tableResultSettingsMap": {"0": {"dataGridStateBlob": "{\"version\":1,\"tableState\":{\"columnPinning\":{\"left\":[\"#row_number#\"],\"right\":[]},\"columnSizing\":{},\"columnVisibility\":{}},\"settings\":{\"columns\":{}},\"syncTimestamp\":1754664851569}", "filterBlob": null, "queryPlanFiltersBlob": null, "tableResultIndex": 0}, "1": {"dataGridStateBlob": "{\"version\":1,\"tableState\":{\"columnPinning\":{\"left\":[\"#row_number#\"],\"right\":[]},\"columnSizing\":{},\"columnVisibility\":{}},\"settings\":{\"columns\":{}},\"syncTimestamp\":1754665202340}", "filterBlob": null, "queryPlanFiltersBlob": null, "tableResultIndex": 1}}, "title": ""}}, "outputs": [], "source": ["# Main modularized flow\n", "for commodity in COMMODITIES:\n", "    # Step 1: Get ready and unprocessed keys\n", "    processed_table = spark.table(f\"{ipv_silver_catalog}.{ipv_silver_schema}.silver_ipv_difference_processed_keys\")\n", "    unprocessed = get_ready_unprocessed_keys(\n", "        AVAILABILITY_TABLE, \n", "        processed_table, \n", "        ipv_silver_catalog, \n", "        ipv_silver_schema, \n", "        commodity, \n", "        BATCH_PROCESSING_LIMIT\n", "    )\n", "    unprocessed.createOrReplaceTempView(f\"ready_unprocessed_keys_{commodity}\")\n", "\n", "    # Step 2: Prepare silver tables\n", "    is_gas = True if commodity == \"gas\" else False\n", "    local_values, settlement_prices = prepare_silver_tables(\n", "        spark, ipv_silver_catalog, ipv_silver_schema, commodity, is_gas, add_code_column\n", "    )\n", "\n", "    # Step 3: Read only relevant rows from silver\n", "    keys = spark.table(f\"ready_unprocessed_keys_{commodity}\")\n", "\n", "    # keys.display()\n", "\n", "    # Step 4: Process and write ipv difference\n", "    diff_final_columns = [\"code\", \"strip_date\", \"trade_date\", \"mbcl_value\", \"settlement_price\"]\n", "    process_and_write_ipv_diff(\n", "        spark, local_values, settlement_prices, keys, diff_final_columns, \n", "        ipv_silver_catalog, ipv_silver_schema, commodity\n", "    )"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "eef80e11-a474-4458-955e-ca2d70a68821", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["# Clean-up code for dev and debugging purposes"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "7a74c370-0281-43f0-8e43-a5265e47f547", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# spark.sql(f\"\"\"TRUNCATE TABLE {ipv_silver_catalog}.{ipv_silver_schema}.silver_ipv_difference_processed_keys\"\"\")\n", "# spark.sql(f\"\"\"TRUNCATE TABLE {ipv_silver_catalog}.{ipv_silver_schema}.silver_ipv_difference_gas\"\"\")\n", "# spark.sql(f\"\"\"TRUNCATE TABLE {ipv_silver_catalog}.{ipv_silver_schema}.silver_ipv_difference_power\"\"\")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "5588dcf9-d21c-474e-be77-57e6fee0d72e", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": []}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "inputWidgetPreferences": null, "language": "python", "notebookMetadata": {"pythonIndentUnit": 4}, "notebookName": "ipv_differential_calculation_bronze_to_silver", "widgets": {"ipv_bronze_catalog": {"currentValue": "mbcl_dev_bronze", "nuid": "2406ca07-c853-4b72-85f8-4c72af248bb7", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "mbcl_dev_bronze", "label": "ipv_bronze_catalog", "name": "ipv_bronze_catalog", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "mbcl_dev_bronze", "label": "ipv_bronze_catalog", "name": "ipv_bronze_catalog", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}, "ipv_bronze_schema": {"currentValue": "ipv", "nuid": "c5ee7d33-2430-40b9-a241-c2cd4ee9767d", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "ipv", "label": "ipv_bronze_schema", "name": "ipv_bronze_schema", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "ipv", "label": "ipv_bronze_schema", "name": "ipv_bronze_schema", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}, "ipv_silver_catalog": {"currentValue": "mbcl_dev_silver", "nuid": "704c8092-824f-4e9d-8921-82bd89f1a800", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "mbcl_dev_silver", "label": "ipv_silver_catalog", "name": "ipv_silver_catalog", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "mbcl_dev_silver", "label": "ipv_silver_catalog", "name": "ipv_silver_catalog", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}, "ipv_silver_schema": {"currentValue": "ipv", "nuid": "4da8ba56-1786-4ca8-9ce3-78036952d97a", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "ipv", "label": "ipv_silver_schema", "name": "ipv_silver_schema", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "ipv", "label": "ipv_silver_schema", "name": "ipv_silver_schema", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}, "reference_gas_table": {"currentValue": "bronze_reference_gas", "nuid": "72f18b00-7d0f-4d40-8346-fceffc55cfce", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "bronze_reference_gas", "label": "reference_gas_table", "name": "reference_gas_table", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "bronze_reference_gas", "label": "reference_gas_table", "name": "reference_gas_table", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}, "reference_months_strips_table": {"currentValue": "bronze_reference_months_strips", "nuid": "1551296a-98f5-49c5-a10f-0477e250a44e", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "bronze_reference_months_strips", "label": "reference_months_strips_table", "name": "reference_months_strips_table", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "bronze_reference_months_strips", "label": "reference_months_strips_table", "name": "reference_months_strips_table", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}, "reference_power_table": {"currentValue": "bronze_reference_power", "nuid": "2efae173-af11-49be-8757-eb018131403d", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "bronze_reference_power", "label": "reference_power_table", "name": "reference_power_table", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "bronze_reference_power", "label": "reference_power_table", "name": "reference_power_table", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}