dbutils.widgets.text("ipv_silver_catalog", "mbcl_silver", "ipv_silver_catalog")
dbutils.widgets.text("ipv_silver_schema", "mbcl_ipv", "ipv_silver_schema")

dbutils.widgets.text("ipv_bronze_catalog", "mbcl_bronze", "ipv_bronze_catalog")
dbutils.widgets.text("ipv_bronze_schema", "mbcl_ipv", "ipv_bronze_schema")

dbutils.widgets.text("reference_gas_table", "bronze_reference_gas", "reference_gas_table")
dbutils.widgets.text("reference_power_table", "bronze_reference_power", "reference_power_table")
dbutils.widgets.text("reference_months_strips_table", "bronze_reference_months_strips", "reference_months_strips_table")

from pyspark.sql import functions as F
from delta.tables import DeltaTable

ipv_silver_catalog = dbutils.widgets.get("ipv_silver_catalog")
ipv_silver_schema = dbutils.widgets.get("ipv_silver_schema")

ipv_bronze_catalog = dbutils.widgets.get("ipv_bronze_catalog")
ipv_bronze_schema = dbutils.widgets.get("ipv_bronze_schema")

reference_gas_table_name = dbutils.widgets.get("reference_gas_table")
reference_power_table_name = dbutils.widgets.get("reference_power_table")
reference_months_strips_table_name = dbutils.widgets.get("reference_months_strips_table")

SETTLEMENT_PRICES_POWER_PK = ['trade_date', 'hub', 'product', 'strip', 'contract']
SETTLEMENT_PRICES_GAS_PK = ['trade_date', 'hub', 'product', 'strip', 'contract']

BRONZE_SETTLEMENT_PRICES_POWER_INGESTION_TIMESTAMP_COLUMN = 'ingestion_timestamp'
BRONZE_SETTLEMENT_PRICES_GAS_INGESTION_TIMESTAMP_COLUMN = 'ingestion_timestamp'

SILVER_AVAILABILITY_TABLE = f"{ipv_silver_catalog}.{ipv_silver_schema}.silver_availability"

MONTH_MAP = {
    1: "Jan", 2: "Feb", 3: "Mar", 4: "Apr", 5: "May", 6: "Jun",
    7: "Jul", 8: "Aug", 9: "Sep", 10: "Oct", 11: "Nov", 12: "Dec"
}

def add_code_column(df, is_gas):
    if is_gas:
        df = (
            df.withColumn(
                "code_array",
                F.array(
                    F.struct(F.col("ol_code").alias("code")),
                    F.struct(F.col("gd_code").alias("code"))
                )
            ).withColumn(
                "code_struct", F.explode("code_array")
            ).withColumn(
                "code", F.col("code_struct.code")
            ).drop("ol_code", "gd_code", "code_array", "code_struct")
        )
        df = df.filter(F.col("code").isNotNull())
    else:
        df = df.withColumnRenamed("ol_code", "code")

    return df


def aggregate_to_availability_keys(silver_new, silver_table_name, is_gas=False):
    """
    This function aggregates the incremental silver table to
    the silver availability table.
    """
    silver_new = add_code_column(silver_new, is_gas)
    ingestion_timestamp_column = BRONZE_SETTLEMENT_PRICES_GAS_INGESTION_TIMESTAMP_COLUMN if is_gas else BRONZE_SETTLEMENT_PRICES_POWER_INGESTION_TIMESTAMP_COLUMN

    availability_keys = ["code", "trade_date", "strip_timestamp"]
    agg = (
        silver_new
        .groupBy(availability_keys)
        .agg(
            F.count(F.lit(1)).alias("records"),
            F.min(ingestion_timestamp_column).alias("first_arrival_ts"),
            F.max(ingestion_timestamp_column).alias("last_arrival_ts")
        )
        .withColumn("table_name", F.lit(silver_table_name))
        .withColumn("commodity", F.lit("gas") if is_gas else F.lit("power"))
    )

    return agg


def write_to_availability_table(silver_new, silver_table_name, is_gas):

    agg_availability = aggregate_to_availability_keys(silver_new, silver_table_name, is_gas)

    if not spark.catalog.tableExists(SILVER_AVAILABILITY_TABLE):
        agg_availability.write.format("delta").mode("overwrite").saveAsTable(SILVER_AVAILABILITY_TABLE)

    availability_dt = DeltaTable.forName(spark, SILVER_AVAILABILITY_TABLE)

    (
        availability_dt.alias("t")
        .merge(
            agg_availability.alias("s"),
            """
            t.table_name = s.table_name AND
            t.code = s.code AND
            t.trade_date = s.trade_date AND
            t.strip_date = s.strip_timestamp
            """
        )
        .whenMatchedUpdate(set={
            # add counts if same key arrives in multiple chunks
            "records": "coalesce(t.records, 0) + coalesce(s.records, 0)",
            "first_arrival_ts": "LEAST(t.first_arrival_ts, s.first_arrival_ts)",
            "last_arrival_ts": "GREATEST(t.last_arrival_ts, s.last_arrival_ts)"
        })
        .whenNotMatchedInsert(values={
            "table_name": "s.table_name",
            "commodity": "s.commodity",
            "code": "s.code",
            "trade_date": "s.trade_date",
            "strip_date": "s.strip_timestamp",
            "records": "s.records",
            "first_arrival_ts": "s.first_arrival_ts",
            "last_arrival_ts": "s.last_arrival_ts"
        })
        .execute()
    )

def process_incremental_bronze_to_silver(
    pipeline_name,
    bronze_table,
    bronze_primary_key,
    bronze_ingestion_timestamp_column,
    silver_table,
    reference_tables,
    is_gas
):
    """
    This function processes the incremental bronze table to silver table.
    """
    try:
        last_ts = spark.read.table(f"{ipv_silver_catalog}.{ipv_silver_schema}.silver_watermark") \
            .filter(F.col("pipeline_name") == pipeline_name) \
            .select("last_processed") \
            .collect()[0]["last_processed"]
    except:
        last_ts = None

    df_bronze = spark.read.table(bronze_table)
    
    if last_ts:
        df_bronze = df_bronze.filter(F.col(bronze_ingestion_timestamp_column) > last_ts)

    new_rows_count = df_bronze.count()
    if new_rows_count == 0:
        print("No new rows to process")
        return

    # drop duplicates for current data batch (duplicates might still be present from different ingestion times)
    df_bronze = df_bronze.dropDuplicates(bronze_primary_key)

    new_rows_count_deduplicated = df_bronze.count()
    if new_rows_count_deduplicated == 0:
        print("All rows were duplicates")
        return

    for reference_table, join_condition, join_type in reference_tables.values():
        df_bronze = df_bronze.join(
            reference_table, 
            join_condition, 
            join_type
        )

    new_rows_count_deduplicated_and_joined = df_bronze.count() 
    if new_rows_count_deduplicated_and_joined == 0:
        print("There was no matching row after join with our reference data")
        return
    
    df_bronze = df_bronze.withColumn(
        "trade_date",
        F.to_date(F.split(F.col("trade_date"), " ").getItem(0), "M/dd/yyyy")
    )
    
    if 'strip' in df_bronze.columns:
        # split (format: month/day/year) into 2 columns strip_month and strip_year
        df_bronze = df_bronze.withColumn("strip_month", F.split(F.col("strip"), "/")[0]) \
            .withColumn("strip_year", F.split(F.col("strip"), "/")[2])

        month_expr = F.create_map([F.lit(i) for pair in MONTH_MAP.items() for i in pair])

        df_bronze = df_bronze.withColumn(
            "strip_date",
            F.concat(
                month_expr[F.col("strip_month")], F.lit("-"), F.col("strip_year")
            )
        )

        df_bronze = df_bronze.withColumn(
            "strip_timestamp",
            F.to_date(
                F.concat(
                    F.col("strip_year"), F.lit("-"),
                    F.lpad(F.col("strip_month").cast("string"), 2, "0"),
                    F.lit("-01")
                ),
                "yyyy-MM-dd"
            )
        )

    write_to_availability_table(df_bronze, silver_table, is_gas)

    # create a pk based on the bronze_primary_key and a hash on the composite columns
    df_bronze = df_bronze.withColumn("primary_key", F.sha2(F.concat_ws("||", *bronze_primary_key), 256))

    # silver logic for power reference data transformations
    if pipeline_name == 'silver_settlement_prices_power':
        df_bronze = (
            df_bronze
                .withColumn("pk", F.col("pk").cast("boolean")) 
                .withColumn("op", F.col("op").cast("boolean")) 
                .withColumn("rt", F.col("rt").cast("boolean"))
                .withColumn("da", F.col("da").cast("boolean"))
        )
        df_bronze = (
            df_bronze
                .withColumn("is_peak", F.when(F.col("pk") == True, True).otherwise(False))
                .withColumn("is_rt", F.when(F.col("rt") == True, True).otherwise(False))
        )

    print(f"Initial total rows: {new_rows_count}. Writing {new_rows_count_deduplicated_and_joined} rows to silver; \
            {new_rows_count - new_rows_count_deduplicated} rows removed after deduplication; \
            {new_rows_count_deduplicated - new_rows_count_deduplicated_and_joined} rows removed after join; \
        ")
    df_bronze.write.mode("append").format("delta").saveAsTable(silver_table)

    new_last_ts = df_bronze.agg(F.max("ingestion_timestamp").alias("max_ts")).collect()[0]["max_ts"]

    watermark_update = spark.createDataFrame(
        [(pipeline_name, new_last_ts)],
        ["pipeline_name", "last_processed"]
    )

    (watermark_update
        .write
        .mode("overwrite")
        .option("replaceWhere", f"pipeline_name = '{pipeline_name}'")
        .saveAsTable(f"{ipv_silver_catalog}.{ipv_silver_schema}.silver_watermark")
    )




reference_gas = spark.read.table(f"{ipv_bronze_catalog}.{ipv_bronze_schema}.{reference_gas_table_name}")
reference_power = spark.read.table(f"{ipv_bronze_catalog}.{ipv_bronze_schema}.{reference_power_table_name}")

silver_pipeline_steps = [
    (
        "silver_settlement_prices_power",
        f"{ipv_bronze_catalog}.{ipv_bronze_schema}.bronze_settlement_prices_power",
        SETTLEMENT_PRICES_POWER_PK,
        BRONZE_SETTLEMENT_PRICES_POWER_INGESTION_TIMESTAMP_COLUMN,
        f"{ipv_silver_catalog}.{ipv_silver_schema}.silver_settlement_prices_power",
        {
            'reference_power': (
                F.broadcast(reference_power).withColumnRenamed('ingestion_timestamp', 'reference_power_ingestion_timestamp'), 
                F.col('contract') == F.col('ice_code'), 
                'inner'
            ),
        },
        False,
    ),
    (
        "silver_settlement_prices_gas",
        f"{ipv_bronze_catalog}.{ipv_bronze_schema}.bronze_settlement_prices_gas",
        SETTLEMENT_PRICES_GAS_PK,
        BRONZE_SETTLEMENT_PRICES_GAS_INGESTION_TIMESTAMP_COLUMN,
        f"{ipv_silver_catalog}.{ipv_silver_schema}.silver_settlement_prices_gas",
        {
            'reference_gas': (
                F.broadcast(reference_gas).withColumnRenamed('ingestion_timestamp', 'reference_gas_ingestion_timestamp'), 
                F.col('contract') == F.col('ice_code'),
                'inner'
            ),
        },
        True
    ),
]

for pipeline_name, bronze_table, bronze_pk, bronze_ingestion_time_column_name, silver_table, join_tables, is_gas  in silver_pipeline_steps:
    process_incremental_bronze_to_silver(
        pipeline_name,
        bronze_table,
        bronze_pk,
        bronze_ingestion_time_column_name,
        silver_table,
        join_tables,
        is_gas
    )

# names_tb_deleted = ['silver_settlement_prices_power', 'silver_settlement_prices_gas']
# names_str = ','.join([f"'{n}'" for n in names_tb_deleted])
# spark.sql(f"DELETE FROM {ipv_silver_catalog}.{ipv_silver_schema}.silver_watermark WHERE pipeline_name IN ({names_str})")

# name_tb_deleted2 = [f"{ipv_silver_catalog}.{ipv_silver_schema}.silver_settlement_prices_power", f"{ipv_silver_catalog}.{ipv_silver_schema}.silver_settlement_prices_gas"]
# names_str2 = ','.join([f"'{n}'" for n in name_tb_deleted2])
# spark.sql(f"DELETE FROM {ipv_silver_catalog}.{ipv_silver_schema}.silver_availability WHERE table_name IN ({names_str2})")

# tables = spark.sql("show tables in mbcl_dev_silver.ipv").collect()
# tables = [t for t in tables if t.tableName in names_tb_deleted]

# for t in tables:
#     table_name = t.tableName
#     print(f"Dropping table {table_name}")
#     spark.sql(f"DROP TABLE IF EXISTS mbcl_dev_silver.ipv.{table_name}")