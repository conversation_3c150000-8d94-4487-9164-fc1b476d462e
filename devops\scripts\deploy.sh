#!/bin/bash

# Databricks Generic Selective Deployment Script
# Usage: ./deploy.sh <environment> <use_case>
# Example: ./deploy.sh test MOS
# Example: ./deploy.sh prod all

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to check if databricks CLI is installed
check_databricks_cli() {
    if ! command -v databricks &> /dev/null; then
        print_message $RED "Error: Databricks CLI is not installed"
        print_message $YELLOW "Install it using: curl -fsSL https://raw.githubusercontent.com/databricks/setup-cli/main/install.sh | sh"
        exit 1
    fi
}

# Function to validate environment
validate_environment() {
    local env=$1
    case $env in
        dev|test|prod)
            return 0
            ;;
        *)
            print_message $RED "Error: Invalid environment '$env'"
            print_message $YELLOW "Valid environments: dev, test, prod"
            exit 1
            ;;
    esac
}

# Function to get all use cases from src directory
get_use_cases() {
    find ./src -maxdepth 1 -mindepth 1 -type d ! -name "shared" -exec basename {} \; | sort
}

# Function to validate use case
validate_use_case() {
    local use_case=$1
    if [ "$use_case" = "all" ]; then
        return 0
    fi
    local found=0
    for uc in $(get_use_cases); do
        if [ "$uc" = "$use_case" ]; then
            found=1
            break
        fi
    done
    if [ $found -eq 1 ]; then
        return 0
    else
        print_message $RED "Error: Invalid use case '$use_case'"
        print_message $YELLOW "Valid use cases: all, $(get_use_cases | tr '\n' ',' | sed 's/,$//')"
        exit 1
    fi
}

# Function to deploy shared folder
deploy_shared() {
    local env=$1
    local workspace_path="/Workspace/Deployments/${env}/files/src/shared"

    print_message $GREEN "\n📁 Deploying shared folder to ${env}..."

    databricks workspace mkdirs "$workspace_path" || true

    if [ -d "./src/shared" ]; then
        databricks workspace import-dir \
            ./src/shared \
            "$workspace_path" \
            --overwrite

        print_message $GREEN "✅ Shared folder deployed successfully"
        print_message $YELLOW "Deployed files:"
        databricks workspace ls "$workspace_path" 2>/dev/null || true
    else
        print_message $RED "⚠️  Warning: src/shared directory not found"
    fi
}

# Function to deploy a single use case
deploy_use_case() {
    local env=$1
    local use_case=$2
    local workspace_path="/Workspace/Deployments/${env}/files/src/${use_case}"

    print_message $GREEN "\n📁 Deploying ${use_case} to ${env}..."

    databricks workspace mkdirs "$workspace_path" || true

    if [ -d "./src/${use_case}" ]; then
        databricks workspace import-dir \
            "./src/${use_case}" \
            "$workspace_path" \
            --overwrite

        print_message $GREEN "✅ ${use_case} deployed successfully"
        print_message $YELLOW "Deployed files:"
        databricks workspace ls "$workspace_path" 2>/dev/null || true
    else
        print_message $RED "⚠️  Warning: src/${use_case} directory not found"
    fi
}

# Function to deploy all use cases
deploy_all_use_cases() {
    local env=$1

    print_message $GREEN "\n📦 Deploying ALL use cases to ${env}..."

    for uc in $(get_use_cases); do
        deploy_use_case "$env" "$uc"
    done
}

# Function to verify deployment
verify_deployment() {
    local env=$1
    local use_case=$2
    local workspace_root="/Workspace/Deployments/${env}/files/src"

    print_message $YELLOW "\n🔍 Verifying deployment..."

    print_message $YELLOW "Checking shared folder:"
    databricks workspace ls "${workspace_root}/shared" 2>/dev/null || \
        print_message $RED "Shared folder not found or empty"

    if [ "$use_case" = "all" ]; then
        for uc in $(get_use_cases); do
            print_message $YELLOW "Checking ${uc}:"
            databricks workspace ls "${workspace_root}/${uc}" 2>/dev/null || \
                print_message $RED "${uc} not found or empty"
        done
    else
        print_message $YELLOW "Checking ${use_case}:"
        databricks workspace ls "${workspace_root}/${use_case}" 2>/dev/null || \
            print_message $RED "${use_case} not found or empty"
    fi
}

# Main deployment function
main() {
    local env=$1
    local use_case=$2

    if [ $# -ne 2 ]; then
        print_message $RED "Error: Invalid number of arguments"
        print_message $YELLOW "Usage: $0 <environment> <use_case>"
        print_message $YELLOW "Example: $0 test MOS"
        print_message $YELLOW "Example: $0 prod all"
        exit 1
    fi

    check_databricks_cli
    validate_environment "$env"
    validate_use_case "$use_case"

    print_message $GREEN "=========================================="
    print_message $GREEN "🚀 Databricks Selective Deployment"
    print_message $GREEN "=========================================="
    print_message $YELLOW "Environment: ${env}"
    print_message $YELLOW "Use Case: ${use_case}"
    print_message $YELLOW "Timestamp: $(date '+%Y-%m-%d %H:%M:%S')"
    print_message $GREEN "=========================================="

    deploy_shared "$env"

    if [ "$use_case" = "all" ]; then
        deploy_all_use_cases "$env"
    else
        deploy_use_case "$env" "$use_case"
    fi

    verify_deployment "$env" "$use_case"

    print_message $GREEN "\n=========================================="
    print_message $GREEN "✅ Deployment completed successfully!"
    print_message $GREEN "=========================================="

    print_message $YELLOW "Workspace root: /Workspace/Deployments/${env}/"
    print_message $YELLOW "Deployed components:"
    print_message $YELLOW "  - shared folder (always deployed)"

    if [ "$use_case" = "all" ]; then
        for uc in $(get_use_cases); do
            print_message $YELLOW "  - ${uc}"
        done
    else
        print_message $YELLOW "  - ${use_case}"
    fi

    print_message $GREEN "=========================================="
}

main "$@"