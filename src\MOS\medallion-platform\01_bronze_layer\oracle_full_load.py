# Databricks notebook source
# MAGIC %md
# MAGIC # Oracle Full Load Ingestion
# MAGIC This notebook performs full load ingestion from Oracle source tables to Bronze layer

# COMMAND ----------

import json
from datetime import datetime
from pyspark.sql import SparkSession

# COMMAND ----------

# MAGIC %md
# MAGIC ## Setup Widgets for Parameter Passing

# COMMAND ----------

# Create widgets for workflow parameter passing
dbutils.widgets.text("environment", "dev", "Environment (dev/test/prod)")
dbutils.widgets.text("source_system", "MOS", "Source System")
dbutils.widgets.text("run_id", "", "Optional Run ID")
dbutils.widgets.text("catalog_name", "", "Catalog Name Override (optional)")

# Get parameters
environment = dbutils.widgets.get("environment")
source_system = dbutils.widgets.get("source_system")
run_id = dbutils.widgets.get("run_id") or f"bronze_full_load_{source_system}_{environment}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
catalog_override = dbutils.widgets.get("catalog_name")

print(f"🚀 Starting Bronze Full Load")
print(f"Environment: {environment}")
print(f"Source System: {source_system}")
print(f"Run ID: {run_id}")
print("="*60)

# COMMAND ----------

# MAGIC %md
# MAGIC ## Load Configuration Framework

# COMMAND ----------

# MAGIC %run ../06_utilities/config_manager

# COMMAND ----------

# MAGIC %run ./ingestion_framework

# COMMAND ----------

# MAGIC %md
# MAGIC ## Initialize Configuration

# COMMAND ----------

# Create configuration manager
config_manager = create_config_manager(environment, source_system, catalog_override)

# Validate configuration
if not config_manager.env_config_manager.validate_configuration():
    print("❌ Configuration validation failed!")
    dbutils.notebook.exit(json.dumps({"status": "config_error", "message": "Configuration validation failed"}))

# Get complete runtime configuration
runtime_config = config_manager.get_complete_runtime_config()
config = runtime_config

print(f"✅ Configuration loaded successfully!")
print(f"Bronze Catalog: {config['databricks']['catalogs']['bronze']}")
print(f"Utility Catalog: {config['databricks']['catalogs']['utility']}")
print(f"Oracle Host: {config['oracle_source']['host']}")
print(f"Database: {config['oracle_source']['database']}")

# Initialize the framework
bronze_framework = BronzeIngestionFramework(spark, config)

# COMMAND ----------

# MAGIC %md
# MAGIC ## Oracle Connection Setup

# COMMAND ----------

from pyspark.sql.types import *
import random

def get_oracle_jdbc_url():
    """Build Oracle JDBC URL"""
    oracle_config = config['oracle_source']
    return f"jdbc:oracle:thin:@//{oracle_config['host']}:{oracle_config['port']}/{oracle_config['database']}"

def read_oracle_table(table_name, oracle_schema=None):
    """Read entire table from Oracle database"""
    oracle_config = config['oracle_source']
    jdbc_url = get_oracle_jdbc_url()

    # Build fully qualified table name with schema prefix
    if oracle_schema:
        full_table_name = f"{oracle_schema}.{table_name}"
    else:
        full_table_name = table_name

    try:
        print(f"Reading full table: {full_table_name}")

        return spark.read \
            .format("jdbc") \
            .option("url", jdbc_url) \
            .option("dbtable", full_table_name) \
            .option("user", oracle_config['username']) \
            .option("password", oracle_config['password']) \
            .option("driver", "oracle.jdbc.driver.OracleDriver") \
            .option("fetchsize", "10000") \
            .load()
    except Exception as e:
        print(f"Error reading from Oracle: {str(e)}")
        if "ClassNotFoundException" in str(e):
            print("\n⚠️ Oracle JDBC driver not found!")
            print("Using sample data instead...")
            # Return sample data for testing
            return create_sample_data(table_name)
        raise e

def create_sample_data(table_name):
    """Create sample data when Oracle is not available"""
    
    
    if "customer" in table_name.lower():
        schema = StructType([
            StructField("customer_id", IntegerType(), False),
            StructField("customer_name", StringType(), True),
            StructField("email", StringType(), True),
            StructField("created_date", DateType(), True)
        ])
        data = [(i, f"Customer {i}", f"customer{i}@example.com", datetime.now().date()) 
                for i in range(1, 101)]
    elif "product" in table_name.lower():
        schema = StructType([
            StructField("product_id", IntegerType(), False),
            StructField("product_name", StringType(), True),
            StructField("price", DoubleType(), True)
        ])
        data = [(i, f"Product {i}", round(random.uniform(10, 500), 2)) 
                for i in range(1, 51)]
    else:
        schema = StructType([
            StructField("id", IntegerType(), False),
            StructField("data", StringType(), True)
        ])
        data = [(i, f"Data {i}") for i in range(1, 21)]
    
    return spark.createDataFrame(data, schema)

# COMMAND ----------

# MAGIC %md
# MAGIC ## Process Full Load Tables

# COMMAND ----------

# Get all tables configured for full load
utility_catalog = config['databricks']['catalogs']['utility']
metadata_schema = config['databricks']['schemas']['metadata']

try:
    full_load_tables = spark.sql(f"""
        SELECT * FROM `{utility_catalog}`.`{metadata_schema}`.pipeline_config
        WHERE ingestion_type = 'full'
        AND is_active = true
    """).collect()
    
    print(f"Found {len(full_load_tables)} tables for full load processing")
except:
    print("\n⚠️ Pipeline config table not found. Using default configuration.")
    # Default configuration for testing
    full_load_tables = [
        type('obj', (object,), {
            'pipeline_id': 'customers_full',
            'source_table': 'CUSTOMERS',
            'target_table': 'customers',
            'partition_columns': ['_ingestion_date']
        })(),
        type('obj', (object,), {
            'pipeline_id': 'products_full',
            'source_table': 'PRODUCTS',
            'target_table': 'products',
            'partition_columns': ['_ingestion_date']
        })()
    ]

# COMMAND ----------

# Helper function to convert Row to dict
def row_to_dict(row):
    """Convert Row to dictionary"""
    if hasattr(row, 'asDict'):
        return row.asDict()
    else:
        # For mock objects
        return {attr: getattr(row, attr) for attr in dir(row) 
                if not attr.startswith('_')}

# COMMAND ----------

# Process each full load table
results = []

for table_config in full_load_tables:
    print(f"\n{'='*60}")
    print(f"Processing: {table_config.source_table}")
    print(f"{'='*60}")
    
    try:

        # Read from Oracle (or use sample data) with schema prefix
        oracle_schema = getattr(table_config, 'oracle_schema', None)
        source_df = read_oracle_table(table_config.source_table, oracle_schema)
        
        # Show source info
        record_count = source_df.count()
        print(f"Source records: {record_count}")
        
        if record_count > 0:
            print("\nSource DataFrame Schema:")
            source_df.printSchema()
        
        # Convert config to dict
        config_dict = row_to_dict(table_config)
        
        # Execute full load
        result = bronze_framework.execute_full_load(source_df, config_dict)
        
        results.append({
            "table": table_config.source_table,
            "status": "success",
            "records": result.get('records_written', 0),
            "run_id": result.get('run_id', 'N/A')
        })
        
        print(f"\n✅ Successfully loaded {result.get('records_written', 0)} records")
        
    except Exception as e:
        results.append({
            "table": table_config.source_table,
            "status": "failed",
            "error": str(e)
        })
        print(f"\n❌ Error: {str(e)}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Summary Report

# COMMAND ----------

# Display results
from pyspark.sql import Row
if results:
    results_df = spark.createDataFrame([Row(**r) for r in results])
    display(results_df)
else:
    print("No tables were processed.")

# COMMAND ----------

# Show pipeline statistics
print("\nPipeline Statistics:")
for table_config in full_load_tables:
    try:
        stats = bronze_framework.get_pipeline_statistics(table_config.pipeline_id)
        if stats:
            print(f"\n{table_config.source_table}:")
            print(f"  Total runs: {stats.get('total_runs', 0)}")
            print(f"  Successful: {stats.get('successful_runs', 0)}")
            print(f"  Failed: {stats.get('failed_runs', 0)}")
    except:
        pass

# COMMAND ----------

# Verify bronze tables
bronze_catalog = config['databricks']['catalogs']['bronze']
bronze_schema = config['databricks']['schemas']['bronze']
print("\nBronze Tables Created:")
try:
    bronze_tables = spark.sql(f"SHOW TABLES IN `{bronze_catalog}`.`{bronze_schema}`")
    bronze_tables.show()
except:
    print("Could not list bronze tables")

# COMMAND ----------

if results:
    success_count = sum(1 for r in results if r['status'] == 'success')
    print(f"\n✅ Full load ingestion completed! Processed {len(results)} tables ({success_count} successful).")
    dbutils.notebook.exit(json.dumps(results))
else:
    print("\n⚠️ No tables found for full load processing.")
    dbutils.notebook.exit(json.dumps({"status": "no_tables_found"}))
