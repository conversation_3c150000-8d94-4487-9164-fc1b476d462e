{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "5f18d1cf-5d8b-4ebd-be37-f33e985c14c0", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# --- Widgets ---\n", "dbutils.widgets.text(\"env\", \"\")\n", "dbutils.widgets.text(\"entity\", \"\")\n", "\n", "env = dbutils.widgets.get(\"env\").lower()\n", "entity = dbutils.widgets.get(\"entity\").lower()"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "98f7849a-0f76-4873-aee4-2314efd5e28b", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["from pyspark.sql import SparkSession, DataFrame\n", "from pyspark.sql.functions import date_format"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "cef31763-108f-4a8f-a100-8dc66e388cfc", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["ipv_gold_catalog = f\"mbcl_{env}_gold\"\n", "ipv_silver_catalog = f\"mbcl_{env}_silver\""]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "9120913d-ad9c-445e-b595-283da1159bde", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["spark.sql(f\"\"\"\n", "CREATE OR REPLACE VIEW `{ipv_gold_catalog}`.`{entity}`.gas_market_price AS\n", "SELECT \n", "  primary_key,\n", "  to_date(split(trade_date, ' ')[0], 'yyyy-MM-dd') AS trade_date,\n", "  location_hub as location,\n", "  region,\n", "  ice_code,\n", "  CAST(mbcl_value AS decimal(38, 4)) as value,\n", "  CONCAT(\n", "    CASE month\n", "      WHEN 1 THEN 'Jan'\n", "      WHEN 2 THEN 'Feb'\n", "      WHEN 3 THEN 'Mar'\n", "      WHEN 4 THEN 'Apr'\n", "      WHEN 5 THEN 'May'\n", "      WHEN 6 THEN 'Jun'\n", "      WHEN 7 THEN 'Jul'\n", "      WHEN 8 THEN 'Aug'\n", "      WHEN 9 THEN 'Sep'\n", "      WHEN 10 THEN 'Oct'\n", "      WHEN 11 THEN 'Nov'\n", "      WHEN 12 THEN 'Dec'\n", "    END, \n", "    '-', \n", "    year\n", "  ) as strip_date,\n", "  contract_date as strip_timestamp,\n", "  ol_code as index_name\n", "FROM `{ipv_silver_catalog}`.`{entity}`.silver_market_price_gas\n", "\"\"\")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "dd0d515a-b14a-4730-8627-5a023f888c5d", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["spark.sql(f\"\"\"\n", "CREATE OR REPLACE VIEW `{ipv_gold_catalog}`.`{entity}`.power_market_price AS\n", "SELECT \n", "  primary_key,\n", "  to_date(split(trade_date, ' ')[0], 'yyyy-MM-dd') AS trade_date,\n", "  location_hub AS location,\n", "  region,\n", "  zone,\n", "  pk AS is_peak,\n", "  op AS is_off_peak,\n", "  rt AS is_real_time,\n", "  da AS is_day_ahead,\n", "  ice_code,\n", "  CAST(mbcl_value AS decimal(38, 2)) as value,\n", "  CONCAT(\n", "    CASE month\n", "      WHEN 1 THEN 'Jan'\n", "      WHEN 2 THEN 'Feb'\n", "      WHEN 3 THEN 'Mar'\n", "      WHEN 4 THEN 'Apr'\n", "      WHEN 5 THEN 'May'\n", "      WHEN 6 THEN 'Jun'\n", "      WHEN 7 THEN 'Jul'\n", "      WHEN 8 THEN 'Aug'\n", "      WHEN 9 THEN 'Sep'\n", "      WHEN 10 THEN 'Oct'\n", "      WHEN 11 THEN 'Nov'\n", "      WHEN 12 THEN 'Dec'\n", "    END, \n", "    '-', \n", "    year\n", "  ) as strip_date,\n", "  contract_date as strip_timestamp,\n", "  ol_code as index_name\n", "  FROM `{ipv_silver_catalog}`.`{entity}`.silver_market_price_power\n", "\"\"\")"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "inputWidgetPreferences": null, "language": "python", "notebookMetadata": {"pythonIndentUnit": 4}, "notebookName": "trader_mark_silver_to_gold", "widgets": {"entity": {"currentValue": "ipv", "nuid": "51ccf25c-2e78-41c9-810d-e553b876ad90", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "", "label": null, "name": "entity", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "", "label": null, "name": "entity", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}, "env": {"currentValue": "dev", "nuid": "1b0d1c09-edba-44e9-a69c-ee7cf31fec38", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "", "label": null, "name": "env", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "", "label": null, "name": "env", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}