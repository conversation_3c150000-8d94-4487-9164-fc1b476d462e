{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "e88c6e21-7301-4398-be2d-6734d10197bc", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["trans_delta_ol_codes = '''<delta_ol_codes_from_trans_sheet>'''.split('\\t')\n", "\n", "trans_ol_codes = '''<ice_ol_codes_from_trans_sheet>'''.split('\\t')\n", "\n", "trans_delta_ol_codes = list([x.strip().lower().replace('\"', '') for x in trans_delta_ol_codes if x.strip()])\n", "trans_ol_codes = list([x.strip().lower().replace('\"', '') for x in trans_ol_codes if x.strip()])\n", "\n", "trans_mapping = list(zip(trans_delta_ol_codes, trans_ol_codes))\n", "\n", "ol_codes = '''<ice_ol_codes_from_reference_data>'''.split('\\n')\n", "\n", "ol_codes = list([x.strip().lower().replace('\"', '') for x in ol_codes if x.strip()])\n", "\n", "import pandas as pd\n", "pd_df_map = pd.DataFrame({\n", "    'index_name': [],\n", "    'ol_index': []\n", "})\n", "\n", "for ref_name in ol_codes:\n", "    aux_res = []\n", "    for x1, x2 in trans_mapping:\n", "        if ref_name == x2:\n", "            aux_res.append(x1)\n", "            pd_df_map = pd_df_map.append({'index_name': ref_name, 'ol_index': x1}, ignore_index=True)\n", "\n", "pd_df_map['index_name'] = pd_df_map['index_name'].str.upper()\n", "\n", "pd_df_ref_power = spark.table(\"mbcl_dev_bronze.ipv.bronze_reference_power\").toPandas()"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "832b822a-10f6-4a79-8405-73936a251c3e", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["joined_df = pd.merge(pd_df_ref_power, pd_df_map, left_on=\"ol_code\", right_on=\"index_name\", how='inner')"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "7e4c4d25-cb36-48a9-99df-29951ccf4b07", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["just_the_mapping_df = joined_df[['ol_code', 'ol_index']].sort_values(by=['ol_code'])\n", "\n", "code_to_filter_by = None\n", "if code_to_filter_by:\n", "    just_the_mapping_df = just_the_mapping_df[just_the_mapping_df['ol_code'] == code_to_filter_by]\n", "\n", "with pd.option_context('display.max_rows', None, 'display.max_columns', None):  # more options can be specified also\n", "    display(just_the_mapping_df)"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "46c60cae-1845-46ab-99f8-7790dafb87b8", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["spark.createDataFrame(just_the_mapping_df).write.mode(\"overwrite\").saveAsTable(\"mbcl_dev_bronze.ipv.bronze_reference_power_ol_code_to_ol_delta_index\")"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": {"hardware": {"accelerator": null, "gpuPoolId": null, "memory": null}}, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "inputWidgetPreferences": null, "language": "python", "notebookMetadata": {"pythonIndentUnit": 4}, "notebookName": "aux_notebook_create_ol_index_mappings", "widgets": {}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}