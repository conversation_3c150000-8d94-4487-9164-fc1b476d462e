resources:
  jobs:
    XVA_Loader:
      name: XVA_Loader
      email_notifications:
        on_failure:
          - <EMAIL>
      tasks:
        - task_key: Load_Inbound_Files
          notebook_task:
            notebook_path: /Workspace/Shared/mbcl-databricks/notebooks/Inbound_Files
            base_parameters:
              entity: XVA
              env: dev
            source: WORKSPACE
        - task_key: Orchestrate_Bronze_Silver
          depends_on:
            - task_key: Load_Inbound_Files
          notebook_task:
            notebook_path: /Workspace/Shared/mbcl-databricks/notebooks/PC With FileLoader
            base_parameters:
              entity: XVA
              env: dev
            source: WORKSPACE
        - task_key: Load_Gold_Tables_Consolidated_Report
          depends_on:
            - task_key: Orchestrate_Bronze_Silver
          notebook_task:
            notebook_path: /Workspace/Shared/mbcl-databricks/notebooks/xvaConsolidatedReports
            base_parameters:
              entity: XVA
              env: dev
            source: WORKSPACE
        - task_key: Load_Gold_Tables_Input_Report
          depends_on:
            - task_key: Orchestrate_Bronze_Silver
          notebook_task:
            notebook_path: /Workspace/Shared/mbcl-databricks/notebooks/xvaCuratedDataset_InputReport
            base_parameters:
              entity: XVA
              env: dev
            source: WORKSPACE
        - task_key: Load_Gold_Tables_MTM_Rec
          depends_on:
            - task_key: Orchestrate_Bronze_Silver
          notebook_task:
            notebook_path: /Workspace/Shared/mbcl-databricks/notebooks/xvaCuratedDataset_MTMReconciliation
            base_parameters:
              entity: XVA
              env: dev
            source: WORKSPACE
        - task_key: Load_Gold_Tables_Missing_Trade_Reports
          depends_on:
            - task_key: Orchestrate_Bronze_Silver
          notebook_task:
            notebook_path: /Workspace/Shared/mbcl-databricks/notebooks/xvaCuratedDataset_MissingTradesReport
            base_parameters:
              entity: XVA
              env: dev
            source: WORKSPACE
      queue:
        enabled: true
      performance_target: PERFORMANCE_OPTIMIZED
