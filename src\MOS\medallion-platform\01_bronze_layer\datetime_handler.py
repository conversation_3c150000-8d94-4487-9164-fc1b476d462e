# Databricks notebook source
# MAGIC %md
# MAGIC # DateTime Handler for Bronze Layer
# MAGIC Comprehensive datetime parsing for various formats

# COMMAND ----------

from datetime import datetime
import re
from typing import Optional, Union

class DateTimeHandler:
    """Handle various datetime formats for Bronze layer watermark processing"""
    
    # Common datetime formats in order of preference
    DATETIME_FORMATS = [
        # ISO formats
        '%Y-%m-%dT%H:%M:%S.%fZ',      # 2024-01-15T14:30:45.123456Z
        '%Y-%m-%dT%H:%M:%S.%f',       # 2024-01-15T14:30:45.123456
        '%Y-%m-%dT%H:%M:%SZ',         # 2024-01-15T14:30:45Z
        '%Y-%m-%dT%H:%M:%S',          # 2024-01-15T14:30:45
        '%Y-%m-%d %H:%M:%S.%f',       # 2024-01-15 14:30:45.123456
        '%Y-%m-%d %H:%M:%S',          # 2024-01-15 14:30:45
        
        # Oracle formats
        '%d-%b-%Y %H.%M.%S.%f %p',    # 15-JAN-2024 02.30.45.123456 PM
        '%d-%b-%Y %H.%M.%S %p',       # 15-JAN-2024 02.30.45 PM
        '%d-%b-%y %H:%M:%S',          # 15-JAN-24 14:30:45
        '%Y/%m/%d %H:%M:%S',          # 2024/01/15 14:30:45
        
        # US formats
        '%m/%d/%Y %H:%M:%S',          # 01/15/2024 14:30:45
        '%m/%d/%Y %I:%M:%S %p',       # 01/15/2024 02:30:45 PM
        '%m-%d-%Y %H:%M:%S',          # 01-15-2024 14:30:45
        '%m-%d-%Y %I:%M:%S %p',       # 01-15-2024 02:30:45 PM
        
        # European formats
        '%d/%m/%Y %H:%M:%S',          # 15/01/2024 14:30:45
        '%d.%m.%Y %H:%M:%S',          # 15.01.2024 14:30:45
        '%d-%m-%Y %H:%M:%S',          # 15-01-2024 14:30:45
        
        # Date only formats (assume start of day)
        '%Y-%m-%d',                    # 2024-01-15
        '%Y/%m/%d',                    # 2024/01/15
        '%m/%d/%Y',                    # 01/15/2024
        '%d/%m/%Y',                    # 15/01/2024
        '%d-%b-%Y',                    # 15-JAN-2024
        '%d-%b-%y',                    # 15-JAN-24
        
        # Compact formats
        '%Y%m%d%H%M%S',               # 20240115143045
        '%Y%m%d',                      # 20240115
        
        # Unix timestamp (special handling)
        'UNIX_TIMESTAMP',              # 1705329045
        'UNIX_TIMESTAMP_MS'            # 1705329045123
    ]
    
    @staticmethod
    def parse_datetime(value: Union[str, int, float, datetime]) -> Optional[datetime]:
        """
        Parse various datetime formats into Python datetime object
        
        Args:
            value: Date/time value in various formats
            
        Returns:
            datetime object or None if parsing fails
        """
        # If already datetime, return as is
        if isinstance(value, datetime):
            return value
            
        # Handle None/empty
        if value is None or (isinstance(value, str) and not value.strip()):
            return None
            
        # Convert to string for parsing
        str_value = str(value).strip()
        
        # Check for Unix timestamp (all digits, 10 or 13 chars)
        if str_value.isdigit():
            try:
                if len(str_value) == 10:
                    # Unix timestamp in seconds
                    return datetime.fromtimestamp(int(str_value))
                elif len(str_value) == 13:
                    # Unix timestamp in milliseconds
                    return datetime.fromtimestamp(int(str_value) / 1000)
            except:
                pass
        
        # Try parsing with each format
        for fmt in DateTimeHandler.DATETIME_FORMATS:
            if fmt.startswith('UNIX_'):
                continue
                
            try:
                return datetime.strptime(str_value, fmt)
            except ValueError:
                continue
                
        # Special handling for Oracle TIMESTAMP WITH TIME ZONE
        # Example: 2024-01-15 14:30:45.123456 -05:00
        tz_pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})(?:\.(\d+))?\s*([+-]\d{2}:\d{2})'
        match = re.match(tz_pattern, str_value)
        if match:
            dt_str = match.group(1)
            try:
                return datetime.strptime(dt_str, '%Y-%m-%d %H:%M:%S')
            except:
                pass
                
        return None
    
    @staticmethod
    def format_for_oracle(dt: datetime) -> str:
        """
        Format datetime for Oracle queries (without microseconds)
        
        Args:
            dt: datetime object
            
        Returns:
            Formatted string for Oracle
        """
        if dt is None:
            return None
        return dt.strftime('%Y-%m-%d %H:%M:%S')
    
    @staticmethod
    def format_for_spark(dt: datetime) -> str:
        """
        Format datetime for Spark operations
        
        Args:
            dt: datetime object
            
        Returns:
            ISO formatted string
        """
        if dt is None:
            return None
        return dt.isoformat()
    
    @staticmethod
    def get_safe_watermark(value: Union[str, int, float, datetime]) -> Optional[str]:
        """
        Parse datetime and return Oracle-safe watermark string
        
        Args:
            value: Date/time value in various formats
            
        Returns:
            Oracle-formatted datetime string or None
        """
        dt = DateTimeHandler.parse_datetime(value)
        if dt:
            return DateTimeHandler.format_for_oracle(dt)
        return None

# COMMAND ----------

# Export the handler
DateTimeHandler = DateTimeHandler
