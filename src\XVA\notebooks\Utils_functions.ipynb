{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "7e6e5754-b169-455c-a8e1-9e1a1dfafc19", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["from pyspark.sql import DataFrame\n", "from pyspark.sql.functions import col\n", "\n", "import re\n", "import pandas as pd\n", "from dateutil import parser\n", "\n", "# Extended regex for ISO + relaxed date formats (YYYY-MM-DD or YYYY/MM/DD)\n", "iso_relaxed_pattern = re.compile(r\"\"\"\n", "    \\b\n", "    \\d{4}[-/]                             # YYYY-\n", "    \\d{2}[-/]                             # MM-\n", "    \\d{2}                                 # DD\n", "    (\n", "        [ T]                              # Separator: T or space\n", "        \\d{2}:\\d{2}:\\d{2}                 # HH:mm:ss\n", "        (\n", "            \\.\\d{1,6}                     # Optional .SSS or microseconds\n", "        )?\n", "        (\n", "            Z|[\\+\\-]\\d{2}:\\d{2}           # Optional timezone Z or ±HH:mm\n", "        )?\n", "    )?\n", "    \\b\n", "\"\"\", re.VERBOSE)\n", "\n", "numeric_double_reggex = r'^-?\\d+(\\.\\d+)?([eE][-+]?\\d+)?$'\n", "\n", "def infer_nullable_column_type(series: pd.Series):\n", "    \"\"\"\n", "    Infers the most appropriate data type for a nullable pandas Series.\n", "\n", "    Returns:\n", "        inferred_dtype (str): 'int', 'float', 'bool', 'datetime', or 'string'\n", "        converted_series (pd.Series): series with inferred type applied\n", "    \"\"\"\n", "    if series.empty:\n", "        return 'empty', series.astype('string')\n", " \n", "    series = series.replace(r'^\\s*$', pd.NA, regex=True)\n", "\n", "    #check for empty strings and None values\n", "    series = series.dropna().astype(str).str.strip()\n", "\n", "    # Try to infer datetime\n", "    if series.apply(lambda x: iso_relaxed_pattern.match(x)).any():\n", "        try:\n", "            coverted = pd.to_datetime(series, errors='raise', utc=False)\n", "            if coverted.notna().any():\n", "                return 'datetime', coverted\n", "        except (ValueErro<PERSON>, TypeError):\n", "            pass\n", "\n", "    # Try numeric inference\n", "    try:\n", "        numeric = pd.to_numeric(series, errors='raise')\n", "        if numeric.notna().any():\n", "            if (numeric.dropna() == numeric.dropna().astype(int)).all():\n", "                return 'int', numeric.astype('Int64')  # Nullable integer\n", "            else:\n", "                return 'float', numeric.astype('Float64')  # Nullable float\n", "    except (ValueErro<PERSON>, TypeError):\n", "        pass\n", "\n", "    # Try to infer boolean\n", "    unique_vals = set(series.dropna().astype(str).str.lower())\n", "    if unique_vals in ['true', 'false']:\n", "        return 'bool', series.astype('bool')\n", "\n", " \n", "    # Fallback to string\n", "    return 'string', series.astype('string')\n", "\n", "pandas_to_spark = {\n", "    'Int64': 'int',\n", "    'Float64': 'double',\n", "    'bool': 'boolean',\n", "    'datetime64[ns]': 'timestamp',\n", "    'string': 'string',\n", "    'empty': 'string'\n", "}    \n", "\n", "def cast_to_inferred_type(sparf_df: DataFrame):\n", "    data_type_mapping = dict()\n", "\n", "    # determine column type\n", "    sample_pd = sparf_df.limit(1000).toPandas()\n", "\n", "    for column in sample_pd.columns:\n", "        column_type, sample_pd[column] = infer_nullable_column_type(sample_pd[column])\n", "        data_type_mapping[column] = sample_pd[column].dtype.name\n", "\n", "    # cast the columns to the correct data type\n", "    for column in sparf_df.columns:\n", "        escaped_col = f\"`{column}`\" if \".\" in column else column\n", "        new_data_type = pandas_to_spark[data_type_mapping[column]]\n", "\n", "        if new_data_type == 'int' or new_data_type == 'double':\n", "            non_numeric_count = sparf_df.filter(~col(escaped_col).rlike(numeric_double_reggex)).limit(1).count()\n", "            if non_numeric_count > 0:\n", "                new_data_type = 'string'\n", "\n", "            elif new_data_type == 'int':\n", "                float_found = sparf_df.filter(col(escaped_col).contains('.')).count() > 0\n", "                if float_found:\n", "                    new_data_type = 'double'\n", "        \n", "        sparf_df = sparf_df.withColumn(column, col(escaped_col).cast(new_data_type))\n", "    \n", "    return sparf_df\n", "\n"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "bc83ef64-5e79-4c9e-b10f-39c54abe6825", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["def tables_exists(full_table_name_list:list) -> bool:\n", "    for table in full_table_name_list:\n", "        if spark.catalog.tableExists(table) == False:\n", "            return False\n", "    return True"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "inputWidgetPreferences": null, "language": "python", "notebookMetadata": {"pythonIndentUnit": 4}, "notebookName": "Utils_functions", "widgets": {}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}