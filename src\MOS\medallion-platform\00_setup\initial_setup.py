# Databricks notebook source
# MAGIC %md
# MAGIC # Initial Setup
# MAGIC One-time setup for the medallion architecture with layer-specific catalogs

# COMMAND ----------

import json

# COMMAND ----------

# MAGIC %md
# MAGIC ## Setup Widgets for Parameter Passing

# COMMAND ----------

# Create widgets for workflow parameter passing
dbutils.widgets.text("environment", "dev", "Environment (dev/test/prod)")
dbutils.widgets.text("source_system", "MOS", "Source System (MOS/XVA)")

# Get parameters
environment = dbutils.widgets.get("environment")
source_system = dbutils.widgets.get("source_system")

# Define layer-specific catalogs based on environment
catalog_mapping = {
    "dev": {
        "bronze": "mbcl_dev_bronze",
        "silver": "mbcl_dev_silver",
        "gold": "mbcl_dev_gold",
        "utility": "mbcl_dev_utility"
    },
    "test": {
        "bronze": "mbcl_test_bronze",
        "silver": "mbcl_test_silver",
        "gold": "mbcl_test_gold",
        "utility": "mbcl_test_utility"
    },
    "prod": {
        "bronze": "mbcl_prod_bronze",
        "silver": "mbcl_prod_silver",
        "gold": "mbcl_prod_gold",
        "utility": "mbcl_prod_utility"
    }
}

catalogs = catalog_mapping.get(environment, catalog_mapping["dev"])
source_schema = source_system.lower()  # e.g., "mos" or "xva"

print(f"🚀 Starting medallion architecture setup...")
print(f"Environment: {environment}")
print(f"Source System: {source_system}")
print(f"Bronze Catalog: {catalogs['bronze']}")
print(f"Silver Catalog: {catalogs['silver']}")
print(f"Gold Catalog: {catalogs['gold']}")
print(f"Utility Catalog: {catalogs['utility']}")
print(f"Source Schema: {source_schema}")
print("="*60)

# COMMAND ----------

# Note: External locations and storage credentials should be configured
# by your Databricks administrator before running this setup.
# This script will create catalogs using the default managed storage.

print("Creating layer-specific catalogs using managed storage...")

# COMMAND ----------

# Create all layer-specific catalogs using managed storage
for layer, catalog_name in catalogs.items():
    print(f"Creating {layer} catalog: {catalog_name}...")

    # Create catalog without specifying managed location (uses default)
    sql_query = f"""CREATE CATALOG IF NOT EXISTS `{catalog_name}`"""

    try:
        spark.sql(sql_query)
        print(f"✅ {layer.title()} catalog created: {catalog_name}")
    except Exception as e:
        print(f"⚠️ {layer.title()} catalog may already exist: {catalog_name}")
        print(f"   Error: {str(e)}")

print("\n✅ All catalogs created successfully!")

# COMMAND ----------

# Create schemas in their respective catalogs
print("\n🏗️ Creating schemas in layer-specific catalogs...")

# Schema configurations: (catalog_key, schema_name, description)
schema_configs = [
    ("bronze", source_schema, f"Bronze layer data for {source_system}"),
    ("silver", source_schema, f"Silver layer data for {source_system}"),
    ("gold", source_schema, f"Gold layer data for {source_system}"),
    ("utility", "metadata", "Metadata and configuration tables")
]

for catalog_key, schema_name, description in schema_configs:
    catalog_name = catalogs[catalog_key]

    print(f"Creating schema: {catalog_name}.{schema_name}")

    try:
        spark.sql(f"""CREATE SCHEMA IF NOT EXISTS `{catalog_name}`.`{schema_name}`
                     COMMENT '{description}'""")
        print(f"✅ Schema created: {catalog_name}.{schema_name}")
    except Exception as e:
        print(f"⚠️ Schema may already exist: {catalog_name}.{schema_name}")
        print(f"   Error: {str(e)}")

print("\n✅ All schemas created successfully!")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Next Steps
# MAGIC
# MAGIC The initial setup is complete! Your medallion architecture now has:
# MAGIC
# MAGIC ### Layer-Specific Catalogs:
# MAGIC - **Bronze**: `{catalogs['bronze']}.{source_schema}` - Raw data ingestion
# MAGIC - **Silver**: `{catalogs['silver']}.{source_schema}` - Cleaned and transformed data
# MAGIC - **Gold**: `{catalogs['gold']}.{source_schema}` - Business-ready analytics data
# MAGIC - **Utility**: `{catalogs['utility']}.metadata` - Metadata and configuration
# MAGIC
# MAGIC ### Next Steps:
# MAGIC 1. Run `create_metadata_tables` to set up metadata tables
# MAGIC 2. Run `main_pipeline_orchestrator` to start data processing
# MAGIC 3. Run `final_verification` to validate the setup

# COMMAND ----------

# Create summary result
setup_result = {
    "status": "SUCCESS",
    "environment": environment,
    "source_system": source_system,
    "catalogs_created": catalogs,
    "schemas_created": {
        "bronze": f"{catalogs['bronze']}.{source_schema}",
        "silver": f"{catalogs['silver']}.{source_schema}",
        "gold": f"{catalogs['gold']}.{source_schema}",
        "metadata": f"{catalogs['utility']}.metadata"
    }
}

print(f"\n🎉 Initial setup completed successfully!")
print(f"Environment: {environment}")
print(f"Source System: {source_system}")
print(f"Catalogs and schemas are ready for use!")

dbutils.notebook.exit(json.dumps(setup_result))
