{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "d6130d5b-636e-4b33-a53b-eec06dcc9057", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["dbutils.widgets.text(\"ipv_silver_catalog\", \"mbcl_silver\", \"ipv_silver_catalog\")\n", "dbutils.widgets.text(\"ipv_silver_schema\", \"mbcl_ipv\", \"ipv_silver_schema\")\n", "\n", "dbutils.widgets.text(\"ipv_bronze_catalog\", \"mbcl_bronze\", \"ipv_bronze_catalog\")\n", "dbutils.widgets.text(\"ipv_bronze_schema\", \"mbcl_ipv\", \"ipv_bronze_schema\")\n", "\n", "dbutils.widgets.text(\"reference_gas_table\", \"bronze_reference_gas\", \"reference_gas_table\")\n", "dbutils.widgets.text(\"reference_power_table\", \"bronze_reference_power\", \"reference_power_table\")\n", "dbutils.widgets.text(\"reference_months_strips_table\", \"bronze_reference_months_strips\", \"reference_months_strips_table\")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "1889ff01-ec3b-43ae-aee7-7d1e27d5d7a8", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["from pyspark.sql import functions as F"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "d0369a50-4c4e-4e62-8774-76932e709f85", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["ipv_silver_catalog = dbutils.widgets.get(\"ipv_silver_catalog\")\n", "ipv_silver_schema = dbutils.widgets.get(\"ipv_silver_schema\")\n", "\n", "ipv_bronze_catalog = dbutils.widgets.get(\"ipv_bronze_catalog\")\n", "ipv_bronze_schema = dbutils.widgets.get(\"ipv_bronze_schema\")\n", "\n", "reference_gas_table_name = dbutils.widgets.get(\"reference_gas_table\")\n", "reference_power_table_name = dbutils.widgets.get(\"reference_power_table\")\n", "reference_months_strips_table_name = dbutils.widgets.get(\"reference_months_strips_table\")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "088f5980-cc64-4ce6-b698-98c001bed968", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["SETTLEMENT_PRICES_POWER_PK = ['trade_date', 'hub', 'product', 'strip', 'contract']\n", "SETTLEMENT_PRICES_GAS_PK = ['trade_date', 'hub', 'product', 'strip', 'contract']\n", "\n", "BRONZE_SETTLEMENT_PRICES_POWER_INGESTION_TIMESTAMP_COLUMN = 'ingestion_timestamp'\n", "BRONZE_SETTLEMENT_PRICES_GAS_INGESTION_TIMESTAMP_COLUMN = 'ingestion_timestamp'"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "b28abf51-7cae-464e-a43b-0c889e2001d3", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["def process_incremental_bronze_to_silver(\n", "    pipeline_name,\n", "    bronze_table,\n", "    bronze_primary_key,\n", "    bronze_ingestion_timestamp_column,\n", "    silver_table,\n", "    reference_tables\n", "):\n", "    \"\"\"\n", "    This function processes the incremental bronze table to silver table.\n", "    \"\"\"\n", "    try:\n", "        last_ts = spark.read.table(f\"{ipv_silver_catalog}.{ipv_silver_schema}.silver_watermark\") \\\n", "            .filter(F.col(\"pipeline_name\") == pipeline_name) \\\n", "            .select(\"last_processed\") \\\n", "            .collect()[0][\"last_processed\"]\n", "    except:\n", "        last_ts = None\n", "\n", "    df_bronze = spark.read.table(bronze_table)\n", "    \n", "    if last_ts:\n", "        df_bronze = df_bronze.filter(<PERSON>.col(bronze_ingestion_timestamp_column) > last_ts)\n", "\n", "    new_rows_count = df_bronze.count()\n", "    if new_rows_count == 0:\n", "        print(\"No new rows to process\")\n", "        return\n", "\n", "    # drop duplicates for current data batch (duplicates might still be present from different ingestion times)\n", "    df_bronze = df_bronze.dropDuplicates(bronze_primary_key)\n", "\n", "    new_rows_count_deduplicated = df_bronze.count()\n", "    if new_rows_count_deduplicated == 0:\n", "        print(\"All rows were duplicates\")\n", "        return\n", "\n", "    for reference_table, join_condition, join_type in reference_tables.values():\n", "        df_bronze = df_bronze.join(\n", "            reference_table, \n", "            join_condition, \n", "            join_type\n", "        )\n", "\n", "    new_rows_count_deduplicated_and_joined = df_bronze.count() \n", "    if new_rows_count_deduplicated_and_joined == 0:\n", "        print(\"There was no matching row after join with our reference data\")\n", "        return\n", "    \n", "    if 'strip' in df_bronze.columns:\n", "        # split (format: month/day/year) into 2 columns strip_month and strip_year\n", "        df_bronze = df_bronze.withColumn(\"strip_month\", F.split(F.col(\"strip\"), \"/\")[0]) \\\n", "            .withColumn(\"strip_year\", <PERSON><PERSON>split(<PERSON>.col(\"strip\"), \"/\")[2])\n", "\n", "    # create a pk based on the bronze_primary_key and a hash on the composite columns\n", "    df_bronze = df_bronze.withColumn(\"primary_key\", F.sha2(F.concat_ws(\"||\", *bronze_primary_key), 256))\n", "\n", "    # silver logic for power reference data transformations\n", "    if pipeline_name == 'silver_settlement_prices_power':\n", "        df_bronze = (\n", "            df_bronze\n", "                .withColumn(\"pk\", <PERSON><PERSON>col(\"pk\").cast(\"boolean\")) \n", "                .with<PERSON><PERSON><PERSON>n(\"op\", <PERSON><PERSON>col(\"op\").cast(\"boolean\")) \n", "                .withColumn(\"rt\", <PERSON><PERSON>col(\"rt\").cast(\"boolean\"))\n", "                .with<PERSON><PERSON><PERSON>n(\"da\", <PERSON><PERSON>col(\"da\").cast(\"boolean\"))\n", "        )\n", "        df_bronze = (\n", "            df_bronze\n", "                .withColumn(\"is_peak\", F.when(F.col(\"pk\") == True, True).otherwise(False))\n", "                .withColumn(\"is_rt\", F.when(F.col(\"rt\") == True, True).otherwise(False))\n", "        )\n", "\n", "    print(f\"Initial total rows: {new_rows_count}. Writing {new_rows_count_deduplicated_and_joined} rows to silver; \\\n", "            {new_rows_count - new_rows_count_deduplicated} rows removed after deduplication; \\\n", "            {new_rows_count_deduplicated - new_rows_count_deduplicated_and_joined} rows removed after join; \\\n", "        \")\n", "    df_bronze.write.mode(\"append\").format(\"delta\").saveAsTable(silver_table)\n", "\n", "    new_last_ts = df_bronze.agg(F.max(\"ingestion_timestamp\").alias(\"max_ts\")).collect()[0][\"max_ts\"]\n", "\n", "    watermark_update = spark.createDataFrame(\n", "        [(pipeline_name, new_last_ts)],\n", "        [\"pipeline_name\", \"last_processed\"]\n", "    )\n", "\n", "    (watermark_update\n", "        .write\n", "        .mode(\"overwrite\")\n", "        .option(\"replaceWhere\", f\"pipeline_name = '{pipeline_name}'\")\n", "        .saveAsTable(f\"{ipv_silver_catalog}.{ipv_silver_schema}.silver_watermark\")\n", "    )\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "5856de88-211d-43c6-8296-38d532393747", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["reference_gas = spark.read.table(f\"{ipv_bronze_catalog}.{ipv_bronze_schema}.{reference_gas_table_name}\")\n", "reference_power = spark.read.table(f\"{ipv_bronze_catalog}.{ipv_bronze_schema}.{reference_power_table_name}\")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "46cf9be0-58db-4742-b96a-5c105a18a9e3", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["silver_pipeline_steps = [\n", "    (\n", "        \"silver_settlement_prices_power\",\n", "        f\"{ipv_bronze_catalog}.{ipv_bronze_schema}.bronze_settlement_prices_power\",\n", "        SETTLEMENT_PRICES_POWER_PK,\n", "        BRONZE_SETTLEMENT_PRICES_POWER_INGESTION_TIMESTAMP_COLUMN,\n", "        f\"{ipv_silver_catalog}.{ipv_silver_schema}.silver_settlement_prices_power\",\n", "        {\n", "            'reference_power': (\n", "                F.broadcast(reference_power).withColumnRenamed('ingestion_timestamp', 'reference_power_ingestion_timestamp'), \n", "                F.col('contract') == F.col('ice_code'), \n", "                'inner'\n", "            ),\n", "        }, \n", "    ),\n", "    (\n", "        \"silver_settlement_prices_gas\",\n", "        f\"{ipv_bronze_catalog}.{ipv_bronze_schema}.bronze_settlement_prices_gas\",\n", "        SETTLEMENT_PRICES_GAS_PK,\n", "        BRONZE_SETTLEMENT_PRICES_GAS_INGESTION_TIMESTAMP_COLUMN,\n", "        f\"{ipv_silver_catalog}.{ipv_silver_schema}.silver_settlement_prices_gas\",\n", "        {\n", "            'reference_gas': (\n", "                F.broadcast(reference_gas).withColumnRenamed('ingestion_timestamp', 'reference_gas_ingestion_timestamp'), \n", "                F.col('contract') == F.col('ice_code'),\n", "                'inner'\n", "            ),\n", "        }, \n", "    ),\n", "]\n", "\n", "for pipeline_name, bronze_table, bronze_pk, bronze_ingestion_time_column_name, silver_table, join_tables  in silver_pipeline_steps:\n", "    process_incremental_bronze_to_silver(\n", "        pipeline_name,\n", "        bronze_table,\n", "        bronze_pk,\n", "        bronze_ingestion_time_column_name,\n", "        silver_table,\n", "        join_tables,\n", "    )"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "inputWidgetPreferences": null, "language": "python", "notebookMetadata": {"pythonIndentUnit": 4}, "notebookName": "ipv_ice_settlements_bronze_to_silver", "widgets": {"ipv_bronze_catalog": {"currentValue": "mbcl_dev_bronze", "nuid": "56e6f645-83d1-4ae0-9336-034e9e67b67e", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "mbcl_bronze", "label": "ipv_bronze_catalog", "name": "ipv_bronze_catalog", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "mbcl_bronze", "label": "ipv_bronze_catalog", "name": "ipv_bronze_catalog", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}, "ipv_bronze_schema": {"currentValue": "ipv", "nuid": "6c196f3c-3801-4c20-b1f9-60d845f13b5a", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "mbcl_ipv", "label": "ipv_bronze_schema", "name": "ipv_bronze_schema", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "mbcl_ipv", "label": "ipv_bronze_schema", "name": "ipv_bronze_schema", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}, "ipv_silver_catalog": {"currentValue": "mbcl_dev_silver", "nuid": "fda15ae9-b2c6-4f5a-a485-de0147adc3a4", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "mbcl_silver", "label": "ipv_silver_catalog", "name": "ipv_silver_catalog", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "mbcl_silver", "label": "ipv_silver_catalog", "name": "ipv_silver_catalog", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}, "ipv_silver_schema": {"currentValue": "ipv", "nuid": "cd63e95b-f438-4a43-9bed-b5eafd06fa80", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "mbcl_ipv", "label": "ipv_silver_schema", "name": "ipv_silver_schema", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "mbcl_ipv", "label": "ipv_silver_schema", "name": "ipv_silver_schema", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}, "reference_gas_table": {"currentValue": "bronze_reference_gas", "nuid": "0e68317b-42b8-43d1-a73f-7b0d6a086204", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "bronze_reference_gas", "label": "reference_gas_table", "name": "reference_gas_table", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "bronze_reference_gas", "label": "reference_gas_table", "name": "reference_gas_table", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}, "reference_months_strips_table": {"currentValue": "bronze_reference_months_strips", "nuid": "62d01558-4d12-4ec0-a67e-c6c228227f5b", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "bronze_reference_months_strips", "label": "reference_months_strips_table", "name": "reference_months_strips_table", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "bronze_reference_months_strips", "label": "reference_months_strips_table", "name": "reference_months_strips_table", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}, "reference_power_table": {"currentValue": "bronze_reference_power", "nuid": "e07ed8b5-b29e-4c4d-942e-47dcb050e17e", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "bronze_reference_power", "label": "reference_power_table", "name": "reference_power_table", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "bronze_reference_power", "label": "reference_power_table", "name": "reference_power_table", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}