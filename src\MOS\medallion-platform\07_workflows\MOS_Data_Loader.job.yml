resources:
  jobs:
    MOS_Data_Loader:
      name: MOS_Data_Loader
      description: Job to Load MOS DataSets into Bronze and Silver Layers with Dynamic Configuration
      email_notifications:
        on_success:
          - balaji.<PERSON><PERSON><PERSON><PERSON>@mbcl.com
        on_failure:
          - balaji.k<PERSON><EMAIL>
      schedule:
        quartz_cron_expression: 38 0 6 * * ?
        timezone_id: UTC
        pause_status: UNPAUSED
      max_concurrent_runs: 1
      parameters:
        - name: environment
          default: dev
        - name: source_system
          default: MOS
        - name: run_mode
          default: full_load
        - name: catalog_name
          default: ""
      tasks:
        - task_key: MOS_Bronze_Full_Load
          notebook_task:
            notebook_path: /Workspace/Shared/medallion-platform/01_bronze_layer/oracle_full_load
            base_parameters:
              environment: "{{job.parameters.environment}}"
              source_system: "{{job.parameters.source_system}}"
              run_id: "{{job.run_id}}"
              catalog_name: "{{job.parameters.catalog_name}}"
            source: WORKSPACE
          existing_cluster_id: "0603-115126-9e8q7erz"
          timeout_seconds: 3600

        - task_key: MOS_Silver_Transformation
          depends_on:
            - task_key: MOS_Bronze_Full_Load
          notebook_task:
            notebook_path: /Workspace/Shared/medallion-platform/02_silver_layer/silver_orchestrator
            base_parameters:
              environment: "{{job.parameters.environment}}"
              source_system: "{{job.parameters.source_system}}"
              run_id: "{{job.run_id}}"
              catalog_name: "{{job.parameters.catalog_name}}"
            source: WORKSPACE
          existing_cluster_id: "0603-115126-9e8q7erz"
          timeout_seconds: 3600

        - task_key: MOS_Data_Quality_Check
          depends_on:
            - task_key: MOS_Silver_Transformation
          notebook_task:
            notebook_path: /Workspace/Shared/medallion-platform/06_utilities/data_quality_check
            base_parameters:
              environment: "{{job.parameters.environment}}"
              source_system: "{{job.parameters.source_system}}"
              run_id: "{{job.run_id}}"
              catalog_name: "{{job.parameters.catalog_name}}"
            source: WORKSPACE
          existing_cluster_id: "0603-115126-9e8q7erz"
          timeout_seconds: 1800

        - task_key: MOS_Final_Verification
          depends_on:
            - task_key: MOS_Data_Quality_Check
          notebook_task:
            notebook_path: /Workspace/Shared/medallion-platform/06_utilities/final_verification
            base_parameters:
              environment: "{{job.parameters.environment}}"
              source_system: "{{job.parameters.source_system}}"
              run_id: "{{job.run_id}}"
              catalog_name: "{{job.parameters.catalog_name}}"
            source: WORKSPACE
          existing_cluster_id: "0603-115126-9e8q7erz"
          timeout_seconds: 900
          
      tags:
        Env: "{{job.parameters.environment}}"
        Owner: Front Office
        SourceSystem: "{{job.parameters.source_system}}"
        Version: v2.0
      queue:
        enabled: true
