{"job_id": 27768570558612, "creator_user_name": "<EMAIL>", "run_as_user_name": "<EMAIL>", "run_as_owner": true, "settings": {"name": "IPV_MBCL_Values", "email_notifications": {"no_alert_for_skipped_runs": false}, "webhook_notifications": {}, "timeout_seconds": 0, "schedule": {"quartz_cron_expression": "0 0 22 ? * MON-FRI", "timezone_id": "Europe/London", "pause_status": "UNPAUSED"}, "max_concurrent_runs": 1, "tasks": [{"task_key": "DB_Ingestion", "run_if": "ALL_SUCCESS", "notebook_task": {"notebook_path": "/Workspace/Users/<USER>/mbcl-databricks/IPV/trader_marks/db_ingestion_to_bronze", "base_parameters": {"env": "dev", "entity": "ipv"}, "source": "WORKSPACE"}, "timeout_seconds": 0, "email_notifications": {}, "webhook_notifications": {}}, {"task_key": "Transformations", "depends_on": [{"task_key": "DB_Ingestion"}], "run_if": "ALL_SUCCESS", "notebook_task": {"notebook_path": "/Workspace/Users/<USER>/mbcl-databricks/IPV/trader_marks/trader_mark_bronze_to_silver", "base_parameters": {"env": "dev", "entity": "ipv"}, "source": "WORKSPACE"}, "timeout_seconds": 0, "email_notifications": {}, "webhook_notifications": {}}, {"task_key": "Presentation_Layer", "depends_on": [{"task_key": "Transformations"}], "run_if": "ALL_SUCCESS", "notebook_task": {"notebook_path": "/Workspace/Users/<USER>/mbcl-databricks/IPV/trader_marks/trader_mark_silver_to_gold", "base_parameters": {"env": "dev", "entity": "ipv"}, "source": "WORKSPACE"}, "timeout_seconds": 0, "email_notifications": {}, "webhook_notifications": {}}], "git_source": {"git_url": "https://github.com/MBCL-IT-Dev/mbcl-databricks.git", "git_provider": "gitHub", "git_branch": "feature/DPI-33-trader-marks"}, "format": "MULTI_TASK", "queue": {"enabled": true}, "performance_target": "STANDARD"}, "created_time": 1753994474335}