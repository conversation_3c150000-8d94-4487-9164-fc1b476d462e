{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "fa64418b-7298-4c65-bed7-0581654cf22f", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["dbutils.widgets.text(\"ipv_silver_catalog\", \"mbcl_silver\", \"ipv_silver_catalog\")\n", "dbutils.widgets.text(\"ipv_silver_schema\", \"mbcl_ipv\", \"ipv_silver_schema\")\n", "\n", "dbutils.widgets.text(\"ipv_gold_catalog\", \"mbcl_gold\", \"ipv_gold_catalog\")\n", "dbutils.widgets.text(\"ipv_gold_schema\", \"mbcl_ipv\", \"ipv_gold_schema\")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "a101272b-f7c4-40fe-9898-401bd33bb07c", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["ipv_silver_catalog = dbutils.widgets.get(\"ipv_silver_catalog\")\n", "ipv_silver_schema = dbutils.widgets.get(\"ipv_silver_schema\")\n", "\n", "ipv_gold_catalog = dbutils.widgets.get(\"ipv_gold_catalog\")\n", "ipv_gold_schema = dbutils.widgets.get(\"ipv_gold_schema\")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "fab2eb44-d13e-4f7a-b3c8-3cd292d422ea", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["spark.sql(f\"\"\"\n", "CREATE OR REPLACE VIEW `{ipv_gold_catalog}`.`{ipv_gold_schema}`.gas_settlement_prices_agg_V AS\n", "SELECT \n", "  primary_key,\n", "  to_date(split(trade_date, ' ')[0], 'M/dd/yyyy') as trade_date,\n", "  location_hub as location,\n", "  region,\n", "  ice_code,\n", "  CONCAT(\n", "    CASE strip_month\n", "      WHEN 1 THEN 'Jan'\n", "      WHEN 2 THEN 'Feb'\n", "      WHEN 3 THEN 'Mar'\n", "      WHEN 4 THEN 'Apr'\n", "      WHEN 5 THEN 'May'\n", "      WHEN 6 THEN 'Jun'\n", "      WHEN 7 THEN 'Jul'\n", "      WHEN 8 THEN 'Aug'\n", "      WHEN 9 THEN 'Sep'\n", "      WHEN 10 THEN 'Oct'\n", "      WHEN 11 THEN 'Nov'\n", "      WHEN 12 THEN 'Dec'\n", "    END, \n", "    '-', \n", "    strip_year\n", "  ) as strip_date,\n", "  to_date(CONCAT(strip_year, '-', lpad(strip_month, 2, '0'), '-01'), 'yyyy-MM-dd') as strip_timestamp,\n", "  ol_code as index_name,\n", "  format_number(round(SUM(settlement_price), 2), 2) AS `value`\n", "  FROM `{ipv_silver_catalog}`.`{ipv_silver_schema}`.silver_settlement_prices_gas\n", "  GROUP BY primary_key, trade_date, location, region, ice_code, strip_date, strip_timestamp, ol_code\n", "\"\"\")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "384e262d-d683-488e-9903-05a223215cdb", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["spark.sql(f\"\"\"\n", "CREATE OR REPLACE VIEW `{ipv_gold_catalog}`.`{ipv_gold_schema}`.power_settlement_prices_agg_V AS\n", "SELECT \n", "  primary_key,\n", "  to_date(split(trade_date, ' ')[0], 'M/dd/yyyy') as trade_date,\n", "  location_hub as location,\n", "  region,\n", "  zone,\n", "  ice_code,\n", "  CONCAT(\n", "    CASE strip_month\n", "      WHEN 1 THEN 'Jan'\n", "      WHEN 2 THEN 'Feb'\n", "      WHEN 3 THEN 'Mar'\n", "      WHEN 4 THEN 'Apr'\n", "      WHEN 5 THEN 'May'\n", "      WHEN 6 THEN 'Jun'\n", "      WHEN 7 THEN 'Jul'\n", "      WHEN 8 THEN 'Aug'\n", "      WHEN 9 THEN 'Sep'\n", "      WHEN 10 THEN 'Oct'\n", "      WHEN 11 THEN 'Nov'\n", "      WHEN 12 THEN 'Dec'\n", "    END, \n", "    '-', \n", "    strip_year\n", "  ) as strip_date,\n", "  to_date(CONCAT(strip_year, '-', lpad(strip_month, 2, '0'), '-01'), 'yyyy-MM-dd') as strip_timestamp,\n", "  ol_code as index_name,\n", "  pk as is_peak,\n", "  op as is_off_peak,\n", "  rt as is_real_time,\n", "  da as is_day_ahead,\n", "  format_number(round(SUM(settlement_price), 4), 4) AS `value`\n", "  FROM `{ipv_silver_catalog}`.`{ipv_silver_schema}`.silver_settlement_prices_power\n", "  GROUP BY primary_key, trade_date, location, region, zone, ice_code, strip_date, strip_timestamp, index_name, pk, op, rt, da\n", "\"\"\")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "2c7d8998-cf89-4118-9069-aa6bfce684bf", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": []}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "inputWidgetPreferences": null, "language": "python", "notebookMetadata": {"pythonIndentUnit": 4}, "notebookName": "ipv_ice_settlements_silver_to_gold", "widgets": {"ipv_gold_catalog": {"currentValue": "mbcl_dev_gold", "nuid": "f63df11d-cff2-4270-9681-cfcbec9f0fd2", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "mbcl_gold", "label": "ipv_gold_catalog", "name": "ipv_gold_catalog", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "mbcl_gold", "label": "ipv_gold_catalog", "name": "ipv_gold_catalog", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}, "ipv_gold_schema": {"currentValue": "ipv", "nuid": "5d60782d-531f-49aa-97b3-33c738f0fb4a", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "mbcl_ipv", "label": "ipv_gold_schema", "name": "ipv_gold_schema", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "mbcl_ipv", "label": "ipv_gold_schema", "name": "ipv_gold_schema", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}, "ipv_silver_catalog": {"currentValue": "mbcl_dev_silver", "nuid": "2cd99ffd-94e4-4838-be04-5e46dee2bb59", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "mbcl_silver", "label": "ipv_silver_catalog", "name": "ipv_silver_catalog", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "mbcl_silver", "label": "ipv_silver_catalog", "name": "ipv_silver_catalog", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}, "ipv_silver_schema": {"currentValue": "ipv", "nuid": "c23243d0-c849-49de-af98-1373f10b24a7", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "mbcl_ipv", "label": "ipv_silver_schema", "name": "ipv_silver_schema", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "mbcl_ipv", "label": "ipv_silver_schema", "name": "ipv_silver_schema", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}