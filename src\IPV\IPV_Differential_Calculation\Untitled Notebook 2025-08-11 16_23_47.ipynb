{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "93d3f33f-6542-4ba8-971e-fb858d9e14ed", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["dbutils.widgets.text(\"ipv_silver_catalog\", \"mbcl_silver\", \"ipv_silver_catalog\")\n", "dbutils.widgets.text(\"ipv_silver_schema\", \"mbcl_ipv\", \"ipv_silver_schema\")\n", "\n", "dbutils.widgets.text(\"ipv_gold_catalog\", \"mbcl_gold\", \"ipv_gold_catalog\")\n", "dbutils.widgets.text(\"ipv_gold_schema\", \"mbcl_ipv\", \"ipv_gold_schema\")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "46bdaf46-e714-4c01-8c8f-468977f1ca3e", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["ipv_silver_catalog = dbutils.widgets.get(\"ipv_silver_catalog\")\n", "ipv_silver_schema = dbutils.widgets.get(\"ipv_silver_schema\")\n", "\n", "ipv_gold_catalog = dbutils.widgets.get(\"ipv_gold_catalog\")\n", "ipv_gold_schema = dbutils.widgets.get(\"ipv_gold_schema\")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "67074773-6c5e-437c-a73f-76a750c24ef1", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["spark.sql(f\"\"\"\n", "    CREATE OR REPLACE VIEW `{ipv_gold_catalog}`.`{ipv_gold_schema}`.ipv_difference_power_V AS\n", "    SELECT \n", "        code as ice_code,\n", "        trade_date,\n", "        strip_date,\n", "        ipv_settlement_and_local_value_diff as difference\n", "    FROM {ipv_silver_catalog}.{ipv_silver_schema}.silver_ipv_difference_power\n", "\"\"\")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "29de8d5e-6092-4f6c-b579-b5a48b141563", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["spark.sql(f\"\"\"\n", "    CREATE OR REPLACE VIEW `{ipv_gold_catalog}`.`{ipv_gold_schema}`.ipv_difference_gas_V AS\n", "    SELECT \n", "        code as ice_code,\n", "        trade_date,\n", "        strip_date,\n", "        ipv_settlement_and_local_value_diff as difference\n", "    FROM {ipv_silver_catalog}.{ipv_silver_schema}.silver_ipv_difference_gas\n", "\"\"\")"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "inputWidgetPreferences": null, "language": "python", "notebookMetadata": {"pythonIndentUnit": 4}, "notebookName": "Untitled Notebook 2025-08-11 16_23_47", "widgets": {"ipv_gold_catalog": {"currentValue": "mbcl_dev_gold", "nuid": "38f1443e-2125-476e-b4da-5401bf7f448d", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "mbcl_gold", "label": "ipv_gold_catalog", "name": "ipv_gold_catalog", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "mbcl_gold", "label": "ipv_gold_catalog", "name": "ipv_gold_catalog", "options": {"widgetType": "text", "autoCreated": false, "validationRegex": null}}}, "ipv_gold_schema": {"currentValue": "ipv", "nuid": "ff50255b-2f67-46ad-8c75-7e26b07a8cc1", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "mbcl_ipv", "label": "ipv_gold_schema", "name": "ipv_gold_schema", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "mbcl_ipv", "label": "ipv_gold_schema", "name": "ipv_gold_schema", "options": {"widgetType": "text", "autoCreated": false, "validationRegex": null}}}, "ipv_silver_catalog": {"currentValue": "mbcl_dev_silver", "nuid": "32b54241-1389-4184-85b3-e533c604f2eb", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "mbcl_silver", "label": "ipv_silver_catalog", "name": "ipv_silver_catalog", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "mbcl_silver", "label": "ipv_silver_catalog", "name": "ipv_silver_catalog", "options": {"widgetType": "text", "autoCreated": false, "validationRegex": null}}}, "ipv_silver_schema": {"currentValue": "ipv", "nuid": "e8c41452-3b8b-45b8-82ed-7e7de75f2ea1", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "mbcl_ipv", "label": "ipv_silver_schema", "name": "ipv_silver_schema", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "mbcl_ipv", "label": "ipv_silver_schema", "name": "ipv_silver_schema", "options": {"widgetType": "text", "autoCreated": false, "validationRegex": null}}}}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}