# Workflow Dependency Fix Documentation

## Issues Resolved

**Error 1**: `dependency on an if/else condition "MOS_Bronze_Incremental_Load" does not specify an outcome`
**Error 2**: `If/else condition does not need a cluster. Remove the cluster specification and retry again`
**Error 3**: `The dependency on a non-if/else condition "MOS_Bronze_Incremental_Execute" specifies an outcome "true". Outcomes can only be specified for if/else condition dependencies`
**Error 4**: `Silver and other dependencies didn't run after Bronze Full Load completed`

## Root Cause

In Databricks workflows:
1. A task cannot have both `notebook_task` AND `condition_task` - it's one or the other
2. A `condition_task` only evaluates conditions - it doesn't run code, so it doesn't need cluster resources
3. When tasks depend on conditional tasks, they must specify the expected outcome
4. **CRITICAL**: Only conditional tasks (with `condition_task`) can have `outcome` specified - regular notebook tasks cannot
5. **DEPENDENCY ISSUE**: When Silver depends on a notebook task that gets skipped due to conditions, the dependency chain breaks

## Solution Applied

### MOS_Data_Loader.job.yml

**Split Conditional Logic into Two Tasks:**
```yaml
# Task 1: Condition evaluation only (no cluster needed)
- task_key: MOS_Bronze_Incremental_Load
  depends_on:
    - task_key: MOS_Bronze_Full_Load
  condition_task:
    op: EQUAL_TO
    left: "{{job.parameters.run_mode}}"
    right: "incremental"

# Task 2: Actual notebook execution (needs cluster)
- task_key: MOS_Bronze_Incremental_Execute
  depends_on:
    - task_key: MOS_Bronze_Incremental_Load
  notebook_task:
    notebook_path: /Workspace/Shared/medallion-platform/01_bronze_layer/oracle_incremental_load
    # ... parameters ...
  existing_cluster_id: "0603-115126-9e8q7erz"

# Task 3: FIXED - Depends on condition task (WITH outcome)
- task_key: MOS_Silver_Transformation
  depends_on:
    - task_key: MOS_Bronze_Full_Load
    - task_key: MOS_Bronze_Incremental_Load  # ← FIXED: Depend on condition task
      outcome: "true"  # ← This handles both success AND skip
```

### Generic_Source_System_Loader.job.yml

**Fixed Dependency Structure:**
```yaml
- task_key: Silver_Transformation
  depends_on:
    - task_key: Bronze_Full_Load
    - task_key: Bronze_Incremental_Load
      outcome: "true"  # Wait for success OR skip
```

## How It Works

### Scenario 1: run_mode = "full_load"
1. `MOS_Bronze_Full_Load` runs ✅
2. `MOS_Bronze_Incremental_Load` is **skipped** (condition not met) ⏭️
3. `MOS_Silver_Transformation` waits for both:
   - `MOS_Bronze_Full_Load` = SUCCESS ✅
   - `MOS_Bronze_Incremental_Load` = SKIPPED (outcome: "true") ✅
4. `MOS_Silver_Transformation` proceeds ✅

### Scenario 2: run_mode = "incremental"
1. `MOS_Bronze_Full_Load` runs ✅
2. `MOS_Bronze_Incremental_Load` runs (condition met) ✅
3. `MOS_Silver_Transformation` waits for both:
   - `MOS_Bronze_Full_Load` = SUCCESS ✅
   - `MOS_Bronze_Incremental_Load` = SUCCESS (outcome: "true") ✅
4. `MOS_Silver_Transformation` proceeds ✅

## Updated Configuration

### MOS_Data_Loader.job.yml
```yaml
email_notifications:
  on_success:
    - <EMAIL>
  on_failure:
    - <EMAIL>

schedule:
  quartz_cron_expression: 38 0 6 * * ?  # 6:38 AM UTC daily

existing_cluster_id: "0603-115126-9e8q7erz"  # Your cluster ID

parameters:
  - name: run_mode
    default: full_load  # Options: full_load, incremental
```

## Run Modes Explained

### full_load (Default)
- Runs `MOS_Bronze_Full_Load` ✅
- Skips `MOS_Bronze_Incremental_Load` ⏭️
- Continues with Silver transformation ✅

### incremental
- Runs `MOS_Bronze_Full_Load` ✅
- Runs `MOS_Bronze_Incremental_Load` ✅
- Continues with Silver transformation ✅

## Testing the Fix

### Test 1: Full Load Mode
```bash
# Trigger workflow with parameters:
{
  "environment": "dev",
  "source_system": "MOS",
  "run_mode": "full_load",
  "catalog_name": ""
}
```

**Expected Result:**
- Bronze Full Load: ✅ SUCCESS
- Bronze Incremental Load: ⏭️ SKIPPED
- Silver Transformation: ✅ SUCCESS (proceeds after both dependencies)

### Test 2: Incremental Mode
```bash
# Trigger workflow with parameters:
{
  "environment": "dev",
  "source_system": "MOS", 
  "run_mode": "incremental",
  "catalog_name": ""
}
```

**Expected Result:**
- Bronze Full Load: ✅ SUCCESS
- Bronze Incremental Load: ✅ SUCCESS
- Silver Transformation: ✅ SUCCESS (proceeds after both dependencies)

## Best Practices for Conditional Dependencies

### 1. Always Specify Outcome
```yaml
depends_on:
  - task_key: conditional_task
    outcome: "true"  # or "false"
```

### 2. Use Clear Condition Logic
```yaml
condition_task:
  op: EQUAL_TO           # Clear comparison
  left: "{{parameter}}"  # Parameter reference
  right: "expected_value" # Expected value
```

### 3. Document Run Modes
```yaml
parameters:
  - name: run_mode
    default: full_load
    # Options: full_load, incremental, bronze_only
```

## Troubleshooting

### If Tasks Still Don't Run
1. **Check condition logic**: Ensure parameter values match exactly
2. **Verify outcome specification**: Use `outcome: "true"` for success/skip
3. **Review parameter defaults**: Ensure they match expected conditions
4. **Test in dev first**: Always test workflow changes in development

### Common Issues
- **Case sensitivity**: `"Full_Load"` ≠ `"full_load"`
- **Missing quotes**: Use quotes around parameter values
- **Wrong outcome**: Use `"true"` for success/skip, `"false"` for failure

The workflow is now properly configured to handle conditional dependencies! 🚀
