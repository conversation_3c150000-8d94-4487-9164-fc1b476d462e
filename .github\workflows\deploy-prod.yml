name: Deploy to PROD

on:
  workflow_dispatch:
    inputs:
      use_case:
        description: 'Select use case to deploy (shared folder is always included)'
        required: true
        type: choice
        options:
          - usecase-1
          - usecase-2
          - all
      change_ticket:
        description: 'Change ticket number (required)'
        required: true
        type: string
      deployment_reason:
        description: 'Reason for deployment'
        required: true
        type: string
      rollback_plan:
        description: 'Rollback plan if deployment fails'
        required: true
        type: string
        default: 'Revert to previous version using Git tags'
      notify_stakeholders:
        description: 'Send notifications to stakeholders'
        required: false
        type: boolean
        default: false

jobs:
  pre-deployment-checks:
    runs-on: ubuntu-latest
    name: Pre-Deployment Validation
    outputs:
      proceed: ${{ steps.validation.outputs.proceed }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Validate inputs
        id: validation
        run: |
          # Just ensure change ticket is not empty
          if [ -z "${{ github.event.inputs.change_ticket }}" ]; then
            echo "❌ Change ticket is required"
            echo "proceed=false" >> $GITHUB_OUTPUT
            exit 1
          fi
          
          # Check if deployment reason is provided
          if [ -z "${{ github.event.inputs.deployment_reason }}" ]; then
            echo "❌ Deployment reason is required"
            echo "proceed=false" >> $GITHUB_OUTPUT
            exit 1
          fi
          
          echo "✅ All validations passed"
          echo "✅ Change ticket: ${{ github.event.inputs.change_ticket }}"
          echo "✅ Deployment reason: ${{ github.event.inputs.deployment_reason }}"
          echo "proceed=true" >> $GITHUB_OUTPUT
      
      - name: Check TEST deployment status
        run: |
          echo "⚠️ WARNING: Ensure the selected use case has been tested in TEST environment"
          echo "Use Case: ${{ github.event.inputs.use_case }}"
          echo "Change Ticket: ${{ github.event.inputs.change_ticket }}"

  deploy-to-prod:
    needs: pre-deployment-checks
    if: needs.pre-deployment-checks.outputs.proceed == 'true'
    runs-on: ubuntu-latest
    name: Deploy to PROD Environment
    environment: production
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
      
      - name: Install Python dependencies
        run: |
          pip install requests
      
      - name: Install Databricks CLI
        run: |
          echo "Installing Databricks CLI..."
          
          # Try the official installer
          curl -fsSL https://raw.githubusercontent.com/databricks/setup-cli/main/install.sh | sh
          
          # The installer puts the CLI in /usr/local/bin/databricks
          # Verify installation
          if databricks --version; then
            echo "✅ Databricks CLI installation verified"
          else
            echo "❌ Databricks CLI installation failed"
            exit 1
          fi
      
      - name: Create deployment backup
        env:
          DATABRICKS_HOST: ${{ vars.DATABRICKS_HOST }}
          DATABRICKS_TOKEN: ${{ secrets.DATABRICKS_TOKEN }}
        run: |
          echo "Creating backup of current PROD deployment..."
          
          BACKUP_DATE=$(date +'%Y%m%d-%H%M%S')
          BACKUP_PATH="/Workspace/Deployments/prod-backups/${BACKUP_DATE}"
          LOCAL_BACKUP_DIR="./prod-backup-${BACKUP_DATE}"
          
          # Create local backup directory
          mkdir -p "${LOCAL_BACKUP_DIR}"
          
          echo "Attempting to backup existing deployment..."
          
          # Use --profile DEFAULT to bypass bundle configuration
          # Backup shared folder
          echo "Backing up shared folder..."
          databricks workspace export-dir \
            /Workspace/Deployments/prod/files/src/shared \
            "${LOCAL_BACKUP_DIR}/shared" \
            --profile DEFAULT \
            --overwrite 2>/dev/null || echo "No shared folder found to backup"
          
          # Backup usecase-1
          echo "Backing up usecase-1..."
          databricks workspace export-dir \
            /Workspace/Deployments/prod/files/src/usecase-1 \
            "${LOCAL_BACKUP_DIR}/usecase-1" \
            --profile DEFAULT \
            --overwrite 2>/dev/null || echo "No usecase-1 found to backup"
          
          # Backup usecase-2
          echo "Backing up usecase-2..."
          databricks workspace export-dir \
            /Workspace/Deployments/prod/files/src/usecase-2 \
            "${LOCAL_BACKUP_DIR}/usecase-2" \
            --profile DEFAULT \
            --overwrite 2>/dev/null || echo "No usecase-2 found to backup"
          
          # Create backup directory in workspace
          databricks workspace mkdirs "${BACKUP_PATH}/src" --profile DEFAULT || true
          
          # Upload all backed up content to workspace backup location
          if [ -d "${LOCAL_BACKUP_DIR}/shared" ]; then
            echo "Uploading shared backup..."
            databricks workspace import-dir \
              "${LOCAL_BACKUP_DIR}/shared" \
              "${BACKUP_PATH}/src/shared" \
              --profile DEFAULT \
              --overwrite || echo "Failed to upload shared backup"
          fi
          
          if [ -d "${LOCAL_BACKUP_DIR}/usecase-1" ]; then
            echo "Uploading usecase-1 backup..."
            databricks workspace import-dir \
              "${LOCAL_BACKUP_DIR}/usecase-1" \
              "${BACKUP_PATH}/src/usecase-1" \
              --profile DEFAULT \
              --overwrite || echo "Failed to upload usecase-1 backup"
          fi
          
          if [ -d "${LOCAL_BACKUP_DIR}/usecase-2" ]; then
            echo "Uploading usecase-2 backup..."
            databricks workspace import-dir \
              "${LOCAL_BACKUP_DIR}/usecase-2" \
              "${BACKUP_PATH}/src/usecase-2" \
              --profile DEFAULT \
              --overwrite || echo "Failed to upload usecase-2 backup"
          fi
          
          # List backup contents for verification
          echo "Backup contents:"
          databricks workspace list "${BACKUP_PATH}/src" --profile DEFAULT 2>/dev/null || echo "No backup created"
          
          # Clean up local backup directory
          rm -rf "${LOCAL_BACKUP_DIR}"
          
          echo "Backup process completed: ${BACKUP_PATH}"
          echo "backup_path=${BACKUP_PATH}" >> $GITHUB_ENV
      
      - name: Display Deployment Plan
        run: |
          echo "================================================"
          echo "PRODUCTION DEPLOYMENT PLAN"
          echo "================================================"
          echo "Environment: PRODUCTION"
          echo "Use Case: ${{ github.event.inputs.use_case }}"
          echo "Change Ticket: ${{ github.event.inputs.change_ticket }}"
          echo "Triggered by: ${{ github.actor }}"
          echo "Reason: ${{ github.event.inputs.deployment_reason }}"
          echo "Rollback Plan: ${{ github.event.inputs.rollback_plan }}"
          echo ""
          echo "Folders to deploy:"
          echo "  ✓ src/shared (always deployed)"
          
          if [ "${{ github.event.inputs.use_case }}" = "all" ]; then
            echo "  ✓ src/usecase-1"
            echo "  ✓ src/usecase-2"
          else
            echo "  ✓ src/${{ github.event.inputs.use_case }}"
          fi
          
          echo ""
          echo "Backup created at: ${{ env.backup_path }}"
          echo "================================================"
      
      
      - name: Deploy Selected Use Cases to PROD
        env:
          DATABRICKS_HOST: ${{ vars.DATABRICKS_HOST }}
          DATABRICKS_TOKEN: ${{ secrets.DATABRICKS_TOKEN }}
        run: |
          echo "Deploying selected use cases to PROD..."
          echo "Use Case: ${{ github.event.inputs.use_case }}"
          
          # Create temporary directory for deployment
          TEMP_DIR="./temp-deploy-$(date +%s)"
          mkdir -p "$TEMP_DIR"
          
          # Always copy shared folder
          cp -r src/shared "$TEMP_DIR/"
          
          # Copy selected use case(s)
          if [ "${{ github.event.inputs.use_case }}" = "all" ]; then
            echo "Deploying ALL use cases..."
            cp -r src/usecase-1 "$TEMP_DIR/"
            cp -r src/usecase-2 "$TEMP_DIR/"
          else
            echo "Deploying ${{ github.event.inputs.use_case }} only..."
            cp -r src/${{ github.event.inputs.use_case }} "$TEMP_DIR/"
          fi
          
          # Ensure deployment directories exist
          databricks workspace mkdirs /Workspace/Deployments/prod/files/src --profile DEFAULT || true
          
          # Upload shared folder
          echo "Uploading shared folder..."
          databricks workspace import-dir \
            "$TEMP_DIR/shared" \
            "/Workspace/Deployments/prod/files/src/shared" \
            --profile DEFAULT \
            --overwrite
          
          # Upload use cases based on selection
          if [ "${{ github.event.inputs.use_case }}" = "all" ]; then
            echo "Uploading usecase-1..."
            databricks workspace import-dir \
              "$TEMP_DIR/usecase-1" \
              "/Workspace/Deployments/prod/files/src/usecase-1" \
              --profile DEFAULT \
              --overwrite
            
            echo "Uploading usecase-2..."
            databricks workspace import-dir \
              "$TEMP_DIR/usecase-2" \
              "/Workspace/Deployments/prod/files/src/usecase-2" \
              --profile DEFAULT \
              --overwrite
          elif [ "${{ github.event.inputs.use_case }}" = "usecase-1" ]; then
            echo "Uploading usecase-1..."
            databricks workspace import-dir \
              "$TEMP_DIR/usecase-1" \
              "/Workspace/Deployments/prod/files/src/usecase-1" \
              --profile DEFAULT \
              --overwrite
          elif [ "${{ github.event.inputs.use_case }}" = "usecase-2" ]; then
            echo "Uploading usecase-2..."
            databricks workspace import-dir \
              "$TEMP_DIR/usecase-2" \
              "/Workspace/Deployments/prod/files/src/usecase-2" \
              --profile DEFAULT \
              --overwrite
          fi
          
          # Clean up temp directory
          rm -rf "$TEMP_DIR"
          
          # Deploy cluster configuration
          if [ "${{ github.event.inputs.use_case }}" = "usecase-1" ]; then
            TARGET="prod-uc1"
          elif [ "${{ github.event.inputs.use_case }}" = "usecase-2" ]; then
            TARGET="prod-uc2"
          else
            TARGET="prod-all"
          fi
          
          echo "Deploying cluster configuration with target: $TARGET"
          echo "Running bundle validate..."
          databricks bundle validate -t $TARGET || echo "Validation warnings (continuing)"
          
          echo "Running bundle deploy for cluster resources..."
          # Since we already uploaded files manually, just deploy the resources (clusters)
          # The bundle deploy will skip file sync since files already exist
          databricks bundle deploy -t $TARGET --auto-approve
          
          echo "Verifying cluster creation..."
          databricks clusters list --profile DEFAULT | grep prod-cluster || echo "Cluster may take time to appear"
          
          echo "✅ Deployment completed to PROD"
      
      - name: Verify PROD Deployment
        env:
          DATABRICKS_HOST: ${{ vars.DATABRICKS_HOST }}
          DATABRICKS_TOKEN: ${{ secrets.DATABRICKS_TOKEN }}
        run: |
          echo "Verifying PROD deployment..."
          echo "----------------------------------------"
          
          # Determine the target for verification
          if [ "${{ github.event.inputs.use_case }}" = "usecase-1" ]; then
            TARGET="prod-uc1"
          elif [ "${{ github.event.inputs.use_case }}" = "usecase-2" ]; then
            TARGET="prod-uc2"
          else
            TARGET="prod-all"
          fi
          
          # List deployed assets
          echo "Bundle deployment structure:"
          databricks workspace list /Workspace/Deployments/prod --profile DEFAULT || echo "⚠️ Prod folder not found"
          echo ""
          
          echo "Checking deployed notebooks:"
          echo "Shared folder:"
          databricks workspace list /Workspace/Deployments/prod/files/src/shared --profile DEFAULT || echo "⚠️ Shared folder not found at expected location"
          echo ""
          
          if [ "${{ github.event.inputs.use_case }}" = "all" ]; then
            echo "UseCase-1:"
            databricks workspace list /Workspace/Deployments/prod/files/src/usecase-1 --profile DEFAULT || echo "⚠️ usecase-1 not found"
            echo ""
            echo "UseCase-2:"
            databricks workspace list /Workspace/Deployments/prod/files/src/usecase-2 --profile DEFAULT || echo "⚠️ usecase-2 not found"
          elif [ "${{ github.event.inputs.use_case }}" = "usecase-1" ]; then
            echo "UseCase-1:"
            databricks workspace list /Workspace/Deployments/prod/files/src/usecase-1 --profile DEFAULT || echo "⚠️ usecase-1 not found"
          elif [ "${{ github.event.inputs.use_case }}" = "usecase-2" ]; then
            echo "UseCase-2:"
            databricks workspace list /Workspace/Deployments/prod/files/src/usecase-2 --profile DEFAULT || echo "⚠️ usecase-2 not found"
          fi
          echo ""
          
          echo "Checking cluster configuration..."
          databricks clusters list --profile DEFAULT | grep prod-cluster || echo "⚠️ prod-cluster not configured"
      
      - name: Run Production Validation Tests
        env:
          DATABRICKS_HOST: ${{ vars.DATABRICKS_HOST }}
          DATABRICKS_TOKEN: ${{ secrets.DATABRICKS_TOKEN }}
        run: |
          # Run validation script with full validation
          python devops/scripts/validate_deployment.py \
            --env prod \
            --host "$DATABRICKS_HOST" \
            --use-case "${{ github.event.inputs.use_case }}" || echo "⚠️ Validation script not found or failed"
      
      - name: Deployment Summary
        run: |
          echo "## 🚀 PRODUCTION Deployment Completed" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Deployment Information" >> $GITHUB_STEP_SUMMARY
          echo "- **Environment:** PRODUCTION" >> $GITHUB_STEP_SUMMARY
          echo "- **Use Case:** ${{ github.event.inputs.use_case }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Change Ticket:** ${{ github.event.inputs.change_ticket }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Status:** ✅ Success" >> $GITHUB_STEP_SUMMARY
          echo "- **Deployed by:** ${{ github.actor }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Deployed Structure" >> $GITHUB_STEP_SUMMARY
          echo "Bundle deployment creates the following structure:" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ \`/Workspace/Deployments/prod/files/src/shared\`" >> $GITHUB_STEP_SUMMARY
          
          USE_CASE="${{ github.event.inputs.use_case }}"
          if [ "$USE_CASE" = "all" ]; then
            echo "- ✅ \`/Workspace/Deployments/prod/files/src/usecase-1\`" >> $GITHUB_STEP_SUMMARY
            echo "- ✅ \`/Workspace/Deployments/prod/files/src/usecase-2\`" >> $GITHUB_STEP_SUMMARY
          elif [ "$USE_CASE" = "usecase-1" ]; then
            echo "- ✅ \`/Workspace/Deployments/prod/files/src/usecase-1\`" >> $GITHUB_STEP_SUMMARY
          elif [ "$USE_CASE" = "usecase-2" ]; then
            echo "- ✅ \`/Workspace/Deployments/prod/files/src/usecase-2\`" >> $GITHUB_STEP_SUMMARY
          fi
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Backup Location" >> $GITHUB_STEP_SUMMARY
          echo "${{ env.backup_path }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Post-Deployment Actions" >> $GITHUB_STEP_SUMMARY
          echo "1. Monitor application performance" >> $GITHUB_STEP_SUMMARY
          echo "2. Check for any errors in logs" >> $GITHUB_STEP_SUMMARY
          echo "3. Validate business functionality" >> $GITHUB_STEP_SUMMARY
          echo "4. Update change ticket with deployment details" >> $GITHUB_STEP_SUMMARY
      
      - name: Handle Deployment Failure
        if: failure()
        run: |
          echo "## ❌ PROD Deployment Failed" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Failure Details" >> $GITHUB_STEP_SUMMARY
          echo "- **Change Ticket:** ${{ github.event.inputs.change_ticket }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Use Case:** ${{ github.event.inputs.use_case }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Triggered by:** ${{ github.actor }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Rollback Instructions" >> $GITHUB_STEP_SUMMARY
          echo "${{ github.event.inputs.rollback_plan }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Action Required" >> $GITHUB_STEP_SUMMARY
          echo "1. Check the workflow logs" >> $GITHUB_STEP_SUMMARY
          echo "2. Identify the failure cause" >> $GITHUB_STEP_SUMMARY
          echo "3. Execute rollback if needed" >> $GITHUB_STEP_SUMMARY
          echo "4. Update the change ticket" >> $GITHUB_STEP_SUMMARY