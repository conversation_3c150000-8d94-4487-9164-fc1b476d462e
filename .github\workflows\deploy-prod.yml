name: Deploy to PROD

on:
  workflow_dispatch:
    inputs:
      use_case:
        description: 'Select use case to deploy (shared folder is always included)'
        required: true
        type: choice
        options:
          - IPV
          - MOS
          - XVA
          - all
      change_ticket:
        description: 'Change ticket number (required)'
        required: true
        type: string
      deployment_reason:
        description: 'Reason for deployment'
        required: true
        type: string
      rollback_plan:
        description: 'Rollback plan if deployment fails'
        required: true
        type: string
        default: 'Revert to previous version using Git tags'
      notify_stakeholders:
        description: 'Send notifications to stakeholders'
        required: false
        type: boolean
        default: false

env:
  REQUESTS_CA_BUNDLE: /etc/ssl/certs/ca-certificates.crt

jobs:
  pre-deployment-checks:
    runs-on: itdev-ubuntu-latest
    name: Pre-Deployment Validation
    permissions:
      contents: read
      actions: read
    outputs:
      proceed: ${{ steps.validation.outputs.proceed }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Add MBCL FW cert
        run: |
          echo "$MBCL_FW_CA_CERT" > mbcl_fw_ca_cert.crt
          sudo cp mbcl_fw_ca_cert.crt /usr/local/share/ca-certificates/firewall-ca.mcrm.net.crt
          sudo update-ca-certificates
        env:
          MBCL_FW_CA_CERT: ${{ secrets.MBCL_FW_CA_CERT }}
      
      - name: Validate inputs
        id: validation
        run: |
          # Just ensure change ticket is not empty
          if [ -z "${{ github.event.inputs.change_ticket }}" ]; then
            echo "❌ Change ticket is required"
            echo "proceed=false" >> $GITHUB_OUTPUT
            exit 1
          fi
          
          # Check if deployment reason is provided
          if [ -z "${{ github.event.inputs.deployment_reason }}" ]; then
            echo "❌ Deployment reason is required"
            echo "proceed=false" >> $GITHUB_OUTPUT
            exit 1
          fi
          
          echo "✅ All validations passed"
          echo "✅ Change ticket: ${{ github.event.inputs.change_ticket }}"
          echo "✅ Deployment reason: ${{ github.event.inputs.deployment_reason }}"
          echo "proceed=true" >> $GITHUB_OUTPUT
      
      - name: Check TEST deployment status
        run: |
          echo "⚠️ WARNING: Ensure the selected use case has been tested in TEST environment"
          echo "Use Case: ${{ github.event.inputs.use_case }}"
          echo "Change Ticket: ${{ github.event.inputs.change_ticket }}"

  deploy-to-prod:
    needs: pre-deployment-checks
    if: needs.pre-deployment-checks.outputs.proceed == 'true'
    runs-on: itdev-ubuntu-latest
    name: Deploy to PROD Environment
    environment: production
    permissions:
      contents: read
      actions: read
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Add MBCL FW cert
        run: |
          echo "$MBCL_FW_CA_CERT" > mbcl_fw_ca_cert.crt
          sudo cp mbcl_fw_ca_cert.crt /usr/local/share/ca-certificates/firewall-ca.mcrm.net.crt
          sudo update-ca-certificates
        env:
          MBCL_FW_CA_CERT: ${{ secrets.MBCL_FW_CA_CERT }}
      
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
      
      - name: Install Python dependencies
        run: |
          pip install requests
      
      - name: Install Databricks CLI
        run: |
          echo "Installing Databricks CLI..."
          
          # Try the official installer
          curl -fsSL https://raw.githubusercontent.com/databricks/setup-cli/main/install.sh | sh
          
          # The installer puts the CLI in /usr/local/bin/databricks
          # Verify installation
          if databricks --version; then
            echo "✅ Databricks CLI installation verified"
          else
            echo "❌ Databricks CLI installation failed"
            exit 1
          fi
      
      - name: Create deployment backup
        env:
          DATABRICKS_HOST: ${{ vars.DATABRICKS_HOST }}
          DATABRICKS_TOKEN: ${{ secrets.DATABRICKS_TOKEN }}
        run: |
          echo "Creating backup of current PROD deployment..."
          
          BACKUP_DATE=$(date +'%Y%m%d-%H%M%S')
          BACKUP_PATH="/Workspace/Deployments/prod-backups/${BACKUP_DATE}"
          LOCAL_BACKUP_DIR="./prod-backup-${BACKUP_DATE}"
          
          # Create local backup directory
          mkdir -p "${LOCAL_BACKUP_DIR}"
          
          echo "Attempting to backup existing deployment..."
          
          # Use --profile DEFAULT to bypass bundle configuration
          # Backup shared folder
          echo "Backing up shared folder..."
          databricks workspace export-dir \
            /Workspace/Deployments/prod/files/src/shared \
            "${LOCAL_BACKUP_DIR}/shared" \
            --profile DEFAULT \
            --overwrite 2>/dev/null || echo "No shared folder found to backup"
          
          # Backup IPV
          echo "Backing up IPV..."
          databricks workspace export-dir \
            /Workspace/Deployments/prod/files/src/IPV \
            "${LOCAL_BACKUP_DIR}/IPV" \
            --profile DEFAULT \
            --overwrite 2>/dev/null || echo "No IPV found to backup"

          # Backup MOS
          echo "Backing up MOS..."
          databricks workspace export-dir \
            /Workspace/Deployments/prod/files/src/MOS \
            "${LOCAL_BACKUP_DIR}/MOS" \
            --profile DEFAULT \
            --overwrite 2>/dev/null || echo "No MOS found to backup"

          # Backup XVA
          echo "Backing up XVA..."
          databricks workspace export-dir \
            /Workspace/Deployments/prod/files/src/XVA \
            "${LOCAL_BACKUP_DIR}/XVA" \
            --profile DEFAULT \
            --overwrite 2>/dev/null || echo "No XVA found to backup"
          
          # Create backup directory in workspace
          databricks workspace mkdirs "${BACKUP_PATH}/src" --profile DEFAULT || true
          
          # Upload all backed up content to workspace backup location
          if [ -d "${LOCAL_BACKUP_DIR}/shared" ]; then
            echo "Uploading shared backup..."
            databricks workspace import-dir \
              "${LOCAL_BACKUP_DIR}/shared" \
              "${BACKUP_PATH}/src/shared" \
              --profile DEFAULT \
              --overwrite || echo "Failed to upload shared backup"
          fi
          
          if [ -d "${LOCAL_BACKUP_DIR}/IPV" ]; then
            echo "Uploading IPV backup..."
            databricks workspace import-dir \
              "${LOCAL_BACKUP_DIR}/IPV" \
              "${BACKUP_PATH}/src/IPV" \
              --profile DEFAULT \
              --overwrite || echo "Failed to upload IPV backup"
          fi

          if [ -d "${LOCAL_BACKUP_DIR}/MOS" ]; then
            echo "Uploading MOS backup..."
            databricks workspace import-dir \
              "${LOCAL_BACKUP_DIR}/MOS" \
              "${BACKUP_PATH}/src/MOS" \
              --profile DEFAULT \
              --overwrite || echo "Failed to upload MOS backup"
          fi

          if [ -d "${LOCAL_BACKUP_DIR}/XVA" ]; then
            echo "Uploading XVA backup..."
            databricks workspace import-dir \
              "${LOCAL_BACKUP_DIR}/XVA" \
              "${BACKUP_PATH}/src/XVA" \
              --profile DEFAULT \
              --overwrite || echo "Failed to upload XVA backup"
          fi
          
          # List backup contents for verification
          echo "Backup contents:"
          databricks workspace list "${BACKUP_PATH}/src" --profile DEFAULT 2>/dev/null || echo "No backup created"
          
          # Clean up local backup directory
          rm -rf "${LOCAL_BACKUP_DIR}"
          
          echo "Backup process completed: ${BACKUP_PATH}"
          echo "backup_path=${BACKUP_PATH}" >> $GITHUB_ENV
      
      - name: Display Deployment Plan
        run: |
          echo "================================================"
          echo "PRODUCTION DEPLOYMENT PLAN"
          echo "================================================"
          echo "Environment: PRODUCTION"
          echo "Use Case: ${{ github.event.inputs.use_case }}"
          echo "Change Ticket: ${{ github.event.inputs.change_ticket }}"
          echo "Triggered by: ${{ github.actor }}"
          echo "Reason: ${{ github.event.inputs.deployment_reason }}"
          echo "Rollback Plan: ${{ github.event.inputs.rollback_plan }}"
          echo ""
          echo "Folders to deploy:"
          echo "  ✓ src/shared (always deployed)"
          
          if [ "${{ github.event.inputs.use_case }}" = "all" ]; then
            echo "  ✓ src/IPV"
            echo "  ✓ src/MOS"
            echo "  ✓ src/XVA"
          else
            echo "  ✓ src/${{ github.event.inputs.use_case }}"
          fi
          
          echo ""
          echo "Backup created at: ${{ env.backup_path }}"
          echo "================================================"
      
      
      - name: Deploy Selected Use Cases to PROD
        env:
          DATABRICKS_HOST: ${{ vars.DATABRICKS_HOST }}
          DATABRICKS_TOKEN: ${{ secrets.DATABRICKS_TOKEN }}
        run: |
          echo "Deploying selected use cases to PROD..."
          echo "Use Case: ${{ github.event.inputs.use_case }}"
          
          # Create temporary directory for deployment
          TEMP_DIR="./temp-deploy-$(date +%s)"
          mkdir -p "$TEMP_DIR"
          
          # Always copy shared folder
          cp -r src/shared "$TEMP_DIR/"
          
          # Copy selected use case(s)
          if [ "${{ github.event.inputs.use_case }}" = "all" ]; then
            echo "Deploying ALL use cases..."
            cp -r src/IPV "$TEMP_DIR/"
            cp -r src/MOS "$TEMP_DIR/"
            cp -r src/XVA "$TEMP_DIR/"
          else
            echo "Deploying ${{ github.event.inputs.use_case }} only..."
            cp -r src/${{ github.event.inputs.use_case }} "$TEMP_DIR/"
          fi
          
          # Ensure deployment directories exist
          databricks workspace mkdirs /Workspace/Deployments/prod/files/src --profile DEFAULT || true
          
          # Upload shared folder
          echo "Uploading shared folder..."
          databricks workspace import-dir \
            "$TEMP_DIR/shared" \
            "/Workspace/Deployments/prod/files/src/shared" \
            --profile DEFAULT \
            --overwrite
          
          # Upload use cases based on selection
          if [ "${{ github.event.inputs.use_case }}" = "all" ]; then
            echo "Uploading IPV..."
            databricks workspace import-dir \
              "$TEMP_DIR/IPV" \
              "/Workspace/Deployments/prod/files/src/IPV" \
              --profile DEFAULT \
              --overwrite

            echo "Uploading MOS..."
            databricks workspace import-dir \
              "$TEMP_DIR/MOS" \
              "/Workspace/Deployments/prod/files/src/MOS" \
              --profile DEFAULT \
              --overwrite

            echo "Uploading XVA..."
            databricks workspace import-dir \
              "$TEMP_DIR/XVA" \
              "/Workspace/Deployments/prod/files/src/XVA" \
              --profile DEFAULT \
              --overwrite
          elif [ "${{ github.event.inputs.use_case }}" = "IPV" ]; then
            echo "Uploading IPV..."
            databricks workspace import-dir \
              "$TEMP_DIR/IPV" \
              "/Workspace/Deployments/prod/files/src/IPV" \
              --profile DEFAULT \
              --overwrite
          elif [ "${{ github.event.inputs.use_case }}" = "MOS" ]; then
            echo "Uploading MOS..."
            databricks workspace import-dir \
              "$TEMP_DIR/MOS" \
              "/Workspace/Deployments/prod/files/src/MOS" \
              --profile DEFAULT \
              --overwrite
          elif [ "${{ github.event.inputs.use_case }}" = "XVA" ]; then
            echo "Uploading XVA..."
            databricks workspace import-dir \
              "$TEMP_DIR/XVA" \
              "/Workspace/Deployments/prod/files/src/XVA" \
              --profile DEFAULT \
              --overwrite
          fi
          
          # Clean up temp directory
          rm -rf "$TEMP_DIR"
          
          # Deploy cluster configuration
          if [ "${{ github.event.inputs.use_case }}" = "IPV" ]; then
            TARGET="prod-ipv"
          elif [ "${{ github.event.inputs.use_case }}" = "MOS" ]; then
            TARGET="prod-mos"
          elif [ "${{ github.event.inputs.use_case }}" = "XVA" ]; then
            TARGET="prod-xva"
          else
            TARGET="prod-all"
          fi
          
          echo "Deploying cluster configuration with target: $TARGET"
          echo "Running bundle validate..."
          databricks bundle validate -t $TARGET || echo "Validation warnings (continuing)"
          
          echo "Running bundle deploy for cluster resources..."
          # Since we already uploaded files manually, just deploy the resources (clusters)
          # The bundle deploy will skip file sync since files already exist
          databricks bundle deploy -t $TARGET --auto-approve
          
          echo "Verifying cluster creation..."
          databricks clusters list --profile DEFAULT | grep prod-cluster || echo "Cluster may take time to appear"
          
          echo "✅ Deployment completed to PROD"
      
      - name: Verify PROD Deployment
        env:
          DATABRICKS_HOST: ${{ vars.DATABRICKS_HOST }}
          DATABRICKS_TOKEN: ${{ secrets.DATABRICKS_TOKEN }}
        run: |
          echo "Verifying PROD deployment..."
          echo "----------------------------------------"
          
          # Determine the target for verification
          if [ "${{ github.event.inputs.use_case }}" = "IPV" ]; then
            TARGET="prod-ipv"
          elif [ "${{ github.event.inputs.use_case }}" = "MOS" ]; then
            TARGET="prod-mos"
          elif [ "${{ github.event.inputs.use_case }}" = "XVA" ]; then
            TARGET="prod-xva"
          else
            TARGET="prod-all"
          fi
          
          # List deployed assets
          echo "Bundle deployment structure:"
          databricks workspace list /Workspace/Deployments/prod --profile DEFAULT || echo "⚠️ Prod folder not found"
          echo ""
          
          echo "Checking deployed notebooks:"
          echo "Shared folder:"
          databricks workspace list /Workspace/Deployments/prod/files/src/shared --profile DEFAULT || echo "⚠️ Shared folder not found at expected location"
          echo ""
          
          if [ "${{ github.event.inputs.use_case }}" = "all" ]; then
            echo "IPV:"
            databricks workspace list /Workspace/Deployments/prod/files/src/IPV --profile DEFAULT || echo "⚠️ IPV not found"
            echo ""
            echo "MOS:"
            databricks workspace list /Workspace/Deployments/prod/files/src/MOS --profile DEFAULT || echo "⚠️ MOS not found"
            echo ""
            echo "XVA:"
            databricks workspace list /Workspace/Deployments/prod/files/src/XVA --profile DEFAULT || echo "⚠️ XVA not found"
          elif [ "${{ github.event.inputs.use_case }}" = "IPV" ]; then
            echo "IPV:"
            databricks workspace list /Workspace/Deployments/prod/files/src/IPV --profile DEFAULT || echo "⚠️ IPV not found"
          elif [ "${{ github.event.inputs.use_case }}" = "MOS" ]; then
            echo "MOS:"
            databricks workspace list /Workspace/Deployments/prod/files/src/MOS --profile DEFAULT || echo "⚠️ MOS not found"
          elif [ "${{ github.event.inputs.use_case }}" = "XVA" ]; then
            echo "XVA:"
            databricks workspace list /Workspace/Deployments/prod/files/src/XVA --profile DEFAULT || echo "⚠️ XVA not found"
          fi
          echo ""
          
          echo "Checking cluster configuration..."
          databricks clusters list --profile DEFAULT | grep prod-cluster || echo "⚠️ prod-cluster not configured"
      
      - name: Run Production Validation Tests
        env:
          DATABRICKS_HOST: ${{ vars.DATABRICKS_HOST }}
          DATABRICKS_TOKEN: ${{ secrets.DATABRICKS_TOKEN }}
        run: |
          # Run validation script with full validation
          python devops/scripts/validate_deployment.py \
            --env prod \
            --host "$DATABRICKS_HOST" \
            --use-case "${{ github.event.inputs.use_case }}" || echo "⚠️ Validation script not found or failed"
      
      - name: Deployment Summary
        run: |
          echo "## 🚀 PRODUCTION Deployment Completed" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Deployment Information" >> $GITHUB_STEP_SUMMARY
          echo "- **Environment:** PRODUCTION" >> $GITHUB_STEP_SUMMARY
          echo "- **Use Case:** ${{ github.event.inputs.use_case }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Change Ticket:** ${{ github.event.inputs.change_ticket }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Status:** ✅ Success" >> $GITHUB_STEP_SUMMARY
          echo "- **Deployed by:** ${{ github.actor }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Deployed Structure" >> $GITHUB_STEP_SUMMARY
          echo "Bundle deployment creates the following structure:" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ \`/Workspace/Deployments/prod/files/src/shared\`" >> $GITHUB_STEP_SUMMARY
          
          USE_CASE="${{ github.event.inputs.use_case }}"
          if [ "$USE_CASE" = "all" ]; then
            echo "- ✅ \`/Workspace/Deployments/prod/files/src/IPV\`" >> $GITHUB_STEP_SUMMARY
            echo "- ✅ \`/Workspace/Deployments/prod/files/src/MOS\`" >> $GITHUB_STEP_SUMMARY
            echo "- ✅ \`/Workspace/Deployments/prod/files/src/XVA\`" >> $GITHUB_STEP_SUMMARY
          elif [ "$USE_CASE" = "IPV" ]; then
            echo "- ✅ \`/Workspace/Deployments/prod/files/src/IPV\`" >> $GITHUB_STEP_SUMMARY
          elif [ "$USE_CASE" = "MOS" ]; then
            echo "- ✅ \`/Workspace/Deployments/prod/files/src/MOS\`" >> $GITHUB_STEP_SUMMARY
          elif [ "$USE_CASE" = "XVA" ]; then
            echo "- ✅ \`/Workspace/Deployments/prod/files/src/XVA\`" >> $GITHUB_STEP_SUMMARY
          fi
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Backup Location" >> $GITHUB_STEP_SUMMARY
          echo "${{ env.backup_path }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Post-Deployment Actions" >> $GITHUB_STEP_SUMMARY
          echo "1. Monitor application performance" >> $GITHUB_STEP_SUMMARY
          echo "2. Check for any errors in logs" >> $GITHUB_STEP_SUMMARY
          echo "3. Validate business functionality" >> $GITHUB_STEP_SUMMARY
          echo "4. Update change ticket with deployment details" >> $GITHUB_STEP_SUMMARY
      
      - name: Handle Deployment Failure
        if: failure()
        run: |
          echo "## ❌ PROD Deployment Failed" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Failure Details" >> $GITHUB_STEP_SUMMARY
          echo "- **Change Ticket:** ${{ github.event.inputs.change_ticket }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Use Case:** ${{ github.event.inputs.use_case }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Triggered by:** ${{ github.actor }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Rollback Instructions" >> $GITHUB_STEP_SUMMARY
          echo "${{ github.event.inputs.rollback_plan }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Action Required" >> $GITHUB_STEP_SUMMARY
          echo "1. Check the workflow logs" >> $GITHUB_STEP_SUMMARY
          echo "2. Identify the failure cause" >> $GITHUB_STEP_SUMMARY
          echo "3. Execute rollback if needed" >> $GITHUB_STEP_SUMMARY
          echo "4. Update the change ticket" >> $GITHUB_STEP_SUMMARY