{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "45b144bb-7bb1-43a0-971c-3c98f139e987", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# --- Widgets ---\n", "dbutils.widgets.text(\"env\", \"\")\n", "dbutils.widgets.text(\"entity\", \"\")\n", "\n", "env = dbutils.widgets.get(\"env\").lower()\n", "entity = dbutils.widgets.get(\"entity\").lower()"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "22cdd459-d206-40b1-aa9c-63ec45bd897b", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Query the scope of Azure Key Vault and secrets\n", "key_vault_scope = \"akv_dataplatform\"\n", "\n", "oracle_username = dbutils.secrets.get(scope=\"akv_dataplatform\", key=\"oracle-midas-username\")\n", "oracle_password = dbutils.secrets.get(scope=\"akv_dataplatform\", key=\"oracle-midas-password\")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "2f9f48c5-4d7e-4975-afd4-771ac86aa403", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["from __future__ import annotations\n", "import os, re, time, logging\n", "from dataclasses import dataclass\n", "from datetime import datetime, timedelta\n", "\n", "import pandas as pd\n", "from pyspark.sql import SparkSession, DataFrame\n", "from pyspark.sql.functions import current_timestamp\n", "from pyspark.sql.types import *\n", "from delta.tables import DeltaTable\n"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "fd978e91-2a59-4b3c-995c-2c8c5314a53c", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["@dataclass\n", "class Config:\n", "    env: str\n", "    entity: str\n", "    jdbc_url: str\n", "    jdbc_user: str = oracle_username      # set via secret / widget\n", "    jdbc_pwd:  str = oracle_password      # set via secret / widget\n", "\n", "    @property\n", "    def jdbc_opts(self):\n", "        return {\n", "            \"url\":       self.jdbc_url,\n", "            \"user\":      self.jdbc_user,\n", "            \"password\":  self.jdbc_pwd,\n", "            \"fetchsize\": \"1000\",\n", "            \"oracle.net.CONNECT_TIMEOUT\": \"10000\",\n", "            \"oracle.net.READ_TIMEOUT\":    \"10000\"\n", "        }\n", "\n", "def get_config(env: str, entity: str) -> Config:\n", "    if env == \"dev\":\n", "        # url = \"**************************************************\"\n", "        # $ nslookup mbclenddb201\n", "        # Server:  MBCLENVMDC02.MBCL.com\n", "        # Address:  ***********\n", "        # Name:    mbclenddb201.mbcl.com\n", "        # Address:  ************\n", "        # url = \"jdbc:oracle:thin:@//************:1526/DLN010_ITDEV\"\n", "        url = \"jdbc:oracle:thin:@//************:1534/DLN280_ITDEV\"\n", "    elif env == \"prod\":\n", "        url = \"***********************************************\" # TO DO\n", "    return Config(env=env, entity=entity, jdbc_url=url)\n", "\n", "# ───────────────────────── LOGGING ────────────────────────\n", "_log = logging.getLogger(\"ingest\")\n", "if not _log.handlers:\n", "    h = logging.StreamHandler()\n", "    h.setFormatter(logging.Formatter(\"%(asctime)s [%(levelname)s] %(message)s\",\n", "                                     \"%Y-%m-%d %H:%M:%S\"))\n", "    _log.add<PERSON><PERSON>ler(h)\n", "    _log.setLevel(logging.INFO)"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "56053345-cfac-4a51-9aa8-fdb1bd344e8b", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["def get_oracle_df(cfg: Config, query: str) -> DataFrame:\n", "    try:\n", "        df = spark.read \\\n", "                .format(\"jdbc\") \\\n", "                .option(\"url\", cfg.jdbc_url) \\\n", "                .option(\"query\", query) \\\n", "                .option(\"user\", cfg.jdbc_user) \\\n", "                .option(\"password\", cfg.jdbc_pwd) \\\n", "                .option(\"fetchsize\", \"10000\") \\\n", "                .option(\"driver\", \"oracle.jdbc.driver.OracleDriver\") \\\n", "                .load()\n", "        return df\n", "    except Exception as e:\n", "        print(f\"Error connecting to {cfg.jdbc_url}: {e}\")\n", "        return None"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "56d20d44-ab3c-4e02-a9b5-4cd7cb9bc623", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["def get_first_business_day_of_current_month(reference_date: datetime = None):\n", "    if reference_date is None:\n", "        reference_date = datetime.today()\n", "    first_day = reference_date.replace(day=1).date()\n", "    first_bussiness_day = pd.bdate_range(start=first_day, periods=1)[0].date()\n", "    return first_bussiness_day.strftime('%Y-%m-%d')\n", "\n", "def get_first_business_day_of_next_month(reference_date: datetime = None):\n", "    if reference_date is None:\n", "        reference_date = datetime.today()\n", "    first_day_next_month = (reference_date.replace(day=1) + pd.DateOffset(months=1)).date()\n", "    first_bussiness_day = pd.bdate_range(start=first_day_next_month, periods=1)[0].date()\n", "    return first_bussiness_day.strftime('%Y-%m-%d')\n", "\n", "def calculate_market_date():\n", "    # market date is COB\n", "    today = datetime.today()\n", "    weekday = today.weekday()  # Monday = 0, Sunday = 6\n", "    if weekday < 5:  # Weekday\n", "        market_date = today - <PERSON><PERSON><PERSON>(days=1)\n", "    else:  # Weekend\n", "        market_date = today - <PERSON><PERSON><PERSON>(days=3)\n", "    return market_date.strftime('%Y-%m-%d')\n", "\n", "def get_index_name(commodity_name: str):\n", "    # get the index name list based on commodity\n", "    if commodity_name not in [\"Gas\", \"Power\"]:\n", "        return []\n", "    table_name = f\"mbcl_{env}_bronze.{entity}.bronze_reference_{commodity_name.lower()}\"\n", "    if commodity_name == \"Gas\":\n", "        index_columns = [\"ol_code\", \"gd_code\"]\n", "    elif commodity_name == \"Power\":\n", "        index_columns = [\"ol_code\"]\n", "\n", "    df = spark.table(table_name)\n", "    index_names = []\n", "    for index in index_columns:\n", "        distinct_entries = df.select(index).distinct().collect()\n", "        index_names.extend([row[index] for row in distinct_entries])\n", "    return index_names"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "2041b660-423b-4196-aff0-245f74c0d62d", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["def main(cfg: Config):\n", "    spark = SparkSession.getActiveSession()\n", "    cfg = get_config(env, entity)\n", "    _END_DATE = \"2031-08-01\"\n", "\n", "    commodities = [\"Gas\", \"Power\"]\n", "    for commodity in commodities:\n", "        _log.info(\"Starting the ingestion for commodity: %s\", commodity)\n", "        if commodity == \"Gas\":\n", "            start_date = get_first_business_day_of_next_month()\n", "        elif commodity == \"Power\":\n", "            start_date = get_first_business_day_of_current_month()\n", "\n", "        end_date = pd.to_datetime(_END_DATE).date().strftime('%Y-%m-%d')\n", "        market_date = calculate_market_date()\n", "\n", "        print(f\"{commodity} Market date: {market_date}\")\n", "        print(f\"{commodity} Start date: {start_date}\")\n", "        print(f\"{commodity} End date: {end_date}\")\n", "\n", "        # Define commodity products\n", "        products = get_index_name(commodity)\n", "        products_list = \", \".join(f\"'{gp}'\" for gp in products)\n", "\n", "        # Build SQL query\n", "        sql_query = f\"\"\"\n", "            SELECT \n", "                market_date, contract_date, price, index_name\n", "            FROM \n", "                arch.ol_market_price\n", "            WHERE market_date = TO_DATE('{market_date}', 'YYYY-MM-DD')\n", "                AND contract_date >= TO_DATE('{start_date}', 'YYYY-MM-DD')\n", "                AND contract_date <= TO_DATE('{end_date}', 'YYYY-MM-DD')\n", "                AND index_name IN ({products_list})\n", "                AND EXTRACT(DAY FROM contract_date) = 1\n", "        \"\"\"\n", "\n", "        df = get_oracle_df(cfg, sql_query)\n", "        if df:\n", "            new_rows_count = df.count()\n", "            if new_rows_count == 0:\n", "                print(f\"No new rows to process for commodiy: {commodity}\")\n", "                continue\n", "\n", "            # add timestamp column\n", "            df = df.withColumn(\"ingestion_timestamp\", current_timestamp())\n", "\n", "            # convert column names to lowercase\n", "            df = df.toDF(*[col.lower() for col in df.columns])\n", "            \n", "            df.write.format(\"delta\").mode(\"append\").saveAsTable(f\"mbcl_{env}_bronze.{entity}.bronze_market_price_{commodity.lower()}\")\n", "            _log.info(\"Ingestion for commodity: %s completed\", commodity)\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    if not env or not entity:\n", "        raise ValueError(\"Both 'env' and 'entity' parameters must be provided\")\n", "\n", "    _log.info(\"Starting DB ingestion job for env=%s, entity=%s\", env, entity.upper())\n", "    try:\n", "        cfg = get_config(env, entity)\n", "        main(cfg)\n", "        _log.info(\"Ingestion job completed successfully\")\n", "    except Exception as exc:\n", "        _log.error(\"Job failed: %s\", exc)\n", "        raise"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "inputWidgetPreferences": null, "language": "python", "notebookMetadata": {"pythonIndentUnit": 4}, "notebookName": "db_ingestion_to_bronze", "widgets": {"entity": {"currentValue": "ipv", "nuid": "7da09097-7b85-4b64-a7e9-b1d296085c29", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "", "label": null, "name": "entity", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "", "label": null, "name": "entity", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}, "env": {"currentValue": "dev", "nuid": "72a5703c-4d58-475f-8cde-67797bd10136", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "", "label": null, "name": "env", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "", "label": null, "name": "env", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}