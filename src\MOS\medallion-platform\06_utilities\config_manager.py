# Databricks notebook source
# MAGIC %md
# MAGIC # Configuration Management Framework
# MAGIC Centralized configuration management for the medallion platform

# COMMAND ----------

# MAGIC %run ./environment_config

# COMMAND ----------

import json
from datetime import datetime
from typing import Dict, Any, Optional

class ConfigurationManager:
    """
    Centralized configuration manager that integrates:
    - Environment-specific configurations
    - Pipeline metadata
    - Runtime parameters
    - Execution logging
    """
    
    def __init__(self, environment: str, source_system: str, catalog_override: str = None):
        self.environment = environment
        self.source_system = source_system
        self.catalog_override = catalog_override
        self.env_config_manager = EnvironmentConfigManager()
        self.runtime_config = None
        
    def get_complete_runtime_config(self) -> Dict[str, Any]:
        """Get complete runtime configuration"""
        
        if self.runtime_config is None:
            # Get base configuration from environment manager
            self.runtime_config = self.env_config_manager.get_complete_config(
                self.environment, 
                self.source_system,
                self.catalog_override
            )
            
            # Add pipeline-specific information
            try:
                pipeline_count = self._get_pipeline_count()
                self.runtime_config["runtime_info"]["total_pipelines"] = pipeline_count
            except:
                self.runtime_config["runtime_info"]["total_pipelines"] = 0
        
        return self.runtime_config
    
    def _get_pipeline_count(self) -> int:
        """Get count of active pipelines for source system"""

        try:
            config = self.get_complete_runtime_config()
            utility_catalog = config["databricks"]["catalogs"]["utility"]
            metadata_schema = config["databricks"]["schemas"]["metadata"]

            count_df = spark.sql(f"""
                SELECT COUNT(*) as pipeline_count
                FROM `{utility_catalog}`.`{metadata_schema}`.pipeline_config
                WHERE source_system = '{self.source_system}'
                AND is_active = true
            """)

            return count_df.collect()[0].pipeline_count

        except Exception as e:
            print(f"⚠️ Could not get pipeline count: {str(e)}")
            return 0
    
    def get_pipeline_configs(self, ingestion_type: str = None) -> list:
        """Get pipeline configurations for source system"""

        try:
            config = self.get_complete_runtime_config()
            utility_catalog = config["databricks"]["catalogs"]["utility"]
            metadata_schema = config["databricks"]["schemas"]["metadata"]

            where_clause = f"WHERE source_system = '{self.source_system}' AND is_active = true"
            if ingestion_type:
                where_clause += f" AND ingestion_type = '{ingestion_type}'"

            pipelines_df = spark.sql(f"""
                SELECT *
                FROM `{utility_catalog}`.`{metadata_schema}`.pipeline_config
                {where_clause}
                ORDER BY pipeline_id
            """)

            return pipelines_df.collect()

        except Exception as e:
            print(f"⚠️ Could not load pipeline configs: {str(e)}")
            return []
    
    def log_pipeline_execution(self, pipeline_id: str, status: str, **kwargs) -> bool:
        """Log pipeline execution to metadata table"""
        
        try:
            config = self.get_complete_runtime_config()
            utility_catalog = config["databricks"]["catalogs"]["utility"]
            metadata_schema = config["databricks"]["schemas"]["metadata"]

            # Prepare execution record with proper data types
            from pyspark.sql import Row
            from pyspark.sql.types import StructType, StructField, StringType, IntegerType, DoubleType, TimestampType, DateType

            # Convert datetime objects to proper format
            start_time = kwargs.get("start_time", datetime.now())
            end_time = kwargs.get("end_time", datetime.now())
            ingestion_date = kwargs.get("ingestion_date", datetime.now().date())

            # Ensure proper types
            if isinstance(start_time, str):
                start_time = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
            if isinstance(end_time, str):
                end_time = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
            if isinstance(ingestion_date, str):
                ingestion_date = datetime.strptime(ingestion_date, '%Y-%m-%d').date()

            execution_record = Row(
                run_id=str(kwargs.get("run_id", f"{pipeline_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}")),
                pipeline_id=str(pipeline_id),
                source_table=str(kwargs.get("source_table", "unknown")),
                ingestion_date=ingestion_date,
                start_time=start_time,
                end_time=end_time,
                status=str(status),
                records_read=int(kwargs.get("records_read", 0)),
                records_written=int(kwargs.get("records_written", 0)),
                watermark_value=str(kwargs.get("watermark_value", "")) if kwargs.get("watermark_value") is not None else "",
                error_message=str(kwargs.get("error_message", "")),
                duration_seconds=float(kwargs.get("duration_seconds", 0))
            )

            # Create DataFrame and insert
            execution_df = spark.createDataFrame([execution_record])
            execution_df.write.mode("append").saveAsTable(f"`{utility_catalog}`.`{metadata_schema}`.pipeline_runs")
            
            print(f"✅ Pipeline execution logged: {pipeline_id} - {status}")
            return True
            
        except Exception as e:
            print(f"⚠️ Could not log pipeline execution: {str(e)}")
            return False
    
    def get_execution_history(self, pipeline_id: str = None, limit: int = 10) -> list:
        """Get pipeline execution history"""
        
        try:
            config = self.get_complete_runtime_config()
            utility_catalog = config["databricks"]["catalogs"]["utility"]
            metadata_schema = config["databricks"]["schemas"]["metadata"]

            where_clause = ""
            if pipeline_id:
                where_clause = f"WHERE pipeline_id = '{pipeline_id}'"

            history_df = spark.sql(f"""
                SELECT *
                FROM `{utility_catalog}`.`{metadata_schema}`.pipeline_runs
                {where_clause}
                ORDER BY start_time DESC
                LIMIT {limit}
            """)
            
            return history_df.collect()
            
        except Exception as e:
            print(f"⚠️ Could not get execution history: {str(e)}")
            return []
    
    def validate_environment_access(self) -> bool:
        """Validate access to environment resources"""

        try:
            config = self.get_complete_runtime_config()
            catalogs = config["databricks"]["catalogs"]
            schemas = config["databricks"]["schemas"]

            # Test catalog access for each layer
            for layer, catalog in catalogs.items():
                try:
                    spark.sql(f"USE CATALOG `{catalog}`")
                    print(f"✅ {layer.title()} catalog access: {catalog}")
                except Exception as e:
                    print(f"❌ Cannot access {layer} catalog {catalog}: {e}")
                    return False

            # Test schema access for each layer
            for layer, schema in schemas.items():
                catalog = catalogs.get("utility" if layer == "metadata" else layer)
                try:
                    spark.sql(f"USE SCHEMA `{catalog}`.`{schema}`")
                    print(f"✅ {layer.title()} schema access: {catalog}.{schema}")
                except Exception as e:
                    print(f"⚠️ Schema {catalog}.{schema} may not exist yet: {e}")

            print(f"✅ Environment access validated for {self.environment}")
            return True

        except Exception as e:
            print(f"❌ Environment access validation failed: {str(e)}")
            return False
    
    def get_table_info(self, catalog: str, schema: str, table: str) -> Dict[str, Any]:
        """Get information about a specific table"""

        try:
            table_path = f"`{catalog}`.`{schema}`.{table}"
            
            # Get table info
            table_df = spark.table(table_path)
            record_count = table_df.count()
            
            # Get schema info
            schema_info = table_df.schema.json()
            
            return {
                "table_path": table_path,
                "record_count": record_count,
                "schema": json.loads(schema_info),
                "exists": True
            }
            
        except Exception as e:
            return {
                "table_path": f"`{catalog}`.`{schema}`.{table}",
                "record_count": 0,
                "schema": None,
                "exists": False,
                "error": str(e)
            }
    
    def create_schemas_if_not_exist(self) -> bool:
        """Create schemas if they don't exist in their respective catalogs"""

        try:
            config = self.get_complete_runtime_config()
            catalogs = config["databricks"]["catalogs"]
            schemas = config["databricks"]["schemas"]

            # Create schemas in their respective catalogs
            for layer, schema_name in schemas.items():
                # Determine which catalog to use
                if layer == "metadata":
                    catalog = catalogs["utility"]
                else:
                    catalog = catalogs[layer]

                try:
                    spark.sql(f"CREATE SCHEMA IF NOT EXISTS `{catalog}`.`{schema_name}`")
                    print(f"✅ Schema ready: {catalog}.{schema_name}")
                except Exception as schema_error:
                    print(f"⚠️ Could not create schema {catalog}.{schema_name}: {schema_error}")

            return True

        except Exception as e:
            print(f"❌ Could not create schemas: {str(e)}")
            return False

def create_config_manager(environment: str, source_system: str, catalog_override: str = None) -> ConfigurationManager:
    """Factory function to create configuration manager"""
    
    # Handle catalog override from widgets if available
    if not catalog_override:
        try:
            catalog_override = dbutils.widgets.get("catalog_name")
        except:
            catalog_override = None
    
    return ConfigurationManager(environment, source_system, catalog_override)

# COMMAND ----------

print("✅ Configuration Management Framework loaded successfully!")
