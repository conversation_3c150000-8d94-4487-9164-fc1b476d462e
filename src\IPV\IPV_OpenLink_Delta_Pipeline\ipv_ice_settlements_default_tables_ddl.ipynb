{"cells": [{"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "fa201e0e-b796-4ec9-b595-b6a1eb9b1fbf", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["# Setup\n"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "85305472-203e-4908-b64b-ae65e91520e8", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["dbutils.widgets.text(\"ipv_silver_catalog\", \"mbcl_dev_silver\")\n", "dbutils.widgets.text(\"ipv_silver_schema\", \"ipv\")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "9c9ec971-9304-4448-be2d-2a5b83417279", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["ipv_silver_catalog = dbutils.widgets.get(\"ipv_silver_catalog\")\n", "ipv_silver_schema = dbutils.widgets.get(\"ipv_silver_schema\")"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "75f7eb1c-09d2-4fd6-8f2d-b61ef9435f94", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["# Create silver watermark table for incremental ingestion from bronze layer"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "417563b7-f2af-4b17-96da-70665cb55aad", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["spark.sql(f\"\"\"\n", "    CREATE TABLE IF NOT EXISTS {ipv_silver_catalog}.{ipv_silver_schema}.silver_watermark (\n", "        pipeline_name STRING,\n", "        last_processed TIMESTAMP\n", "    ) USING DELTA;\n", "\"\"\")\n", "\n", "spark.sql(f\"\"\"\n", "    CREATE TABLE IF NOT EXISTS {ipv_silver_catalog}.{ipv_silver_schema}.silver_availability (\n", "        table_name STRING,\n", "        commodity STRING,\n", "        code STRING,\n", "        strip_date DATE,\n", "        trade_date DATE,\n", "        records BIGINT,\n", "        first_arrival_ts TIMESTAMP,\n", "        last_arrival_ts TIMESTAMP,\n", "        PRIMARY KEY (table_name, code, strip_date, trade_date)\n", "    ) USING DELTA;\n", "\"\"\")\n", "\n", "spark.sql(f\"\"\"\n", "    CREATE TABLE IF NOT EXISTS {ipv_silver_catalog}.{ipv_silver_schema}.silver_ipv_difference_processed_keys (\n", "        commodity STRING,\n", "        code STRING,\n", "        strip_date DATE,\n", "        trade_date DATE,\n", "        processed_ts TIMESTAMP\n", "    ) USING DELTA;\n", "\"\"\")\n", "\n", "spark.sql(f\"\"\"\n", "    CREATE TABLE IF NOT EXISTS {ipv_silver_catalog}.{ipv_silver_schema}.silver_ipv_difference_joined \n", "    USING DELTA\n", "    AS SELECT\n", "        CAST(NULL AS STRING) AS commodity,\n", "        CAST(NULL AS STRING) AS code,\n", "        CAST(NULL AS DATE) AS strip_date,\n", "        CAST(NULL AS DATE) AS trade_date\n", "    WHERE 1=0;         \n", "\"\"\")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "70b31d29-a7d7-4866-9f57-6e2f931dca62", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%sql\n", "drop table mbcl_dev_silver.ipv.silver_ipv_difference_joined"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "inputWidgetPreferences": null, "language": "python", "notebookMetadata": {"mostRecentlyExecutedCommandWithImplicitDF": {"commandId": 7958978754207918, "dataframes": ["_sqldf"]}, "pythonIndentUnit": 4}, "notebookName": "ipv_ice_settlements_default_tables_ddl", "widgets": {"ipv_silver_catalog": {"currentValue": "mbcl_dev_silver", "nuid": "566f4be2-98d1-4dbe-9a66-c5ada3371f19", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "mbcl_dev_silver", "label": null, "name": "ipv_silver_catalog", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "mbcl_dev_silver", "label": null, "name": "ipv_silver_catalog", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}, "ipv_silver_schema": {"currentValue": "ipv", "nuid": "cc5727b5-7272-44e9-b902-6d8fcd19dab1", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "ipv", "label": null, "name": "ipv_silver_schema", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "ipv", "label": null, "name": "ipv_silver_schema", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}