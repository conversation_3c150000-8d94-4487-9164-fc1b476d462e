{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "98f530ff-cc3c-4320-8b4a-c7e41943cf1f", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["from pyspark.sql import types as T"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "37c3ed9f-dda5-4bd8-a8fa-7d6f78133c6a", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["reference_power_schema = T.StructType([\n", "    <PERSON><PERSON>('Zone', T.<PERSON>(), True),\n", "    <PERSON><PERSON>('Region', T<PERSON>(), True),\n", "    <PERSON><PERSON>('Location/Hub', T.<PERSON>(), True),\n", "    <PERSON><PERSON>('ice_code', T.<PERSON>(), True),\n", "    <PERSON><PERSON>('OL Code', T.<PERSON>(), True),\n", "    <PERSON><PERSON>('pk', <PERSON><PERSON>(), True),\n", "    <PERSON><PERSON>('op', <PERSON><PERSON>(), True),\n", "    <PERSON><PERSON>('da', <PERSON><PERSON>(), True),\n", "    <PERSON><PERSON>('rt', <PERSON><PERSON>(), True),\n", "])\n", "\n", "reference_gas_schema = T.StructType([\n", "    <PERSON><PERSON>('Region', T<PERSON>(), True),\n", "    <PERSON><PERSON>('Location/Hub', T.<PERSON>(), True),\n", "    <PERSON><PERSON>('ICE Code', T.<PERSON>(), True),\n", "    <PERSON><PERSON>('OL Code', T.<PERSON>(), True),\n", "    <PERSON><PERSON>('GD Code', T.<PERSON>(), True),\n", "])\n", "\n", "reference_regions_schema = T.StructType([\n", "    <PERSON><PERSON>('WEST PK', T.<PERSON>(), True),\n", "    <PERSON><PERSON>('WEST OP', T.<PERSON>(), True),\n", "    <PERSON><PERSON>('EAST PK DA', T.StringType(), True),\n", "    <PERSON><PERSON>('EAST PK RT', T.StringType(), True),\n", "    <PERSON><PERSON>('EAST OP DA', T.StringType(), True),\n", "    <PERSON><PERSON>('EAST OP RT', T.StringType(), True),\n", "])\n", "\n", "settlement_prices_power_schema = T.StructType([\n", "    <PERSON><PERSON>('TRADE DATE', T.StringType(), True),\n", "    <PERSON><PERSON>('HUB', T.<PERSON>(), True),\n", "    <PERSON><PERSON>('PRODUCT', T.<PERSON>(), True),\n", "    <PERSON><PERSON>('STRIP', T.<PERSON>(), True),\n", "    <PERSON><PERSON>('CONTRACT', T.<PERSON>(), True),\n", "    <PERSON><PERSON>('CONTRACT TYPE', T.StringT<PERSON>(), True),\n", "    <PERSON><PERSON>('STRIKE', T.<PERSON>(), True),\n", "    <PERSON><PERSON>('SETTLEMENT PRICE', T.DoubleType(), True),\n", "    <PERSON><PERSON>('NET CHANGE', T.<PERSON>(), True),\n", "    <PERSON><PERSON>('EXPIRATION DATE', T.StringType(), True),\n", "    <PERSON><PERSON>('PRODUCT_ID', T.In<PERSON>gerT<PERSON>(), True),\n", "])\n", "\n", "settlement_prices_gas_schema = T.StructType([\n", "    <PERSON><PERSON>('TRADE DATE', T.StringType(), True),\n", "    <PERSON><PERSON>('HUB', T.<PERSON>(), True),\n", "    <PERSON><PERSON>('PRODUCT', T.<PERSON>(), True),\n", "    <PERSON><PERSON>('STRIP', T.<PERSON>(), True),\n", "    <PERSON><PERSON>('CONTRACT', T.<PERSON>(), True),\n", "    <PERSON><PERSON>('CONTRACT TYPE', T.StringT<PERSON>(), True),\n", "    <PERSON><PERSON>('STRIKE', T.<PERSON>(), True),\n", "    <PERSON><PERSON>('SETTLEMENT PRICE', T.DoubleType(), True),\n", "    <PERSON><PERSON>('NET CHANGE', T.<PERSON>(), True),\n", "    <PERSON><PERSON>('EXPIRATION DATE', T.StringType(), True),\n", "    <PERSON><PERSON>('PRODUCT_ID', T.In<PERSON>gerT<PERSON>(), True),\n", "])\n", "\n", "quarantine_log_schema = T.StructType([\n", "    <PERSON><PERSON>(\"file_path\", T.StringType(), True),\n", "    <PERSON><PERSON>(\"batch_id\", T<PERSON>(), True),\n", "    <PERSON><PERSON>(\"reason\", <PERSON><PERSON>(), True),\n", "    <PERSON><PERSON>(\"timestamp\", T.TimestampType(), True),\n", "    <PERSON><PERSON>(\"moved\", <PERSON><PERSON>(), True)\n", "])"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "inputWidgetPreferences": null, "language": "python", "notebookMetadata": {"pythonIndentUnit": 4}, "notebookName": "ipv_ice_settlements_constants", "widgets": {}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}