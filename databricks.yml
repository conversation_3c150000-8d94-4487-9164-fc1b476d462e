bundle:
  name: mbcl-dataplatform-cicd

variables:
  use_case:
    description: Which use case to deploy
    default: all

targets:
  # Development target - deploys everything for PR validation
  dev:
    mode: production
    workspace:
      root_path: /Workspace/Deployments/dev
    
    resources:
      clusters:
        dev-cluster:
          cluster_name: dev-cluster
          spark_version: "16.4.x-scala2.13"
          node_type_id: "Standard_DS3_v2"
          driver_node_type_id: "Standard_DS3_v2"
          autotermination_minutes: 60
          enable_elastic_disk: true
          runtime_engine: "PHOTON"
          data_security_mode: "SINGLE_USER"
          autoscale:
            min_workers: 2
            max_workers: 8
          spark_conf:
            "spark.driver.extraClassPath": "/Volumes/mbcl_dev_bronze/default/jdbc/ojdbc8.jar"
            "spark.executor.extraClassPath": "/Volumes/mbcl_dev_bronze/default/jdbc/ojdbc8.jar"
          azure_attributes:
            availability: "ON_DEMAND_AZURE"
          custom_tags:
            Environment: "Development"
            Project: "mbcl-dataplatform"
            Department: "ITDEV"
            Category: "DataBricks"
            System: "Non-Critical"
    
    sync:
      paths:
        - ./src  # Deploy entire src directory for dev
      exclude:
        - "*.pyc"
        - "__pycache__"
        - ".DS_Store"
        - "*.ipynb_checkpoints"

  # TEST targets for selective deployment
  test-xva:
    mode: production
    workspace:
      root_path: /Workspace/Deployments/test
    
    resources:
      clusters:
        test-cluster:
          cluster_name: test-cluster
          spark_version: "16.4.x-scala2.13"
          node_type_id: "Standard_DS3_v2"
          driver_node_type_id: "Standard_DS3_v2"
          autotermination_minutes: 60
          enable_elastic_disk: true
          runtime_engine: "PHOTON"
          data_security_mode: "SINGLE_USER"
          autoscale:
            min_workers: 2
            max_workers: 8
          spark_conf:
            "spark.driver.extraClassPath": "/Volumes/mbcl_test_bronze/default/jdbc/ojdbc8.jar"
            "spark.executor.extraClassPath": "/Volumes/mbcl_test_bronze/default/jdbc/ojdbc8.jar"
          azure_attributes:
            availability: "ON_DEMAND_AZURE"
          custom_tags:
            Environment: "Testing"
            Project: "mbcl-dataplatform"
            Department: "ITDEV"
            Category: "DataBricks"
            System: "Non-Critical"
    
    sync:
      paths:
        - ./src/shared
        - ./src/XVA
      exclude:
        - "*.pyc"
        - "__pycache__"
        - ".DS_Store"
        - "*.ipynb_checkpoints"

  test-mos:
    mode: production
    workspace:
      root_path: /Workspace/Deployments/test
    
    resources:
      clusters:
        test-cluster:
          cluster_name: test-cluster
          spark_version: "16.4.x-scala2.13"
          node_type_id: "Standard_DS3_v2"
          driver_node_type_id: "Standard_DS3_v2"
          autotermination_minutes: 60
          enable_elastic_disk: true
          runtime_engine: "PHOTON"
          data_security_mode: "SINGLE_USER"
          autoscale:
            min_workers: 2
            max_workers: 8
          spark_conf:
            "spark.driver.extraClassPath": "/Volumes/mbcl_test_bronze/default/jdbc/ojdbc8.jar"
            "spark.executor.extraClassPath": "/Volumes/mbcl_test_bronze/default/jdbc/ojdbc8.jar"
          azure_attributes:
            availability: "ON_DEMAND_AZURE"
          custom_tags:
            Environment: "Testing"
            Project: "mbcl-dataplatform"
            Department: "ITDEV"
            Category: "DataBricks"
            System: "Non-Critical"
    
    sync:
      paths:
        - ./src/shared
        - ./src/MOS
      exclude:
        - "*.pyc"
        - "__pycache__"
        - ".DS_Store"
        - "*.ipynb_checkpoints"

  test-ipv:
    mode: production
    workspace:
      root_path: /Workspace/Deployments/test
    
    resources:
      clusters:
        test-cluster:
          cluster_name: test-cluster
          spark_version: "16.4.x-scala2.13"
          node_type_id: "Standard_DS3_v2"
          driver_node_type_id: "Standard_DS3_v2"
          autotermination_minutes: 60
          enable_elastic_disk: true
          runtime_engine: "PHOTON"
          data_security_mode: "SINGLE_USER"
          autoscale:
            min_workers: 2
            max_workers: 8
          spark_conf:
            "spark.driver.extraClassPath": "/Volumes/mbcl_test_bronze/default/jdbc/ojdbc8.jar"
            "spark.executor.extraClassPath": "/Volumes/mbcl_test_bronze/default/jdbc/ojdbc8.jar"
          azure_attributes:
            availability: "ON_DEMAND_AZURE"
          custom_tags:
            Environment: "Testing"
            Project: "mbcl-dataplatform"
            Department: "ITDEV"
            Category: "DataBricks"
            System: "Non-Critical"
    
    sync:
      paths:
        - ./src/shared
        - ./src/IPV
      exclude:
        - "*.pyc"
        - "__pycache__"
        - ".DS_Store"
        - "*.ipynb_checkpoints"

  test-all:
    mode: production
    workspace:
      root_path: /Workspace/Deployments/test
    
    resources:
      clusters:
        test-cluster:
          cluster_name: test-cluster
          spark_version: "16.4.x-scala2.13"
          node_type_id: "Standard_DS3_v2"
          driver_node_type_id: "Standard_DS3_v2"
          autotermination_minutes: 60
          enable_elastic_disk: true
          runtime_engine: "PHOTON"
          data_security_mode: "SINGLE_USER"
          autoscale:
            min_workers: 2
            max_workers: 8
          spark_conf:
            "spark.driver.extraClassPath": "/Volumes/mbcl_test_bronze/default/jdbc/ojdbc8.jar"
            "spark.executor.extraClassPath": "/Volumes/mbcl_test_bronze/default/jdbc/ojdbc8.jar"
          azure_attributes:
            availability: "ON_DEMAND_AZURE"
          custom_tags:
            Environment: "Testing"
            Project: "mbcl-dataplatform"
            Department: "ITDEV"
            Category: "DataBricks"
            System: "Non-Critical"
    
    sync:
      paths:
        - ./src/shared
        - ./src/MOS
        - ./src/IPV
        - ./src/XVA
      exclude:
        - "*.pyc"
        - "__pycache__"
        - ".DS_Store"
        - "*.ipynb_checkpoints"

  # PROD targets for selective deployment
  prod-xva:
    mode: production
    workspace:
      root_path: /Workspace/Deployments/prod

    resources:
      clusters:
        prod-cluster:
          cluster_name: prod-cluster
          spark_version: "16.4.x-scala2.13"
          node_type_id: "Standard_DS3_v2"
          driver_node_type_id: "Standard_DS3_v2"
          autotermination_minutes: 60
          enable_elastic_disk: true
          runtime_engine: "PHOTON"
          data_security_mode: "SINGLE_USER"
          autoscale:
            min_workers: 2
            max_workers: 8
          spark_conf:
            "spark.driver.extraClassPath": "/Volumes/mbcl_prod_bronze/default/jdbc/ojdbc8.jar"
            "spark.executor.extraClassPath": "/Volumes/mbcl_prod_bronze/default/jdbc/ojdbc8.jar"
          azure_attributes:
            availability: "ON_DEMAND_AZURE"
          custom_tags:
            Environment: "Production"
            Project: "mbcl-dataplatform"
            Department: "ITDEV"
            Category: "DataBricks"
            System: "Critical"

    sync:
      paths:
        - ./src/shared
        - ./src/XVA
      exclude:
        - "*.pyc"
        - "__pycache__"
        - ".DS_Store"
        - "*.ipynb_checkpoints"

    permissions:
      - level: CAN_VIEW
        group_name: users

  prod-mos:
    mode: production
    workspace:
      root_path: /Workspace/Deployments/prod

    resources:
      clusters:
        prod-cluster:
          cluster_name: prod-cluster
          spark_version: "16.4.x-scala2.13"
          node_type_id: "Standard_DS3_v2"
          driver_node_type_id: "Standard_DS3_v2"
          autotermination_minutes: 60
          enable_elastic_disk: true
          runtime_engine: "PHOTON"
          data_security_mode: "SINGLE_USER"
          autoscale:
            min_workers: 2
            max_workers: 8
          spark_conf:
            "spark.driver.extraClassPath": "/Volumes/mbcl_prod_bronze/default/jdbc/ojdbc8.jar"
            "spark.executor.extraClassPath": "/Volumes/mbcl_prod_bronze/default/jdbc/ojdbc8.jar"
          azure_attributes:
            availability: "ON_DEMAND_AZURE"
          custom_tags:
            Environment: "Production"
            Project: "mbcl-dataplatform"
            Department: "ITDEV"
            Category: "DataBricks"
            System: "Critical"

    sync:
      paths:
        - ./src/shared
        - ./src/MOS
      exclude:
        - "*.pyc"
        - "__pycache__"
        - ".DS_Store"
        - "*.ipynb_checkpoints"

    permissions:
      - level: CAN_VIEW
        group_name: users

  prod-ipv:
    mode: production
    workspace:
      root_path: /Workspace/Deployments/prod

    resources:
      clusters:
        prod-cluster:
          cluster_name: prod-cluster
          spark_version: "16.4.x-scala2.13"
          node_type_id: "Standard_DS3_v2"
          driver_node_type_id: "Standard_DS3_v2"
          autotermination_minutes: 60
          enable_elastic_disk: true
          runtime_engine: "PHOTON"
          data_security_mode: "SINGLE_USER"
          autoscale:
            min_workers: 2
            max_workers: 8
          spark_conf:
            "spark.driver.extraClassPath": "/Volumes/mbcl_prod_bronze/default/jdbc/ojdbc8.jar"
            "spark.executor.extraClassPath": "/Volumes/mbcl_prod_bronze/default/jdbc/ojdbc8.jar"
          azure_attributes:
            availability: "ON_DEMAND_AZURE"
          custom_tags:
            Environment: "Production"
            Project: "mbcl-dataplatform"
            Department: "ITDEV"
            Category: "DataBricks"
            System: "Critical"

    sync:
      paths:
        - ./src/shared
        - ./src/IPV
      exclude:
        - "*.pyc"
        - "__pycache__"
        - ".DS_Store"
        - "*.ipynb_checkpoints"

    permissions:
      - level: CAN_VIEW
        group_name: users

  prod-all:
    mode: production
    workspace:
      root_path: /Workspace/Deployments/prod

    resources:
      clusters:
        prod-cluster:
          cluster_name: prod-cluster
          spark_version: "16.4.x-scala2.13"
          node_type_id: "Standard_DS3_v2"
          driver_node_type_id: "Standard_DS3_v2"
          autotermination_minutes: 60
          enable_elastic_disk: true
          runtime_engine: "PHOTON"
          data_security_mode: "SINGLE_USER"
          autoscale:
            min_workers: 2
            max_workers: 8
          spark_conf:
            "spark.driver.extraClassPath": "/Volumes/mbcl_prod_bronze/default/jdbc/ojdbc8.jar"
            "spark.executor.extraClassPath": "/Volumes/mbcl_prod_bronze/default/jdbc/ojdbc8.jar"
          azure_attributes:
            availability: "ON_DEMAND_AZURE"
          custom_tags:
            Environment: "Production"
            Project: "mbcl-dataplatform"
            Department: "ITDEV"
            Category: "DataBricks"
            System: "Critical"

    sync:
      paths:
        - ./src/shared
        - ./src/MOS
        - ./src/IPV
        - ./src/XVA
      exclude:
        - "*.pyc"
        - "__pycache__"
        - ".DS_Store"
        - "*.ipynb_checkpoints"

    permissions:
      - level: CAN_VIEW
        group_name: users