{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "cdbb848d-78d7-453c-9f81-b24207305ed7", "showTitle": true, "tableResultSettingsMap": {}, "title": "Determine Environment Based on Databricks Workspace URL"}}, "outputs": [], "source": ["workspace_name = spark.conf.get(\"spark.databricks.workspaceUrl\")\n", "print(f\"Workspace name: {workspace_name}\")\n", "\n", "if \"adb-892524365579866.6.azuredatabricks.net\" in workspace_name.lower():\n", "    env = \"dev\"\n", "elif \"adb-1468361750653135.15.azuredatabricks.net\" in workspace_name.lower():\n", "    env = \"test\"\n", "elif \"adb-**************.3.azuredatabricks.net\" in workspace_name.lower():\n", "    env = \"prod\"\n", "else:\n", "    env = \"unknown\"\n", "\n", "print(f\"Environment: {env}\")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "d26d7721-d4d8-41d6-8b2c-013be59141d9", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["if env == \"test\" or env == \"prod\":\n", "\n", "    # Set environment\n", "    catalogs = [f\"mbcl_{env}_utility\", f\"mbcl_{env}_bronze\", f\"mbcl_{env}_silver\", f\"mbcl_{env}_gold\"]\n", "    schema = \"metadata\"\n", "    source_table = \"mbcl_dev_utility.metadata.pipeline_control\"\n", "    target_table = f\"{catalogs[0]}.{schema}.metadata_pipeline_control\"\n", "    external_location = \"abfss://<EMAIL>/\"\n", "\n", "    # Step 3: Create catalogs if not exists\n", "    for catalog in catalogs:\n", "        spark.sql(f\"CREATE CATALOG IF NOT EXISTS {catalog}\")\n", "        print(f\"Catalog '{catalog}' checked/created with external location '{external_location}'.\")\n", "\n", "    # Step 4: Create schema if not exists\n", "    spark.sql(f\"CREATE SCHEMA IF NOT EXISTS {catalogs[0]}.{schema}\")\n", "    print(f\"Schema '{catalogs[0]}.{schema}' checked/created.\")\n", "\n", "\n", "    # Step 6: Create MOS schema if not exists in bronze, silver, and gold catalogs\n", "    for catalog in catalogs[1:]:\n", "        spark.sql(f\"CREATE SCHEMA IF NOT EXISTS {catalog}.MOS\")\n", "        print(f\"Schema '{catalog}.MOS' checked/created.\")\n", "\n", "    # Step 7: Grant admin <NAME_EMAIL>\n", "    for catalog in catalogs:\n", "        spark.sql(f\"GRANT ALL PRIVILEGES ON CATALOG {catalog} TO `<EMAIL>`\")\n", "        print(f\"Granted admin <NAME_EMAIL> on catalog '{catalog}'.\")\n", "\n", "else:\n", "    print(\"This is NOT a test or production workspace. Skipping catalog/table creation.\")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "7526b9f9-a6a8-4f3e-8c04-3c8615a57d94", "showTitle": true, "tableResultSettingsMap": {}, "title": "Drop Bronze, Silver, and Gold Catalogs Data"}}, "outputs": [], "source": ["# %sql\n", "# -- Drop all objects within the mbcl_prod_bronze catalog\n", "# DROP SCHEMA IF EXISTS mbcl_prod_bronze.mos;\n", "# DROP SCHEMA IF EXISTS mbcl_prod_bronze.default;\n", "# -- Drop the mbcl_prod_bronze catalog\n", "# DROP CATALOG IF EXISTS mbcl_prod_bronze;\n", "\n", "# -- Drop all objects within the mbcl_prod_silver catalog\n", "# DROP SCHEMA IF EXISTS mbcl_prod_silver.mos;\n", "# DROP SCHEMA IF EXISTS mbcl_prod_silver.default;\n", "# -- Drop the mbcl_prod_silver catalog\n", "# DROP CATALOG IF EXISTS mbcl_prod_silver;\n", "\n", "# -- Drop all objects within the mbcl_prod_gold catalog\n", "# DROP SCHEMA IF EXISTS  mbcl_prod_gold.mos;\n", "# DROP SCHEMA IF EXISTS  mbcl_prod_gold.default;\n", "# -- Drop the mbcl_prod_gold catalog\n", "# DROP CATALOG IF EXISTS mbcl_prod_gold;"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "7ddb52f8-2bb3-41ee-a1f7-3855e85faacd", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%sql\n", "drop database hive_metastore.mbcl_prod_utility "]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "f8236cfe-7749-4056-8f65-7da94e394e22", "showTitle": true, "tableResultSettingsMap": {}, "title": "Determine Environment and Configure Catalogs and Tables"}}, "outputs": [], "source": ["# Step 1: Get the current workspace name\n", "workspace_name = spark.conf.get(\"spark.databricks.workspaceUrl\", \"unknown\")\n", "print(f\"Workspace name: {workspace_name}\")\n", "\n", "# Step 2: Determine environment\n", "env_mapping = {\n", "    \"adb-892524365579866.6.azuredatabricks.net\": \"dev\",\n", "    \"adb-1468361750653135.15.azuredatabricks.net\": \"test\",\n", "    \"adb-**************.3.azuredatabricks.net\": \"prod\"\n", "}\n", "\n", "env = \"unknown\"\n", "for pattern, environment in env_mapping.items():\n", "    if pattern in workspace_name.lower():\n", "        env = environment\n", "        break\n", "\n", "print(f\"Environment: {env}\")\n", "\n", "if env in [\"test\", \"prod\"]:\n", "    # Configuration\n", "    db_prefix = \"mbcl\"\n", "    databases = [f\"{db_prefix}_{env}_bronze\", f\"{db_prefix}_{env}_silver\", f\"{db_prefix}_{env}_gold\"]\n", "    storage_account = \"sadbwmsproduks\"\n", "    container = \"metastore\"\n", "    external_location = f\"abfss://{container}@{storage_account}.dfs.core.windows.net/\"\n", "    \n", "    # Step 3: Verify storage connection first\n", "    # try:\n", "    #     test_path = f\"{external_location}test_connection\"\n", "    #     dbutils.fs.ls(test_path)\n", "    #     print(f\"Successfully connected to storage location: {external_location}\")\n", "    # except Exception as e:\n", "    #     print(f\"ERROR: Cannot access storage location. Check permissions and configuration.\")\n", "    #     print(f\"Details: {str(e)}\")\n", "    #     raise\n", "    \n", "    # Step 4: Create databases with explicit error handling\n", "    for db in databases:\n", "        try:\n", "            print(f\"\\nAttempting to create database: {db}\")\n", "            \n", "            # Check if database exists first\n", "            db_exists = spark.sql(f\"SHOW DATABASES LIKE '{db}'\").count() > 0\n", "            \n", "            if db_exists:\n", "                print(f\"Database {db} already exists. Checking location...\")\n", "                current_location = spark.sql(f\"DESCRIBE DATABASE EXTENDED {db}\") \\\n", "                                      .filter(\"info_name == 'Location'\") \\\n", "                                      .select(\"info_value\") \\\n", "                                      .collect()[0][0]\n", "                print(f\"Current location: {current_location}\")\n", "            else:\n", "                # Create with location\n", "                create_stmt = f\"\"\"\n", "                CREATE DATABASE IF NOT EXISTS {db} \n", "                LOCATION '{external_location}{db}'\n", "                COMMENT 'Created by automated script for {env} environment'\n", "                \"\"\"\n", "                spark.sql(create_stmt)\n", "                print(f\"Successfully created database {db} at location: {external_location}{db}\")\n", "                \n", "                # Verify creation\n", "                if spark.sql(f\"SHOW DATABASES LIKE '{db}'\").count() > 0:\n", "                    print(f\"Verification: Database {db} exists after creation\")\n", "                else:\n", "                    print(f\"WARNING: Database {db} not found after creation attempt\")\n", "                \n", "        except Exception as e:\n", "            print(f\"ERROR creating database {db}: {str(e)}\")\n", "            import traceback\n", "            traceback.print_exc()\n", "\n", "    # Step 6: Create MOS schemas in other databases\n", "    for db in databases[1:]:\n", "        if spark.sql(f\"SHOW DATABASES LIKE '{db}'\").count() > 0:\n", "            try:\n", "                spark.sql(f\"CREATE SCHEMA IF NOT EXISTS {db}.MOS\")\n", "                print(f\"Created MOS schema in {db}\")\n", "            except Exception as e:\n", "                print(f\"Error creating MOS schema in {db}: {str(e)}\")\n", "\n", "    # Step 7: Grant permissions (if using legacy permissions)\n", "    try:\n", "        user_email = \"<EMAIL>\"\n", "        for db in databases:\n", "            if spark.sql(f\"SHOW DATABASES LIKE '{db}'\").count() > 0:\n", "                spark.sql(f\"GRANT ALL PRIVILEGES ON DATABASE {db} TO `{user_email}`\")\n", "                print(f\"Granted permissions on {db} to {user_email}\")\n", "    except Exception as e:\n", "        print(f\"Error granting permissions: {str(e)}\")\n", "\n", "else:\n", "    print(\"Skipping database creation for non-test/prod environment\")\n"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "ea7e3a96-c084-4453-b8c7-6afe66e4595e", "showTitle": true, "tableResultSettingsMap": {}, "title": "This one Vinny"}}, "outputs": [], "source": ["%sql\n", "CREATE TABLE IF NOT EXISTS mbcl_prod_utility.metadata.pipelinecontrol (\n", "    EntityId INT,\n", "    Entity STRING,\n", "    Source STRING,\n", "    SourceType STRING,\n", "    TargetType STRING,\n", "    SourceTableName STRING,\n", "    TargetTableName STRING,\n", "    SchemaName STRING,\n", "    RefinedSchemaName STRING,\n", "    PreCustomSQL STRING,\n", "    CustomSQL STRING,\n", "    IsActive BOOLEAN,\n", "    Description STRING,\n", "    Delimiter STRING,\n", "    Status STRING,\n", "    DateLogged DOUBLE,\n", "    ApplicationType STRING,\n", "    ReadyToLoad BOOLEAN,\n", "    KnownSchemaList STRING,\n", "    TableSchemaList STRING,\n", "    ApplySchemaMapping BOOLEAN,\n", "    IsSilver BOOLEAN,\n", "    KeyColumn STRING,\n", "    UseAutoEncoding BOOLEAN,\n", "    last_update_column STRING,\n", "    last_run_status STRING,\n", "    use_last_ts BOOLEAN,\n", "    last_run_timestamp TIMESTAMP,\n", "    oracle_schema STRING,\n", "    Input_FilePath STRING\n", ");\n"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "646eaf80-5f29-4589-8583-af374280123a", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%sql\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('8', 'XVA', 'File', 'csv', 'lakehouse', 'xvasensitivitiesreportnetbycounterpartyandcommodity', 'xvasensitivitiesreportnetbycounterpartyandcommodity', 'mbcl_prod_bronze.xva', 'mbcl_prod_silver.xva', 'null', 'null', 'TRUE', 'Load for xvasensitivitiesreportnetbycounterpartyandcommodity', ',', 'Pending', '1.75E+09', 'batch', 'TRUE', 'null', 'null', 'FALSE', 'FALSE', NULL, 'FALSE', 'last_modified', 'null', 'FALSE', 'null', NULL, '/Volumes/mbcl_prod_bronze/xva/inbound_cleanfiles/');\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('38', 'XVA', 'File', 'csv', 'lakehouse', 'eodcsipvreport', 'eodcsipvreport', 'mbcl_prod_bronze.xva', 'mbcl_prod_silver.xva', 'null', 'null', 'TRUE', 'Load for eodcsipvreport', ',', 'Pending', '1.75E+09', 'batch', 'TRUE', 'null', 'null', 'FALSE', 'FALSE', NULL, 'FALSE', 'last_modified', 'null', 'FALSE', 'null', NULL, '/Volumes/mbcl_prod_bronze/xva/inbound_cleanfiles/');\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('50', 'XVA', 'File', 'csv', 'lakehouse', 'eodcsreport', 'eodcsreport', 'mbcl_prod_bronze.xva', 'mbcl_prod_silver.xva', 'null', 'null', 'TRUE', 'Load for eodcsreport', ',', 'Pending', '1.75E+09', 'batch', 'TRUE', 'null', 'null', 'FALSE', 'FALSE', NULL, 'FALSE', 'last_modified', 'null', 'FALSE', 'null', NULL, '/Volumes/mbcl_prod_bronze/xva/inbound_cleanfiles/');\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('39', 'XVA', 'File', 'csv', 'lakehouse', 'quantificommoditydeltabycommodityindex', 'quantificommoditydeltabycommodityindex', 'mbcl_prod_bronze.xva', 'mbcl_prod_silver.xva', 'null', 'null', 'TRUE', 'Load for quantificommoditydeltabycommodityindex', ',', 'Pending', '1.75E+09', 'batch', 'TRUE', 'null', 'null', 'FALSE', 'FALSE', 'NULL', 'FALSE', 'last_modified', 'null', 'FALSE', 'null', NULL, '/Volumes/mbcl_prod_bronze/xva/inbound_cleanfiles/');\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('40', 'XVA', 'File', 'csv', 'lakehouse', 'xvaleodpnlexplainedpnl', 'xvaleodpnlexplainedpnl', 'mbcl_prod_bronze.xva', 'mbcl_prod_silver.xva', 'null', 'null', 'TRUE', 'Load for xvaleodpnlexplainedpnl', ',', 'Pending', '1.75E+09', 'batch', 'TRUE', 'null', 'null', 'FALSE', 'FALSE', 'NULL', 'FALSE', 'last_modified', 'null', 'FALSE', 'null', NULL, '/Volumes/mbcl_prod_bronze/xva/inbound_cleanfiles/');\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('41', 'XVA', 'File', 'csv', 'lakehouse', 'xvaleodsummarypnl', 'xvaleodsummarypnl', 'mbcl_prod_bronze.xva', 'mbcl_prod_silver.xva', 'null', 'null', 'TRUE', 'Load for xvaleodsummarypnl', ',', 'Pending', '1.75E+09', 'batch', 'TRUE', 'null', 'null', 'FALSE', 'FALSE', 'NULL', 'FALSE', 'last_modified', 'null', 'FALSE', 'null', NULL, '/Volumes/mbcl_prod_bronze/xva/inbound_cleanfiles/');\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('42', 'XVA', 'File', 'csv', 'lakehouse', 'xvafeodpnlexplainedpnl', 'xvafeodpnlexplainedpnl', 'mbcl_prod_bronze.xva', 'mbcl_prod_silver.xva', 'null', 'null', 'TRUE', 'Load for xvafeodpnlexplainedpnl', ',', 'Pending', '1.75E+09', 'batch', 'TRUE', 'null', 'null', 'FALSE', 'FALSE', 'NULL', 'FALSE', 'last_modified', 'null', 'FALSE', 'null', NULL, '/Volumes/mbcl_prod_bronze/xva/inbound_cleanfiles/');\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('43', 'XVA', 'File', 'csv', 'lakehouse', 'xvafeodsummarypnl', 'xvafeodsummarypnl', 'mbcl_prod_bronze.xva', 'mbcl_prod_silver.xva', 'null', 'null', 'TRUE', 'Load for xvafeodsummarypnl', ',', 'Pending', '1.75E+09', 'batch', 'TRUE', 'null', 'null', 'FALSE', 'FALSE', 'NULL', 'FALSE', 'last_modified', 'null', 'FALSE', 'null', NULL, '/Volumes/mbcl_prod_bronze/xva/inbound_cleanfiles/');\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('44', 'XVA', 'File', 'csv', 'lakehouse', 'XVAPLReport_CounterpartyLevel', 'XVAPLReport_CounterpartyLevel', 'mbcl_prod_bronze.xva', 'mbcl_prod_silver.xva', 'null', 'null', 'TRUE', 'Load for XVAPLReport_CounterpartyLevel', ',', 'Pending', '1.75E+09', 'batch', 'TRUE', 'null', 'null', 'FALSE', 'FALSE', 'NULL', 'FALSE', 'last_modified', 'null', 'FALSE', 'null', NULL, '/Volumes/mbcl_prod_bronze/xva/inbound_cleanfiles/');\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('45', 'XVA', 'File', 'csv', 'lakehouse', 'xvaceodsummarypnl', 'xvaceodsummarypnl', 'mbcl_prod_bronze.xva', 'mbcl_prod_silver.xva', 'null', 'null', 'TRUE', 'Load for xvaceodsummarypnl', ',', 'Pending', '1.75E+09', 'batch', 'TRUE', 'null', 'null', 'FALSE', 'FALSE', 'NULL', 'FALSE', 'last_modified', 'null', 'FALSE', 'null', NULL, '/Volumes/mbcl_prod_bronze/xva/inbound_cleanfiles/');\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('46', 'XVA', 'File', 'csv', 'lakehouse', 'xvaceodpnlexplainedpnl', 'xvaceodpnlexplainedpnl', 'mbcl_prod_bronze.xva', 'mbcl_prod_silver.xva', 'null', 'null', 'TRUE', 'Load for xvaceodpnlexplainedpnl', ',', 'Pending', '1.75E+09', 'batch', 'TRUE', 'null', 'null', 'FALSE', 'FALSE', 'NULL', 'FALSE', 'last_modified', 'null', 'FALSE', 'null', NULL, '/Volumes/mbcl_prod_bronze/xva/inbound_cleanfiles/');\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('47', 'XVA', 'File', 'csv', 'lakehouse', 'xvaeeodsummarypnl', 'xvaeeodsummarypnl', 'mbcl_prod_bronze.xva', 'mbcl_prod_silver.xva', 'null', 'null', 'TRUE', 'Load for xvaeeodsummarypnl', ',', 'Pending', '1.75E+09', 'batch', 'TRUE', 'null', 'null', 'FALSE', 'FALSE', 'NULL', 'FALSE', 'last_modified', 'null', 'FALSE', 'null', NULL, '/Volumes/mbcl_prod_bronze/xva/inbound_cleanfiles/');\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('48', 'XVA', 'File', 'csv', 'lakehouse', 'xvadesknetpv01rhosensitivity', 'xvadesknetpv01rhosensitivity', 'mbcl_prod_bronze.xva', 'mbcl_prod_silver.xva', 'null', 'null', 'TRUE', 'Load for xvadesknetpv01rhosensitivity', ',', 'Pending', '1.75E+09', 'batch', 'TRUE', 'null', 'null', 'FALSE', 'FALSE', 'NULL', 'FALSE', 'last_modified', 'null', 'FALSE', 'null', NULL, '/Volumes/mbcl_prod_bronze/xva/inbound_cleanfiles/');\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('49', 'XVA', 'File', 'csv', 'lakehouse', 'xvaeeodpnlexplainedpnl', 'xvaeeodpnlexplainedpnl', 'mbcl_prod_bronze.xva', 'mbcl_prod_silver.xva', 'null', 'null', 'TRUE', 'Load for xvaeeodpnlexplainedpnl', ',', 'Pending', '1.75E+09', 'batch', 'TRUE', 'null', 'null', 'FALSE', 'FALSE', 'NULL', 'FALSE', 'last_modified', 'null', 'FALSE', 'null', NULL, '/Volumes/mbcl_prod_bronze/xva/inbound_cleanfiles/');\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('9', 'XVA', 'File', 'csv', 'lakehouse', 'xvasummaryrisksensitivitiesnetbycounterparty', 'xvasummaryrisksensitivitiesnetbycounterparty', 'mbcl_prod_bronze.xva', 'mbcl_prod_silver.xva', 'null', 'null', 'TRUE', 'Load for xvasummaryrisksensitivitiesnetbycounterparty', ',', 'Pending', '1.75E+09', 'batch', 'TRUE', 'null', 'null', 'FALSE', 'FALSE', 'business_date,counterparty_name,currency_pair', 'FALSE', 'last_modified', 'null', 'FALSE', 'null', NULL, '/Volumes/mbcl_prod_bronze/xva/inbound_cleanfiles/');\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('10', 'XVA', 'File', 'csv', 'lakehouse', 'mtmscenarioreportnetbycounterparty', 'mtmscenarioreportnetbycounterparty', 'mbcl_prod_bronze.xva', 'mbcl_prod_silver.xva', 'null', 'null', 'TRUE', 'Load for mtmscenarioreportnetbycounterparty', ',', 'Pending', '1.75E+09', 'batch', 'TRUE', 'null', 'null', 'FALSE', 'FALSE', 'business_date,counterparty', 'FALSE', 'last_modified', 'null', 'FALSE', 'null', NULL, '/Volumes/mbcl_prod_bronze/xva/inbound_cleanfiles/');\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('11', 'XVA', 'File', 'csv', 'lakehouse', 'xvascenarioreportnetbycounterparty', 'xvascenarioreportnetbycounterparty', 'mbcl_prod_bronze.xva', 'mbcl_prod_silver.xva', 'null', 'null', 'TRUE', 'Load for xvascenarioreportnetbycounterparty', ',', 'Pending', '1.75E+09', 'batch', 'TRUE', 'null', 'null', 'FALSE', 'FALSE', 'bsUiness_date,counterparty', 'FALSE', 'last_modified', 'null', 'FALSE', 'null', NULL, '/Volumes/mbcl_prod_bronze/xva/inbound_cleanfiles/');\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('12', 'XVA', 'File', 'csv', 'lakehouse', 'eodcdspnlreport', 'eodcdspnlreport', 'mbcl_prod_bronze.xva', 'mbcl_prod_silver.xva', 'null', 'null', 'TRUE', 'Load for eodcdspnlreport', ',', 'Pending', '1.75E+09', 'batch', 'TRUE', 'null', 'null', 'FALSE', 'FALSE', 'value_date,trade_id', 'FALSE', 'last_modified', 'null', 'FALSE', 'null', NULL, '/Volumes/mbcl_prod_bronze/xva/inbound_cleanfiles/');\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('13', 'XVA', 'File', 'csv', 'lakehouse', 'eodxvanewtradereport', 'eodxvanewtradereport', 'mbcl_prod_bronze.xva', 'mbcl_prod_silver.xva', 'null', 'null', 'TRUE', 'Load for eodxvanewtradereport', ',', 'Pending', '1.75E+09', 'batch', 'TRUE', 'null', 'null', 'FALSE', 'FALSE', 'trade_date,desk,currency', 'FALSE', 'last_modified', 'null', 'FALSE', 'null', NULL, '/Volumes/mbcl_prod_bronze/xva/inbound_cleanfiles/');\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('14', 'XVA', 'File', 'csv', 'lakehouse', 'eodpvrhosensitivityreport', 'eodpvrhosensitivityreport', 'mbcl_prod_bronze.xva', 'mbcl_prod_silver.xva', 'null', 'null', 'TRUE', 'Load for eodpvrhosensitivityreport', ',', 'Pending', '1.75E+09', 'batch', 'TRUE', 'null', 'null', 'FALSE', 'FALSE', 'business_date,Portfolio,ccy', 'FALSE', 'last_modified', 'null', 'FALSE', 'null', NULL, '/Volumes/mbcl_prod_bronze/xva/inbound_cleanfiles/');\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('15', 'XVA', 'File', 'csv', 'lakehouse', 'quantificommodityvegareportbycommoditytype', 'quantificommodityvegareportbycommoditytype', 'mbcl_prod_bronze.xva', 'mbcl_prod_silver.xva', 'null', 'null', 'TRUE', 'Load for quantificommodityvegareportbycommoditytype', ',', 'Pending', '1.75E+09', 'batch', 'TRUE', 'null', 'null', 'FALSE', 'FALSE', 'date,index', 'FALSE', 'last_modified', 'null', 'FALSE', 'null', NULL, '/Volumes/mbcl_prod_bronze/xva/inbound_cleanfiles/');\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('16', 'XVA', 'File', 'csv', 'lakehouse', 'eodpvrhosensitivityreport', 'eodpvrhosensitivityreport', 'mbcl_prod_bronze.xva', 'mbcl_prod_silver.xva', 'null', 'null', 'TRUE', 'Load for eodpvrhosensitivityreport', ',', 'Pending', '1.75E+09', 'batch', 'TRUE', 'null', 'null', 'FALSE', 'FALSE', 'business_date,Portfolio,ccy', 'FALSE', 'last_modified', 'null', 'FALSE', 'null', NULL, '/Volumes/mbcl_prod_bronze/xva/inbound_cleanfiles/');\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('17', 'XVA', 'File', 'csv', 'lakehouse', 'quantificommodityvegareport', 'quantificommodityvegareport', 'mbcl_prod_bronze.xva', 'mbcl_prod_silver.xva', 'null', 'null', 'TRUE', 'Load for quantificommodityvegareport', ',', 'Pending', '1.75E+09', 'batch', 'TRUE', 'null', 'null', 'FALSE', 'FALSE', 'business_date,counterparty,expiry', 'FALSE', 'last_modified', 'null', 'FALSE', 'null', NULL, '/Volumes/mbcl_prod_bronze/xva/inbound_cleanfiles/');\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('18', 'XVA', 'File', 'csv', 'lakehouse', 'energyexposures', 'energyexposures', 'mbcl_prod_bronze.xva', 'mbcl_prod_silver.xva', 'null', 'null', 'TRUE', 'Load for energyexposures', ',', 'Pending', '1.75E+09', 'batch', 'TRUE', 'null', 'null', 'FALSE', 'FALSE', 'deal_tracking_num', 'FALSE', 'last_modified', 'null', 'FALSE', 'null', NULL, '/Volumes/mbcl_prod_bronze/xva/inbound_cleanfiles/');\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('19', 'XVA', 'File', 'csv', 'lakehouse', 'tradedatareconciliation', 'tradedatareconciliation', 'mbcl_prod_bronze.xva', 'mbcl_prod_silver.xva', 'null', 'null', 'TRUE', 'Load for tradedatareconciliation', ',', 'Pending', '1.75E+09', 'batch', 'TRUE', 'null', 'null', 'FALSE', 'FALSE', 'business_date,deal_tracking_num', 'FALSE', 'last_modified', 'null', 'FALSE', 'null', NULL, '/Volumes/mbcl_prod_bronze/xva/inbound_cleanfiles/');\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('20', 'XVA', 'File', 'csv', 'lakehouse', 'eodfxsensitivityreport', 'eodfxsensitivityreport', 'mbcl_prod_bronze.xva', 'mbcl_prod_silver.xva', 'null', 'null', 'TRUE', 'Load for eodfxsensitivityreport', ',', 'Pending', '1.75E+09', 'batch', 'TRUE', 'null', 'null', 'FALSE', 'FALSE', 'business_date,Portfolio,ccy', 'FALSE', 'last_modified', 'null', 'FALSE', 'null', NULL, '/Volumes/mbcl_prod_bronze/xva/inbound_cleanfiles/');\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('21', 'XVA', 'File', 'csv', 'lakehouse', 'tradeeod', 'tradeeod', 'mbcl_prod_bronze.xva', 'mbcl_prod_silver.xva', 'null', 'null', 'TRUE', 'Load for tradeeod', ',', 'Pending', '1.75E+09', 'batch', 'TRUE', 'null', 'null', 'FALSE', 'FALSE', 'deal_tracking_num', 'FALSE', 'last_modified', 'null', 'FALSE', 'null', NULL, '/Volumes/mbcl_prod_bronze/xva/inbound_cleanfiles/');\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('22', 'XVA', 'File', 'csv', 'lakehouse', 'vwdeltapv', 'vwdeltapv', 'mbcl_prod_bronze.xva', 'mbcl_prod_silver.xva', 'null', 'null', 'TRUE', 'Load for vwdeltapv', ',', 'Pending', '1.75E+09', 'batch', 'TRUE', 'null', 'null', 'FALSE', 'FALSE', 'business_date,index', 'FALSE', 'last_modified', 'null', 'FALSE', 'null', NULL, '/Volumes/mbcl_prod_bronze/xva/inbound_cleanfiles/');\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('23', 'XVA', 'File', 'csv', 'lakehouse', 'metalsexposures', 'metalsexposures', 'mbcl_prod_bronze.xva', 'mbcl_prod_silver.xva', 'null', 'null', 'TRUE', 'Load for metalsexposures', ',', 'Pending', '1.75E+09', 'batch', 'TRUE', 'null', 'null', 'FALSE', 'FALSE', 'deal_num', 'FALSE', 'last_modified', 'null', 'FALSE', 'null', NULL, '/Volumes/mbcl_prod_bronze/xva/inbound_cleanfiles/');\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('24', 'XVA', 'File', 'csv', 'lakehouse', 'xvadesknetfxsensitivity', 'xvadesknetfxsensitivity', 'mbcl_prod_bronze.xva', 'mbcl_prod_silver.xva', 'null', 'null', 'TRUE', 'Load for xvadesknetfxsensitivity', ',', 'Pending', '1.75E+09', 'batch', 'TRUE', 'null', 'null', 'FALSE', 'FALSE', 'currency,total__currency_amount_', 'FALSE', 'last_modified', 'null', 'FALSE', 'null', NULL, '/Volumes/mbcl_prod_bronze/xva/inbound_cleanfiles/');\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('25', 'XVA', 'File', 'csv', 'lakehouse', 'quantifisensitivitycounterpartylevel', 'quantifisensitivitycounterpartylevel', 'mbcl_prod_bronze.xva', 'mbcl_prod_silver.xva', 'null', 'null', 'TRUE', 'Load for quantifisensitivitycounterpartylevel', ',', 'Pending', '1.75E+09', 'batch', 'TRUE', 'null', 'null', 'FALSE', 'FALSE', 'cpty_name,cpty_rating,credit_curve_name', 'FALSE', 'last_modified', 'null', 'FALSE', 'null', NULL, '/Volumes/mbcl_prod_bronze/xva/inbound_cleanfiles/');\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('26', 'XVA', 'File', 'csv', 'lakehouse', 'marketriskirsensitivitybycounterparty', 'marketriskirsensitivitybycounterparty', 'mbcl_prod_bronze.xva', 'mbcl_prod_silver.xva', 'null', 'null', 'TRUE', 'Load for marketriskirsensitivitybycounterparty', ',', 'Pending', '1.75E+09', 'batch', 'TRUE', 'null', 'null', 'FALSE', 'FALSE', 'cpty_name,cpty_rating', 'FALSE', 'last_modified', 'null', 'FALSE', 'null', NULL, '/Volumes/mbcl_prod_bronze/xva/inbound_cleanfiles/');\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('27', 'XVA', 'File', 'csv', 'lakehouse', 'vwmosvegastrikesurface', 'vwmosvegastrikesurface', 'mbcl_prod_bronze.xva', 'mbcl_prod_silver.xva', 'null', 'null', 'TRUE', 'Load for vwmosvegastrikesurface', ',', 'Pending', '1.75E+09', 'batch', 'TRUE', 'null', 'null', 'FALSE', 'FALSE', 'business_date,index_name,expiry', 'FALSE', 'last_modified', 'null', 'FALSE', 'null', NULL, '/Volumes/mbcl_prod_bronze/xva/inbound_cleanfiles/');\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('28', 'XVA', 'File', 'csv', 'lakehouse', 'quantificommodityvegareportbycommoditytype', 'quantificommodityvegareportbycommoditytype', 'mbcl_prod_bronze.xva', 'mbcl_prod_silver.xva', 'null', 'null', 'TRUE', 'Load for quantificommodityvegareportbycommoditytype', ',', 'Pending', '1.75E+09', 'batch', 'TRUE', 'null', 'null', 'FALSE', 'FALSE', 'date,index', 'FALSE', 'last_modified', 'null', 'FALSE', 'null', NULL, '/Volumes/mbcl_prod_bronze/xva/inbound_cleanfiles/');\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('29', 'XVA', 'File', 'csv', 'lakehouse', 'vwmosdeltapv', 'vwmosdeltapv', 'mbcl_prod_bronze.xva', 'mbcl_prod_silver.xva', 'null', 'null', 'TRUE', 'Load for vwmosdeltapv', ',', 'Pending', '1.75E+09', 'batch', 'TRUE', 'null', 'null', 'FALSE', 'FALSE', 'business_date,index', 'FALSE', 'last_modified', 'null', 'FALSE', 'null', NULL, '/Volumes/mbcl_prod_bronze/xva/inbound_cleanfiles/');\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('30', 'XVA', 'File', 'csv', 'lakehouse', 'marketriskcommoditysensitivitybycounterparty', 'marketriskcommoditysensitivitybycounterparty', 'mbcl_prod_bronze.xva', 'mbcl_prod_silver.xva', 'null', 'null', 'TRUE', 'Load for marketriskcommoditysensitivitybycounterparty', ',', 'Pending', '1.75E+09', 'batch', 'TRUE', 'null', 'null', 'FALSE', 'FALSE', 'cpty_name,cpty_rating,credit_curve_name', 'FALSE', 'last_modified', 'null', 'FALSE', 'null', NULL, '/Volumes/mbcl_prod_bronze/xva/inbound_cleanfiles/');\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('31', 'XVA', 'File', 'csv', 'lakehouse', 'xvaplreportcounterpartylevel', 'xvaplreportcounterpartylevel', 'mbcl_prod_bronze.xva', 'mbcl_prod_silver.xva', 'null', 'null', 'TRUE', 'Load for xvaplreportcounterpartylevel', ',', 'Pending', '1.75E+09', 'batch', 'TRUE', 'null', 'null', 'FALSE', 'FALSE', 'value_date,cpty_name,cpty_rating', 'FALSE', 'last_modified', 'null', 'FALSE', 'null', NULL, '/Volumes/mbcl_prod_bronze/xva/inbound_cleanfiles/');\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('32', 'XVA', 'File', 'csv', 'lakehouse', 'xvacsreportnetbycounterparty', 'xvacsreportnetbycounterparty', 'mbcl_prod_bronze.xva', 'mbcl_prod_silver.xva', 'null', 'null', 'TRUE', 'Load for xvacsreportnetbycounterparty', ',', 'Pending', '1.75E+09', 'batch', 'TRUE', 'null', 'null', 'FALSE', 'FALSE', 'business_date,counterparty,currency', 'FALSE', 'last_modified', 'null', 'FALSE', 'null', NULL, '/Volumes/mbcl_prod_bronze/xva/inbound_cleanfiles/');\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('33', 'XVA', 'File', 'csv', 'lakehouse', 'marketriskfxsensitivitybycounterparty', 'marketriskfxsensitivitybycounterparty', 'mbcl_prod_bronze.xva', 'mbcl_prod_silver.xva', 'null', 'null', 'TRUE', 'Load for marketriskfxsensitivitybycounterparty', ',', 'Pending', '1.75E+09', 'batch', 'TRUE', 'null', 'null', 'FALSE', 'FALSE', 'cpty_name,cpty_rating,credit_curve_name', 'FALSE', 'last_modified', 'null', 'FALSE', 'null', NULL, '/Volumes/mbcl_prod_bronze/xva/inbound_cleanfiles/');\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('34', 'XVA', 'File', 'csv', 'lakehouse', 'marketriskcsreportbycreditcurve', 'marketriskcsreportbycreditcurve', 'mbcl_prod_bronze.xva', 'mbcl_prod_silver.xva', 'null', 'null', 'TRUE', 'Load for marketriskcsreportbycreditcurve', ',', 'Pending', '1.75E+09', 'batch', 'TRUE', 'null', 'null', 'FALSE', 'FALSE', 'value_date,bond_reference_entity,currency', 'FALSE', 'last_modified', 'null', 'FALSE', 'null', NULL, '/Volumes/mbcl_prod_bronze/xva/inbound_cleanfiles/');\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('35', 'XVA', 'File', 'csv', 'lakehouse', 'creditcurves', 'creditcurves', 'mbcl_prod_bronze.xva', 'mbcl_prod_silver.xva', 'null', 'null', 'TRUE', 'Load for creditcurves', ',', 'Pending', '1.75E+09', 'batch', 'TRUE', 'null', 'null', 'FALSE', 'FALSE', 'cobdate,curveticker', 'FALSE', 'last_modified', 'null', 'FALSE', 'null', NULL, '/Volumes/mbcl_prod_bronze/xva/inbound_cleanfiles/');\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('36', 'XVA', 'File', 'csv', 'lakehouse', 'quantificommoditydeltanetbycounterpartyandcommodity', 'quantificommoditydeltanetbycounterpartyandcommodity', 'mbcl_prod_bronze.xva', 'mbcl_prod_silver.xva', 'null', 'null', 'TRUE', 'Load for quantificommoditydeltanetbycounterpartyandcommodity', ',', 'Pending', '1.75E+09', 'batch', 'TRUE', 'null', 'null', 'FALSE', 'FALSE', 'business_date,index,xva_commodity_delta', 'FALSE', 'last_modified', 'null', 'FALSE', 'null', NULL, '/Volumes/mbcl_prod_bronze/xva/inbound_cleanfiles/');\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('37', 'XVA', 'File', 'csv', 'lakehouse', 'xvaraterisknetbyrateindex', 'xvaraterisknetbyrateindex', 'mbcl_prod_bronze.xva', 'mbcl_prod_silver.xva', 'null', 'null', 'TRUE', 'Load for xvaraterisknetbyrateindex', ',', 'Pending', '1.75E+09', 'batch', 'TRUE', 'null', 'null', 'FALSE', 'FALSE', 'bsuiness_date,xva,total', 'FALSE', 'last_modified', 'null', 'FALSE', 'null', NULL, '/Volumes/mbcl_prod_bronze/xva/inbound_cleanfiles/');\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('5', 'MOS', 'Lakehouse', 'Database', 'lakehouse', 'VW_NEW_DER', 'VW_NEW_DER', 'mbcl_prod_bronze.mos', 'mbcl_prod_silver.mos', 'null', 'null', 'TRUE', 'Load data for Vw New Der', 'null', 'null', 'null', 'null', 'null', 'null', 'null', 'null', 'FALSE', 'reportDate,entityId', 'null', 'REPORT_DATE', 'FAILED', 'TRUE', '2025-05-24T22:24:53.01827Z', 'mosprod', NULL);\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('3', 'MOS', 'Lakehouse', 'Database', 'lakehouse', 'VW_NEW_DER_CSA', 'VW_NEW_DER_CSA', 'mbcl_prod_bronze.mos', 'mbcl_prod_silver.mos', 'null', 'null', 'TRUE', 'Load data for Vw New Der Csa', 'null', 'null', 'null', 'null', 'null', 'null', 'null', 'null', 'FALSE', 'reportDate,entityId', 'null', 'REPORT_DATE', 'FAILED', 'TRUE', '2025-05-24T22:24:50.182144Z', 'mosprod', NULL);\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('6', 'MOS', 'Lakehouse', 'Database', 'lakehouse', 'VW_NEW_DER_TMA', 'VW_NEW_DER_TMA', 'mbcl_prod_bronze.mos', 'mbcl_prod_silver.mos', 'null', 'null', 'TRUE', 'Load data for Vw New Der Tma', 'null', 'null', 'null', 'null', 'null', 'null', 'null', 'null', 'FALSE', 'reportDate,entityId', 'null', 'REPORT_DATE', 'FAILED', 'TRUE', '2025-05-24T22:24:41.184275Z', 'mosprod', NULL);\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('4', 'MOS', 'Lakehouse', 'Database', 'lakehouse', 'VW_NEW_DER_NONCSA', 'VW_NEW_DER_NONCSA', 'mbcl_prod_bronze.mos', 'mbcl_prod_silver.mos', 'null', 'null', 'TRUE', 'Load data for Vw New Der Noncsa', 'null', 'null', 'null', 'null', 'null', 'null', 'null', 'null', 'FALSE', 'reportDate,entityId', 'null', 'REPORT_DATE', 'FAILED', 'TRUE', '2025-05-24T22:24:55.759391Z', 'mosprod', NULL);\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('2', 'MOS', 'Lakehouse', 'Database', 'lakehouse', 'VW_ACCOUNT_RECEIVABLE_DETAILS', 'VW_ACCOUNT_RECEIVABLE_DETAILS', 'mbcl_prod_bronze.mos', 'mbcl_prod_silver.mos', 'null', 'null', 'TRUE', 'Load data for Vw Account Receivable Details', 'null', 'null', 'null', 'null', 'null', 'null', 'null', 'null', 'FALSE', 'runDate,entityId', 'null', 'RUN_DATE', 'FAILED', 'TRUE', '2025-05-24T22:24:44.196635Z', 'mosprod', NULL);\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('7', 'MOS', 'Lakehouse', 'Database', 'lakehouse', 'VW_SUSPENDED_COUNTERPARTIES', 'VW_SUSPENDED_COUNTERPARTIES', 'mbcl_prod_bronze.mos', 'mbcl_prod_silver.mos', 'null', 'null', 'TRUE', 'Load data for Vw Suspended Counterparties', 'null', 'null', 'null', 'null', 'null', 'null', 'null', 'null', 'FALSE', 'snapshotDate,entityId', 'null', 'SNAPSHOT_DATE', 'FAILED', 'TRUE', '2025-05-24T22:24:58.930103Z', 'mosprod', NULL);\n", "INSERT INTO mbcl_prod_utility.metadata.pipelinecontrol (EntityId, Entity, Source, SourceType, TargetType, SourceTableName, TargetTableName, SchemaName, RefinedSchemaName, PreCustomSQL, CustomSQL, IsActive, Description, Delimiter, Status, DateLogged, ApplicationType, ReadyToLoad, KnownSchemaList, TableSchemaList, ApplySchemaMapping, IsSilver, KeyColumn, UseAutoEncoding, last_update_column, last_run_status, use_last_ts, last_run_timestamp, oracle_schema, Input_FilePath) VALUES ('1', 'MOS', 'Lakehouse', 'Database', 'lakehouse', 'VW_ACCOUNT_RECEIVABLE_BY_CLIENT', 'VW_ACCOUNT_RECEIVABLE_BY_CLIENT', 'mbcl_prod_bronze.mos', 'mbcl_prod_silver.mos', 'null', 'null', 'TRUE', 'Load data for  Vw Account Receivable By Client', 'null', 'null', 'null', 'null', 'null', 'null', 'null', 'null', 'FALSE', 'runDate,entityId', 'null', 'RUN_DATE', 'FAILED', 'TRUE', '2025-05-24T22:24:47.305106Z', 'mosprod', NULL);\n"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "inputWidgetPreferences": null, "language": "python", "notebookMetadata": {"mostRecentlyExecutedCommandWithImplicitDF": {"commandId": ****************, "dataframes": ["_sqldf"]}, "pythonIndentUnit": 4}, "notebookName": "Create UC Required Tables", "widgets": {}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}