# Databricks notebook source
# MAGIC %md
# MAGIC
# MAGIC # 📘 xvaCuratedDataset_MTMReconciliation – Documentation
# MAGIC
# MAGIC This notebook performs **Mark-to-Market (MTM) reconciliation** between Mitsui and Quantifi data sources. It focuses on exposures from **metal** and **energy** desks and joins this with trade data to highlight mismatches in MTM valuation.
# MAGIC
# MAGIC ---
# MAGIC
# MAGIC ## 📦 Libraries Used
# MAGIC
# MAGIC ```python
# MAGIC from pyspark.sql.functions import col
# MAGIC ```
# MAGIC
# MAGIC ---
# MAGIC ## 📊 Step 1 – Remove "_1" and "_2" etc. from the dealTrackingNum column from "tradedatareconciliation" table and keep only one entry having mtm = sum of mtm values for the same dealTrackingNum
# MAGIC
# MAGIC **Tables Involved**:
# MAGIC - `{bronze_catalog}.{schema_name}.tradedatareconciliation`
# MAGIC
# MAGIC **Transformation Performed**:
# MAGIC - Remove "_1" and "_2" etc. from the dealTrackingNum column and keep only one entry
# MAGIC - Do sum on all `mtm` values
# MAGIC - Outputs a unified Spark SQL temp view: `TempTradeRecView`
# MAGIC
# MAGIC ---
# MAGIC
# MAGIC ## 📊 Step 2 – Load and Combine Metal and Energy MTM Data
# MAGIC
# MAGIC **Tables Involved**:
# MAGIC - `{bronze_catalog}.{schema_name}.metalsexposures`
# MAGIC - `{bronze_catalog}.{schema_name}.energyexposures`
# MAGIC - `{bronze_catalog}.{schema_name}.tradeeod`
# MAGIC
# MAGIC **Transformation Performed**:
# MAGIC - Standardizes metal and energy trade structures
# MAGIC - Aggregates `base_mtm` values
# MAGIC - Adds business and trade metadata
# MAGIC - Outputs a unified Spark SQL temp view: `TempView`
# MAGIC
# MAGIC ---
# MAGIC
# MAGIC ## 🔁 Step 3 – Reconcile with Quantifi Trade Data
# MAGIC
# MAGIC **Tables Involved**:
# MAGIC - `TempView` (from Step 1)
# MAGIC - `{bronze_catalog}.{schema_name}.tradedatareconciliation`
# MAGIC
# MAGIC **Objective**:
# MAGIC - Join Mitsui MTM with Quantifi MTM by `deal_number`
# MAGIC - Calculate MTM variance per trade
# MAGIC ---
# MAGIC
# MAGIC ## ✅ Final Output
# MAGIC
# MAGIC - Each row shows a **deal-level MTM delta**.
# MAGIC
# MAGIC

# COMMAND ----------

from pyspark.sql.functions import col, sum, first, split, date_format, to_timestamp

# COMMAND ----------

dbutils.widgets.text("env", "")
dbutils.widgets.text("entity", "")

env = dbutils.widgets.get("env").lower()
entity = dbutils.widgets.get("entity").upper()

bronze_catalog = f"mbcl_{env}_bronze"
gold_catalog = f"mbcl_{env}_gold"
schema_name = entity.lower()



# COMMAND ----------

# MAGIC %run ./Utils_functions

# COMMAND ----------

# Check if all required tables for **Mark-to-Market (MTM) reconciliation** are present

used_tables = [
  f"{bronze_catalog}.{schema_name}.tradedatareconciliation",
  f"{bronze_catalog}.{schema_name}.metalexposures",
  f"{bronze_catalog}.{schema_name}.tradeeod",
  f"{bronze_catalog}.{schema_name}.energyexposures"
]

if not tables_exists(used_tables):
  dbutils.notebook.exit(f"At least one of the following tables is missing: {used_tables}")

# COMMAND ----------

# Remove "_1" and "_2" etc. from the dealTrackingNum column from "tradedatareconciliation" table and keep only one entry having mtm = sum of mtm values for the same dealTrackingNum

df_tradereconciliation = spark.read.table(f"{bronze_catalog}.{schema_name}.tradedatareconciliation")
df_tradereconciliation = df_tradereconciliation.withColumn("base_dealTrackingNum", split("dealTrackingNum", "_").getItem(0))
df_tradereconciliation = df_tradereconciliation.withColumn("mtm",col("mtm").cast("double"))

df_tradereconciliation = df_tradereconciliation.withColumn(
    "businessDate",
    date_format(to_timestamp("businessDate", "M/d/yyyy h:mm:ss a"), "dd-MMM-yyyy")
)

result_df_tradereconciliation = df_tradereconciliation.groupBy("base_dealTrackingNum").agg(
  sum("mtm").alias("mtm"),
  first("tradecurrency").alias("tradecurrency"),
  first("businessDate").alias("businessDate")
)
result_df_tradereconciliation = result_df_tradereconciliation.withColumnRenamed("base_dealTrackingNum", "dealTrackingNum")
result_df_tradereconciliation.createOrReplaceTempView('TempTradeRecView')

# COMMAND ----------

query = f"""
WITH CTE_TEMP AS (
    SELECT 
        DISTINCT 'metal' AS source,
        dealNum AS deal_number,
        basemtmdisc AS base_mtm,
        t.tradecurrency AS BaseCurrency,
        internallentity,
        externallentity,
        EXTERNALENTITYID,
        INTERNALPORTFOLIO,
        TRADEDATE,
        BUYSELL,
        TOOLSET,
        INSTYPE
    FROM {bronze_catalog}.{schema_name}.metalexposures m 
    LEFT JOIN {bronze_catalog}.{schema_name}.tradeeod t 
    ON m.dealNum = t.dealTrackingNum

    UNION ALL

    SELECT 
        DISTINCT 'energy' AS source,
        e.dealTrackingNum AS deal_number,
        basemtm AS mtm,
        t.tradecurrency AS BaseCurrency,
        internallentity,
        externallentity,
        EXTERNALENTITYID,
        INTERNALPORTFOLIO,
        TRADEDATE,
        BUYSELL,
        TOOLSET,
        INSTYPE
    FROM {bronze_catalog}.{schema_name}.energyexposures e
    LEFT JOIN {bronze_catalog}.{schema_name}.tradeeod t 
    ON e.dealtrackingnum = t.dealtrackingnum
)

SELECT 
    source,
    deal_number,
    SUM(base_mtm) AS mtm,
    BaseCurrency,
    internallentity,
    externallentity,
    EXTERNALENTITYID,
    INTERNALPORTFOLIO,
    TRADEDATE,
    BUYSELL,
    TOOLSET,
    INSTYPE
FROM CTE_TEMP
GROUP BY 
    source,
    deal_number,
    BaseCurrency,
    internallentity,
    externallentity,
    EXTERNALENTITYID,
    INTERNALPORTFOLIO,
    TRADEDATE,
    BUYSELL,
    TOOLSET,
    INSTYPE
ORDER BY deal_number
"""

df = spark.sql(query)
df.createOrReplaceTempView('TempView')

# COMMAND ----------


spark.conf.set("spark.sql.ansi.enabled", "true")
query = f"""
WITH XTE_Base AS (
    SELECT DISTINCT
        xt.deal_number,        
        xt.mtm AS mitsui_base_mtm,
        tr.mtm AS quantifi_base_mtm,
        tr.tradecurrency,
        xt.internallentity,
        xt.externallentity,
        xt.EXTERNALENTITYID,
        xt.INTERNALPORTFOLIO,
        xt.TRADEDATE,
        xt.BUYSELL,
        xt.TOOLSET,
        xt.INSTYPE,
        tr.businessDate
    FROM 
        TempView xt
    JOIN TempTradeRecView tr 
        ON try_cast(xt.deal_number AS BIGINT) = try_cast(tr.dealTrackingNum AS BIGINT)
),

XTE_A AS (
    SELECT 
        deal_number,
        quantifi_base_mtm,
        mitsui_base_mtm,
        CASE
            WHEN quantifi_base_mtm = 0 AND mitsui_base_mtm = 0 THEN 0.0
            WHEN mitsui_base_mtm = 0 AND quantifi_base_mtm != 0 THEN 100.0
        ELSE ABS(quantifi_base_mtm - mitsui_base_mtm) / ABS(mitsui_base_mtm) * 100
        END AS abs_percentage_difference,
        CASE
            WHEN quantifi_base_mtm = 0 AND mitsui_base_mtm = 0 THEN 0.0
            WHEN mitsui_base_mtm = 0 AND quantifi_base_mtm != 0 THEN 100.0
        ELSE try_divide(quantifi_base_mtm - mitsui_base_mtm, mitsui_base_mtm) * 100
        END AS percentage_difference,
        ABS(quantifi_base_mtm - mitsui_base_mtm) AS abs_Difference,
        (quantifi_base_mtm - mitsui_base_mtm) AS difference,
        tradecurrency as trade_currency,
        internallentity as internal_lentity,
        externallentity as external_lentity,
        externalentityid as external_entity_id,
        internalportfolio as internal_portfolio,
        tradedate as trade_date,
        buysell as buy_sell,
        toolset,
        instype as ins_type, 
        businessDate as business_date,
        -- Classification logic for abs_percentage_diff_bucket
        CASE 
            WHEN abs_percentage_difference < 1 THEN 'Less 1%'
            WHEN abs_percentage_difference > 1 AND abs_Difference <= 500 THEN '>1%<+/-$500'
            WHEN abs_percentage_difference >= 10 THEN '>10%'
            WHEN abs_percentage_difference > 5 THEN '>5%'
        ELSE '>1%'
        END AS abs_percentage_diff_bucket
    FROM 
        XTE_Base
)
SELECT * FROM XTE_A
"""


df = spark.sql(query)
df = cast_to_inferred_type(df)
df.write.format("delta").mode("overwrite").option("overwriteSchema", "true").saveAsTable(f"{gold_catalog}.{schema_name}.xva_tradelevelmtmrec")
