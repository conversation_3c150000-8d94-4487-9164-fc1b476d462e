{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "1c298023-8dc0-4147-91b2-fee86ac07c72", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["dbutils.widgets.text(\"ipv_bronze_catalog\", \"mbcl_dev_bronze\", \"ipv_bronze_catalog\")\n", "dbutils.widgets.text(\"ipv_bronze_schema\", \"ipv\", \"ipv_bronze_schema\")\n", "dbutils.widgets.text(\"moving_files_to_quarantine\", \"no\", \"moving_files_to_quarantine\")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "dad89693-470d-4b5f-8556-a44edc551e22", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["import pyspark.sql.functions as F\n", "import pyspark.sql.types as T\n", "from pyspark.sql import DataFrame\n", "from datetime import datetime\n", "import re\n", "import json"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "c0a592e2-c740-44ee-ad50-17827f0ebba7", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["CATALOG = dbutils.widgets.get(\"ipv_bronze_catalog\")\n", "SCHEMA = dbutils.widgets.get(\"ipv_bronze_schema\")\n", "\n", "MOVING_FILES_TO_QUARANTINE = True if dbutils.widgets.get(\"moving_files_to_quarantine\").lower() == \"yes\" else False\n", "\n", "BASE_PATH = '/Volumes/mbcl_dev_bronze/ipv/'\n", "INBOUND_PATH = 'temp_inbound'\n", "PROCESSED_PATH = 'temp_processed'\n", "QUARANTINE_PATH = 'temp_quarantine'\n", "\n", "REFERENCE_GAS_COLUMNS = ['Gas HUB', 'ICE CODE', 'OL Code', 'GD Code']\n", "REFERENCE_POWER_COLUMNS = ['Power OL Name', 'ICE CODE', 'Region']\n", "REFERENCE_REGION_COLUMNS = ['WEST PK', 'WEST OP', 'EAST PK DA', 'EAST PK RT', 'EAST OP DA', 'EAST OP RT']"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "a902fd1c-7f5b-4938-b04e-f27692b418ab", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%run ./ipv_ice_settlements_constants"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "6edacf99-4568-44fe-be5f-8c7454860482", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["def clean_column_name(col_name):\n", "    return re.sub(r'[^a-zA-Z0-9_]', '_', col_name.strip()).lower()\n", "\n", "def clean_columns(df):\n", "    return df.toDF(*[clean_column_name(col_name) for col_name in df.columns])\n", "    \n", "def autoload_csv(\n", "    source_path, bronze_table, checkpoint_path, \n", "    schema_path, quarantine_path, quarantine_log_table, \n", "    schema=None):\n", "    \"\"\"\n", "    Auto Loader CSV with file-level quarantine:\n", "    - if any row is bad, quarantine the entire file\n", "    - if all rows are good, write to bronze table\n", "    - logs quarantined files to a delta table for traceability\n", "    - supports schema evolution\n", "    - supports reprocessing of quarantined fiels later\n", "    \"\"\"\n", "    if schema is None:\n", "        print(\"Using autoloader inferred schema.\")\n", "        try:\n", "            with open(schema_path, 'r') as f:\n", "                schema = f.read()\n", "            schema = T.StructType.from_json(schema)\n", "        except:\n", "            inferred_df = spark.read \\\n", "                .format('csv') \\\n", "                .option('header', 'true') \\\n", "                .load(source_path)\n", "            schema = inferred_df.schema\n", "            with open(schema_path, 'w') as f:\n", "                f.write(json.dumps(schema.jsonValue()))\n", "    else:\n", "        print(\"Using provided tables schema.\")\n", "\n", "    df = (\n", "        spark.readStream\n", "        .format('cloudFiles')\n", "        .option('cloudFiles.format', 'csv')\n", "        .option(\"badRecordsPath\", quarantine_path)\n", "        .option('cloudFiles.schemaLocation', schema_path)\n", "        .option('header', 'true')\n", "        .schema(schema)\n", "        .load(source_path)\n", "    )\n", "\n", "    df = clean_columns(df)\n", "\n", "    df = (\n", "        df\n", "        .withColumn('ingestion_timestamp', F.current_timestamp())\n", "        .withColumn('_file_path', F.col('_metadata.file_path'))\n", "    )\n", "\n", "    # Create quarantine log table if not exists\n", "    spark.sql(f\"\"\"\n", "        CREATE TABLE IF NOT EXISTS {quarantine_log_table} (\n", "            file_path STRING,\n", "            batch_id BIGINT,\n", "            reason STRING,\n", "            timestamp TIMESTAMP,\n", "            moved BOOLEAN\n", "        ) USING DELTA  \n", "    \"\"\")\n", "\n", "    # micro batch logic\n", "    def process_batch(batch_df: DataFrame, batch_id: int):\n", "        if batch_df.isEmpty():\n", "            return\n", "        \n", "        files = [r[\"_file_path\"] for r in batch_df.select(\"_file_path\").distinct().collect()]\n", "\n", "        try:\n", "            bad_df = spark.read \\\n", "                .format('json') \\\n", "                .load(f'{quarantine_path}/bad_records') \\\n", "                .select(\"path\").distinct()\n", "\n", "            bad_files = [r[\"path\"] for r in bad_df.select(\"path\").collect()]\n", "        except Exception as e:\n", "            bad_files = []\n", "\n", "        quarantiend = []\n", "\n", "        for file in files:\n", "            if file in bad_files:\n", "                quarantiend.append(file)\n", "                \n", "                spark \\\n", "                    .createDataFrame(\n", "                        [(file, batch_id, \"Bad row detected\", datetime.now(), False)],\n", "                        schema=quarantine_log_schema\n", "                    ) \\\n", "                    .write \\\n", "                    .mode(\"append\") \\\n", "                    .saveAsTable(quarantine_log_table)\n", "            else:\n", "                (\n", "                    batch_df.filter(batch_df[\"_file_path\"] == file)\n", "                        .drop(\"_file_path\")\n", "                        .write.format(\"delta\")\n", "                        .mode(\"append\")\n", "                        .saveAsTable(bronze_table)\n", "                )\n", "        if quarantiend:\n", "            print(f\"Batch {batch_id} quarantined {len(quarantiend)} files: {quarantiend}\")\n", "        else:\n", "            print(f\"Batch {batch_id} processed succesfull\")\n", "\n", "    query = (df.writeStream\n", "        .foreachBatch(process_batch)\n", "        .option('checkpointLocation', checkpoint_path)\n", "        .trigger(availableNow=True)\n", "        .start()\n", "    )\n", "\n", "    return query\n", "\n", "def move_files(quarantine_log_table):\n", "    quarantine_log_df = \\\n", "        spark.sql(f\"\"\"\n", "            SELECT * FROM {quarantine_log_table}\n", "            WHERE moved = false\"\"\"\n", "        ).collect()\n", "\n", "    for row in quarantine_log_df:\n", "        file_path = row[\"file_path\"]\n", "        path_split = row.file_path.split(\"/\")\n", "        if len(path_split) < 2:\n", "            print(f\"Invalid file path: {file_path}\")\n", "            continue\n", "        filename = path_split[-1]\n", "        file_folder = path_split[-2]\n", "        print(file_path, filename)\n", "        dbutils.fs.mv(file_path, f\"{BASE_PATH}/temp_quarantine/{file_folder}/{filename}\")\n", "        \n", "    spark.sql(f\"\"\" UPDATE {quarantine_log_table} SET moved = true \"\"\")\n"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "bff873b1-0d56-46c9-9715-0f33564b57eb", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["files = [\n", "    (\"reference_power\", \"bronze_reference_power\", reference_power_schema),\n", "    (\"reference_gas\", \"bronze_reference_gas\", reference_gas_schema),\n", "    (\"reference_regions\", \"bronze_reference_regions\", reference_regions_schema),\n", "    (\"settlement_prices_power\", \"bronze_settlement_prices_power\", settlement_prices_power_schema),\n", "    (\"settlement_prices_gas\", \"bronze_settlement_prices_gas\", settlement_prices_gas_schema)\n", "]"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "b865d130-d566-408b-9378-f9cf24390428", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["for name, current_table, current_table_schema in files:\n", "    autoload_csv(\n", "        f\"{BASE_PATH}/temp_inbound/{name}\",\n", "        f\"{CATALOG}.{SCHEMA}.{current_table}\",\n", "        f\"{BASE_PATH}/temp_checkpoints/{name}\",\n", "        f\"{BASE_PATH}/temp_schema_tracking/{name}\",\n", "        f\"{BASE_PATH}/temp_quarantine/{name}\",\n", "        f\"{CATALOG}.{SCHEMA}.quarantine_log_table\",\n", "        current_table_schema\n", "    )"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "14fceed9-5227-4317-ae29-54fe81f07b1b", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["if MOVING_FILES_TO_QUARANTINE:\n", "    move_files(f\"{CATALOG}.{SCHEMA}.quarantine_log_table\")"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "inputWidgetPreferences": null, "language": "python", "notebookMetadata": {"pythonIndentUnit": 4}, "notebookName": "ipv_ice_settlements_autoloader_ingestion_adls_to_bronze", "widgets": {"ipv_bronze_catalog": {"currentValue": "mbcl_dev_bronze", "nuid": "63ccdb8e-f58d-4aae-9b59-d436fdb43462", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "mbcl_dev_bronze", "label": "ipv_bronze_catalog", "name": "ipv_bronze_catalog", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "mbcl_dev_bronze", "label": "ipv_bronze_catalog", "name": "ipv_bronze_catalog", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}, "ipv_bronze_schema": {"currentValue": "ipv", "nuid": "a56a6a8c-7832-4ba2-a69c-13071c243c1f", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "ipv", "label": "ipv_bronze_schema", "name": "ipv_bronze_schema", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "ipv", "label": "ipv_bronze_schema", "name": "ipv_bronze_schema", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}, "moving_files_to_quarantine": {"currentValue": "no", "nuid": "66e132b0-b874-4cb9-a0d5-9d7c0e31df75", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "no", "label": "moving_files_to_quarantine", "name": "moving_files_to_quarantine", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "no", "label": "moving_files_to_quarantine", "name": "moving_files_to_quarantine", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}