{"cells": [{"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "fa201e0e-b796-4ec9-b595-b6a1eb9b1fbf", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["# Setup\n"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "7a512f0c-c72f-4d49-98e3-419997cac502", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["ipv_silver_catalog = 'mbcl_dev_silver'\n", "ipv_silver_schema = 'ipv'"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "75f7eb1c-09d2-4fd6-8f2d-b61ef9435f94", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["# Create silver watermark table for incremental ingestion from bronze layer"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "417563b7-f2af-4b17-96da-70665cb55aad", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["spark.sql(f\"\"\"\n", "    CREATE TABLE IF NOT EXISTS {ipv_silver_catalog}.{ipv_silver_schema}.silver_watermark (\n", "        pipeline_name STRING,\n", "        last_processed TIMESTAMP\n", "    ) USING DELTA;\n", "\"\"\")"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "inputWidgetPreferences": null, "language": "python", "notebookMetadata": {"pythonIndentUnit": 4}, "notebookName": "ipv_ice_settlements_default_tables_ddl", "widgets": {}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}