{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "2789abf2-844a-441f-a3b1-183dfeac0f79", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# --- Widgets ---\n", "dbutils.widgets.text(\"env\", \"\")\n", "dbutils.widgets.text(\"entity\", \"\")\n", "\n", "env = dbutils.widgets.get(\"env\").lower()\n", "entity = dbutils.widgets.get(\"entity\").lower()"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "5d80e5fa-fc3e-4b15-8836-aad5e4ab8fde", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Query the scope of Azure Key Vault and secrets\n", "key_vault_scope = \"akv_dataplatform\"\n", "\n", "oracle_username = dbutils.secrets.get(scope=\"akv_dataplatform\", key=\"oracle-midas-username\")\n", "oracle_password = dbutils.secrets.get(scope=\"akv_dataplatform\", key=\"oracle-midas-password\")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "b3866ed2-e7bf-413c-8bb5-5ddd270b6af6", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["from dataclasses import dataclass\n", "\n", "import pandas as pd\n", "from pyspark.sql import SparkSession, DataFrame\n", "from pyspark.sql.functions import current_timestamp, to_timestamp\n", "from pyspark.sql.types import *\n", "from delta.tables import DeltaTable"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "85994335-7d1c-4381-937b-be2d3deba88f", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["@dataclass\n", "class Config:\n", "    env: str\n", "    entity: str\n", "    jdbc_url: str\n", "    jdbc_user: str = oracle_username      # set via secret / widget\n", "    jdbc_pwd:  str = oracle_password      # set via secret / widget\n", "\n", "    @property\n", "    def jdbc_opts(self):\n", "        return {\n", "            \"url\":       self.jdbc_url,\n", "            \"user\":      self.jdbc_user,\n", "            \"password\":  self.jdbc_pwd,\n", "            \"fetchsize\": \"1000\",\n", "            \"oracle.net.CONNECT_TIMEOUT\": \"10000\",\n", "            \"oracle.net.READ_TIMEOUT\":    \"10000\"\n", "        }\n", "\n", "def get_config(env: str, entity: str) -> Config:\n", "    if env == \"dev\":\n", "        # url = \"**************************************************\"\n", "        # $ nslookup mbclenddb201\n", "        # Server:  MBCLENVMDC02.MBCL.com\n", "        # Address:  ***********\n", "        # Name:    mbclenddb201.mbcl.com\n", "        # Address:  ************\n", "        # url = \"**************************************************\"\n", "        url = \"**************************************************\"\n", "    elif env == \"prod\":\n", "        url = \"***********************************************\" # TO DO\n", "    else:\n", "        raise ValueError(f\"Invalid environment: {env}\")\n", "    return Config(env=env, entity=entity, jdbc_url=url)"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "47cf6464-e273-47cb-bd45-025bdc880572", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["def get_oracle_df(cfg: Config, query: str) -> DataFrame:\n", "    try:\n", "        df = spark.read \\\n", "                .format(\"jdbc\") \\\n", "                .option(\"url\", cfg.jdbc_url) \\\n", "                .option(\"query\", query) \\\n", "                .option(\"user\", cfg.jdbc_user) \\\n", "                .option(\"password\", cfg.jdbc_pwd) \\\n", "                .option(\"fetchsize\", \"10000\") \\\n", "                .option(\"driver\", \"oracle.jdbc.driver.OracleDriver\") \\\n", "                .load()\n", "        return df\n", "    except Exception as e:\n", "        print(f\"Error connecting to {cfg.jdbc_url}: {e}\")\n", "        return None\n", "    \n", "def get_index_name(commodity_name: str):\n", "    # get the index name list based on commodity\n", "    if commodity_name not in [\"Gas\", \"Power\"]:\n", "        return []\n", "\n", "    table_name = f\"mbcl_{env}_bronze.{entity}.bronze_reference_{commodity_name.lower()}\"\n", "    index_column = \"ol_code\"\n", "    df = spark.table(table_name)\n", "    distinct_entries = df.select(index_column).distinct().collect()\n", "    index_names = [row[index_column] for row in distinct_entries]\n", "    return index_names"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "7c0ccf04-60c5-4811-ae35-360820911e38", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["cfg = get_config(env, entity)\n", "for commodity in [\"Gas\", \"Power\"]:\n", "    index_names = get_index_name(commodity)\n", "\n", "    # Define commodity products\n", "    products = get_index_name(commodity)\n", "    products_list = \", \".join(f\"'{gp}'\" for gp in products)\n", "\n", "    sql_query = f\"\"\"\n", "        SELECT \n", "            market_date, contract_date, price, index_name\n", "        FROM \n", "            arch.ol_market_price\n", "        WHERE index_name in ({products_list})\n", "    \"\"\"\n", "    df = get_oracle_df(cfg, sql_query)\n", "    if df:\n", "        # add timestamp column\n", "        current_ts =current_timestamp()\n", "        df = df.withColumn(\"ingestion_timestamp\", current_ts)\n", "\n", "        # convert column names to lowercase\n", "        df = df.toDF(*[col.lower() for col in df.columns])\n", "        \n", "        df.write.format(\"delta\").mode(\"overwrite\").saveAsTable(f\"mbcl_{env}_bronze.{entity}.bronze_market_price_{commodity.lower()}\")\n", "        print(f\"Table mbcl_{env}_bronze.{entity}.bronze_market_price_{commodity.lower()} created.\")\n", "\n", "        # add an initial watermark entry in silver\n", "        current_ts = spark.range(1).select(current_ts.alias(\"ts\")).collect()[0][\"ts\"]"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "inputWidgetPreferences": null, "language": "python", "notebookMetadata": {"pythonIndentUnit": 4}, "notebookName": "initial_db_ingestion", "widgets": {"entity": {"currentValue": "ipv", "nuid": "8df280c8-262b-4c5c-896a-7589a32e98c7", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "", "label": null, "name": "entity", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "", "label": null, "name": "entity", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}, "env": {"currentValue": "dev", "nuid": "915b6f9a-2f12-4145-b988-302e78f7882c", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "", "label": null, "name": "env", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "", "label": null, "name": "env", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}