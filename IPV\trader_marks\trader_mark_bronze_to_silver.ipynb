{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "1602e716-81ee-4984-941e-67949727a0de", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# --- Widgets ---\n", "dbutils.widgets.text(\"env\", \"\")\n", "dbutils.widgets.text(\"entity\", \"\")\n", "\n", "env = dbutils.widgets.get(\"env\").lower()\n", "entity = dbutils.widgets.get(\"entity\").lower()"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "9c79c796-302e-4b10-9ef4-35c8ca23f753", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["from pyspark.sql import SparkSession, DataFrame\n", "from pyspark.sql import functions as F\n", "from pyspark.sql.functions import col, lit, year, month, to_timestamp, array, sha2, concat_ws\n", "\n", "import logging"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "238347b7-bf0e-4d40-a650-74ef8ab1af89", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["\n", "# ───────────────────────── LOGGING ────────────────────────\n", "_log = logging.getLogger(\"ingest\")\n", "if not _log.handlers:\n", "    h = logging.StreamHandler()\n", "    h.setFormatter(logging.Formatter(\"%(asctime)s [%(levelname)s] %(message)s\",\n", "                                     \"%Y-%m-%d %H:%M:%S\"))\n", "    _log.add<PERSON><PERSON>ler(h)\n", "    _log.setLevel(logging.INFO)"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "aa0207d4-79bb-4da9-adfb-77426056fd42", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["def get_ol_index_names():\n", "    # We need to keep only the ol_code index names for GAS\n", "    table_name = f\"mbcl_{env}_bronze.{entity}.bronze_reference_gas\"\n", "    df = spark.table(table_name)\n", "    if df:\n", "        results = df.select(\"ol_code\").distinct().collect()\n", "        if results:\n", "            return [res[\"ol_code\"] for res in results]\n", "    return []\n", "\n", "def get_reference_columns(commodity_name: str, join_column: str, columns_to_fetch: str, df_bronze: DataFrame):\n", "    # join_column will be ol_code for Gas and power_ol_code for Power\n", "    table = f\"mbcl_{env}_bronze.{entity}.bronze_reference_{commodity_name.lower()}\"\n", "    selected_columns = [join_column] + columns_to_fetch\n", "\n", "    df = spark.table(table) # the table containing the columns that needs to be fetched\n", "    df_enriched = df_bronze.join(df.select(*selected_columns), on=join_column, how=\"inner\")\n", "    return df_enriched\n", "    "]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "b53b557d-b23b-48a2-a051-d770cdcb8756", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["MARKET_PRICE_POWER_PK = ['trade_date', 'contract_date', 'ol_code']\n", "MARKET_PRICE_GAS_PK = ['trade_date', 'contract_date', 'ol_code']\n", " "]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "998c075b-72d3-4cd3-87f9-dda63359a6dd", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# keep only IF for Gas\n", "for commodity in [\"Gas\", \"Power\"]:\n", "    pipeline_name = f\"bronze_market_price_{commodity.lower()}\"\n", "    try:\n", "        last_ts = spark.read.table(f\"mbcl_{env}_silver.{entity}.silver_watermark\") \\\n", "            .filter(F.col(\"pipeline_name\") == pipeline_name) \\\n", "            .select(\"last_processed\") \\\n", "            .collect()[0][\"last_processed\"]\n", "    except:\n", "        last_ts = None\n", "    _log.info(f\"{commodity}: Latest timestamp ingested: {last_ts}\")\n", "    \n", "    df_bronze = spark.table(f\"mbcl_{env}_bronze.{entity}.bronze_market_price_{commodity.lower()}\")\n", "    if last_ts:\n", "        df_bronze = df_bronze.filter(<PERSON><PERSON>col(\"ingestion_timestamp\") > last_ts)\n", "\n", "    new_rows_count = df_bronze.count()\n", "    if new_rows_count == 0:\n", "        _log.info(f\"No new rows to process for commodity: {commodity}\")\n", "        continue\n", "    \n", "    # rename existing columns\n", "    df_bronze = df_bronze.withColumnRenamed(\"market_date\", \"trade_date\")\n", "    df_bronze = df_bronze.withColumnRenamed(\"price\", \"mbcl_value\")\n", "    df_bronze = df_bronze.withColumnRenamed(\"index_name\", \"ol_code\")\n", "\n", "    if commodity == \"Power\":\n", "        bronze_primary_key = MARKET_PRICE_POWER_PK\n", "        df_bronze = get_reference_columns(commodity, \"ol_code\", [\"ice_code\", \"region\", \"zone\", \"location_hub\", \"rt\", \"da\", \"pk\", \"op\"], df_bronze)\n", "\n", "    if commodity == \"Gas\":\n", "        bronze_primary_key = MARKET_PRICE_GAS_PK\n", "\n", "        # get only the ol_code index names\n", "        ol_index_names = get_ol_index_names()\n", "        df_bronze = df_bronze.filter(<PERSON>.col(\"ol_code\").isin(ol_index_names))\n", "\n", "        df_bronze = get_reference_columns(commodity, \"ol_code\", [\"ice_code\", \"location_hub\", \"region\"], df_bronze)\n", "\n", "    df_bronze = df_bronze.withColumn(\"primary_key\", sha2(concat_ws(\"||\", *bronze_primary_key), 256))\n", "\n", "    # convert contract_date to timestamp and overwrite the original column\n", "    df_bronze = df_bronze.withColumn(\"contract_date\", to_timestamp(\"contract_date\"))\n", "\n", "    # extract year and month from the timestamp\n", "    df_bronze = df_bronze.withColumn(\"year\", year(\"contract_date\"))\n", "    df_bronze = df_bronze.withColumn(\"month\", month(\"contract_date\"))\n", "\n", "    df_bronze.write.format(\"delta\").mode(\"append\").saveAsTable(f\"mbcl_{env}_silver.{entity}.silver_market_price_{commodity.lower()}\")\n", "    _log.info(f\"Writing {new_rows_count} rows from commodity:{commodity} to table: mbcl_{env}_silver.{entity}.silver_market_price_{commodity.lower()}\")\n", "\n", "    # update silver watermark table with latest timestamp\n", "    new_last_ts = df_bronze.agg(F.max(\"ingestion_timestamp\").alias(\"max_ts\")).collect()[0][\"max_ts\"]\n", "\n", "    watermark_update = spark.createDataFrame(\n", "        [(pipeline_name, new_last_ts)],\n", "        [\"pipeline_name\", \"last_processed\"]\n", "    )\n", "\n", "    (watermark_update\n", "        .write\n", "        .mode(\"overwrite\")\n", "        .option(\"replaceWhere\", f\"pipeline_name = '{pipeline_name}'\")\n", "        .saveAsTable(f\"mbcl_{env}_silver.{entity}.silver_watermark\")\n", "    )\n"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "inputWidgetPreferences": null, "language": "python", "notebookMetadata": {"pythonIndentUnit": 4}, "notebookName": "trader_mark_bronze_to_silver", "widgets": {"entity": {"currentValue": "ipv", "nuid": "31bf3c64-f23e-4f57-a86f-e62c73843970", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "", "label": null, "name": "entity", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "", "label": null, "name": "entity", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}, "env": {"currentValue": "dev", "nuid": "47cea7db-f789-4d89-b06c-13f0c11d8b7f", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "", "label": null, "name": "env", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "", "label": null, "name": "env", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}