name: Deploy to TEST

on:
  workflow_dispatch:
    inputs:
      use_case:
        description: 'Select use case to deploy (shared folder is always included)'
        required: true
        type: choice
        options:
          - IPV
          - MOS
          - XVA
          - all
      comments:
        description: 'Deployment comments (optional)'
        required: false
        type: string

env:
  REQUESTS_CA_BUNDLE: /etc/ssl/certs/ca-certificates.crt

jobs:
  deploy-to-test:
    runs-on: itdev-ubuntu-latest
    name: Deploy to TEST Environment
    environment: test
    permissions:
      contents: read
      actions: read
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Add MBCL FW cert
        run: |
          echo "$MBCL_FW_CA_CERT" > mbcl_fw_ca_cert.crt
          sudo cp mbcl_fw_ca_cert.crt /usr/local/share/ca-certificates/firewall-ca.mcrm.net.crt
          sudo update-ca-certificates
        env:
          MBCL_FW_CA_CERT: ${{ secrets.MBCL_FW_CA_CERT }}
      
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
      
      - name: Install Python dependencies
        run: |
          pip install requests
      
      - name: Install Databricks CLI
        run: |
          echo "Installing Databricks CLI..."
          
          # Try the official installer
          curl -fsSL https://raw.githubusercontent.com/databricks/setup-cli/main/install.sh | sh
          
          # The installer puts the CLI in /usr/local/bin/databricks
          # Verify installation
          if databricks --version; then
            echo "✅ Databricks CLI installation verified"
          else
            echo "❌ Databricks CLI installation failed"
            exit 1
          fi
      
      - name: Display Deployment Plan
        run: |
          echo "================================================"
          echo "TEST DEPLOYMENT PLAN"
          echo "================================================"
          echo "Environment: TEST"
          echo "Use Case: ${{ github.event.inputs.use_case }}"
          echo "Triggered by: ${{ github.actor }}"
          echo "Comments: ${{ github.event.inputs.comments }}"
          echo ""
          echo "Folders to deploy:"
          echo "  ✓ src/shared (always deployed)"
          
          if [ "${{ github.event.inputs.use_case }}" = "all" ]; then
            echo "  ✓ src/IPV"
            echo "  ✓ src/MOS"
            echo "  ✓ src/XVA"
          else
            echo "  ✓ src/${{ github.event.inputs.use_case }}"
          fi
          echo "================================================"
      
      
      - name: Deploy Selected Use Cases to TEST
        env:
          DATABRICKS_HOST: ${{ vars.DATABRICKS_HOST }}
          DATABRICKS_TOKEN: ${{ secrets.DATABRICKS_TOKEN }}
        run: |
          echo "Deploying selected use cases to TEST..."
          echo "Use Case: ${{ github.event.inputs.use_case }}"
          
          # Create temporary directory for deployment
          TEMP_DIR="./temp-deploy-$(date +%s)"
          mkdir -p "$TEMP_DIR"
          
          # Always copy shared folder
          cp -r src/shared "$TEMP_DIR/"
          
          # Copy selected use case(s)
          if [ "${{ github.event.inputs.use_case }}" = "all" ]; then
            echo "Deploying ALL use cases..."
            cp -r src/IPV "$TEMP_DIR/"
            cp -r src/MOS "$TEMP_DIR/"
            cp -r src/XVA "$TEMP_DIR/"
          else
            echo "Deploying ${{ github.event.inputs.use_case }} only..."
            cp -r src/${{ github.event.inputs.use_case }} "$TEMP_DIR/"
          fi
          
          # Ensure deployment directories exist
          databricks workspace mkdirs /Workspace/Deployments/test/files/src --profile DEFAULT || true
          
          # Upload shared folder
          echo "Uploading shared folder..."
          databricks workspace import-dir \
            "$TEMP_DIR/shared" \
            "/Workspace/Deployments/test/files/src/shared" \
            --profile DEFAULT \
            --overwrite
          
          # Upload use cases based on selection
          if [ "${{ github.event.inputs.use_case }}" = "all" ]; then
            echo "Uploading IPV..."
            databricks workspace import-dir \
              "$TEMP_DIR/IPV" \
              "/Workspace/Deployments/test/files/src/IPV" \
              --profile DEFAULT \
              --overwrite

            echo "Uploading MOS..."
            databricks workspace import-dir \
              "$TEMP_DIR/MOS" \
              "/Workspace/Deployments/test/files/src/MOS" \
              --profile DEFAULT \
              --overwrite

            echo "Uploading XVA..."
            databricks workspace import-dir \
              "$TEMP_DIR/XVA" \
              "/Workspace/Deployments/test/files/src/XVA" \
              --profile DEFAULT \
              --overwrite
          elif [ "${{ github.event.inputs.use_case }}" = "IPV" ]; then
            echo "Uploading IPV..."
            databricks workspace import-dir \
              "$TEMP_DIR/IPV" \
              "/Workspace/Deployments/test/files/src/IPV" \
              --profile DEFAULT \
              --overwrite
          elif [ "${{ github.event.inputs.use_case }}" = "MOS" ]; then
            echo "Uploading MOS..."
            databricks workspace import-dir \
              "$TEMP_DIR/MOS" \
              "/Workspace/Deployments/test/files/src/MOS" \
              --profile DEFAULT \
              --overwrite
          elif [ "${{ github.event.inputs.use_case }}" = "XVA" ]; then
            echo "Uploading XVA..."
            databricks workspace import-dir \
              "$TEMP_DIR/XVA" \
              "/Workspace/Deployments/test/files/src/XVA" \
              --profile DEFAULT \
              --overwrite
          fi
          
          # Clean up temp directory
          rm -rf "$TEMP_DIR"
          
          # Deploy cluster configuration
          if [ "${{ github.event.inputs.use_case }}" = "IPV" ]; then
            TARGET="test-ipv"
          elif [ "${{ github.event.inputs.use_case }}" = "MOS" ]; then
            TARGET="test-mos"
          elif [ "${{ github.event.inputs.use_case }}" = "XVA" ]; then
            TARGET="test-xva"
          else
            TARGET="test-all"
          fi
          
          echo "Deploying cluster configuration with target: $TARGET"
          echo "Running bundle validate..."
          databricks bundle validate -t $TARGET || echo "Validation warnings (continuing)"
          
          echo "Running bundle deploy for cluster resources..."
          # Since we already uploaded files manually, just deploy the resources (clusters)
          # The bundle deploy will skip file sync since files already exist
          databricks bundle deploy -t $TARGET --auto-approve
          
          echo "Verifying cluster creation..."
          databricks clusters list --profile DEFAULT | grep test-cluster || echo "Cluster may take time to appear"
          
          echo "✅ Deployment completed to TEST"
      
      - name: Verify Deployment
        env:
          DATABRICKS_HOST: ${{ vars.DATABRICKS_HOST }}
          DATABRICKS_TOKEN: ${{ secrets.DATABRICKS_TOKEN }}
        run: |
          echo "Verifying deployment..."
          echo "----------------------------------------"
          
          # Determine the target for verification
          if [ "${{ github.event.inputs.use_case }}" = "IPV" ]; then
            TARGET="test-ipv"
          elif [ "${{ github.event.inputs.use_case }}" = "MOS" ]; then
            TARGET="test-mos"
          elif [ "${{ github.event.inputs.use_case }}" = "XVA" ]; then
            TARGET="test-xva"
          else
            TARGET="test-all"
          fi
          
          # List deployed assets (bundle creates files/ subfolder)
          echo "Bundle deployment structure:"
          databricks workspace list /Workspace/Deployments/test --profile DEFAULT || echo "⚠️ Test folder not found"
          echo ""
          
          echo "Checking deployed notebooks:"
          echo "Shared folder:"
          databricks workspace list /Workspace/Deployments/test/files/src/shared --profile DEFAULT || echo "⚠️ Shared folder not found at expected location"
          echo ""
          
          if [ "${{ github.event.inputs.use_case }}" = "all" ]; then
            echo "IPV:"
            databricks workspace list /Workspace/Deployments/test/files/src/IPV --profile DEFAULT || echo "⚠️ IPV not found"
            echo ""
            echo "MOS:"
            databricks workspace list /Workspace/Deployments/test/files/src/MOS --profile DEFAULT || echo "⚠️ MOS not found"
            echo ""
            echo "XVA:"
            databricks workspace list /Workspace/Deployments/test/files/src/XVA --profile DEFAULT || echo "⚠️ XVA not found"
          elif [ "${{ github.event.inputs.use_case }}" = "IPV" ]; then
            echo "IPV:"
            databricks workspace list /Workspace/Deployments/test/files/src/IPV --profile DEFAULT || echo "⚠️ IPV not found"
          elif [ "${{ github.event.inputs.use_case }}" = "MOS" ]; then
            echo "MOS:"
            databricks workspace list /Workspace/Deployments/test/files/src/MOS --profile DEFAULT || echo "⚠️ MOS not found"
          elif [ "${{ github.event.inputs.use_case }}" = "XVA" ]; then
            echo "XVA:"
            databricks workspace list /Workspace/Deployments/test/files/src/XVA --profile DEFAULT || echo "⚠️ XVA not found"
          fi
          
          echo ""
          echo "Checking cluster configuration..."
          databricks clusters list --profile DEFAULT | grep test-cluster || echo "⚠️ test-cluster not found"
      
      - name: Run Validation Tests
        env:
          DATABRICKS_HOST: ${{ vars.DATABRICKS_HOST }}
          DATABRICKS_TOKEN: ${{ secrets.DATABRICKS_TOKEN }}
        run: |
          # Make deploy script executable
          chmod +x devops/scripts/deploy.sh || true
          
          # Run validation script
          python devops/scripts/validate_deployment.py \
            --env test \
            --host "$DATABRICKS_HOST" \
            --use-case "${{ github.event.inputs.use_case }}" || echo "⚠️ Validation script not found or failed"
      
      - name: Deployment Summary
        run: |
          echo "## ✅ TEST Deployment Completed" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Deployment Information" >> $GITHUB_STEP_SUMMARY
          echo "- **Environment:** TEST" >> $GITHUB_STEP_SUMMARY
          echo "- **Use Case:** ${{ github.event.inputs.use_case }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Status:** ✅ Success" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Deployed Structure" >> $GITHUB_STEP_SUMMARY
          echo "Bundle deployment creates the following structure:" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ \`/Workspace/Deployments/test/files/src/shared\`" >> $GITHUB_STEP_SUMMARY
          
          USE_CASE="${{ github.event.inputs.use_case }}"
          if [ "$USE_CASE" = "all" ]; then
            echo "- ✅ \`/Workspace/Deployments/test/files/src/IPV\`" >> $GITHUB_STEP_SUMMARY
            echo "- ✅ \`/Workspace/Deployments/test/files/src/MOS\`" >> $GITHUB_STEP_SUMMARY
            echo "- ✅ \`/Workspace/Deployments/test/files/src/XVA\`" >> $GITHUB_STEP_SUMMARY
          elif [ "$USE_CASE" = "IPV" ]; then
            echo "- ✅ \`/Workspace/Deployments/test/files/src/IPV\`" >> $GITHUB_STEP_SUMMARY
          elif [ "$USE_CASE" = "MOS" ]; then
            echo "- ✅ \`/Workspace/Deployments/test/files/src/MOS\`" >> $GITHUB_STEP_SUMMARY
          elif [ "$USE_CASE" = "XVA" ]; then
            echo "- ✅ \`/Workspace/Deployments/test/files/src/XVA\`" >> $GITHUB_STEP_SUMMARY
          fi
          
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Next Steps" >> $GITHUB_STEP_SUMMARY
          echo "1. Validate the deployment in TEST environment" >> $GITHUB_STEP_SUMMARY
          echo "2. Run any necessary tests" >> $GITHUB_STEP_SUMMARY
          echo "3. Deploy to PROD when ready" >> $GITHUB_STEP_SUMMARY