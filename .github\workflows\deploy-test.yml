name: Deploy to TEST

on:
  workflow_dispatch:
    inputs:
      use_case:
        description: 'Select use case to deploy (shared folder is always included)'
        required: true
        type: choice
        options:
          - usecase-1
          - usecase-2
          - all
      comments:
        description: 'Deployment comments (optional)'
        required: false
        type: string

jobs:
  deploy-to-test:
    runs-on: ubuntu-latest
    name: Deploy to TEST Environment
    environment: test
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
      
      - name: Install Python dependencies
        run: |
          pip install requests
      
      - name: Install Databricks CLI
        run: |
          echo "Installing Databricks CLI..."
          
          # Try the official installer
          curl -fsSL https://raw.githubusercontent.com/databricks/setup-cli/main/install.sh | sh
          
          # The installer puts the CLI in /usr/local/bin/databricks
          # Verify installation
          if databricks --version; then
            echo "✅ Databricks CLI installation verified"
          else
            echo "❌ Databricks CLI installation failed"
            exit 1
          fi
      
      - name: Display Deployment Plan
        run: |
          echo "================================================"
          echo "TEST DEPLOYMENT PLAN"
          echo "================================================"
          echo "Environment: TEST"
          echo "Use Case: ${{ github.event.inputs.use_case }}"
          echo "Triggered by: ${{ github.actor }}"
          echo "Comments: ${{ github.event.inputs.comments }}"
          echo ""
          echo "Folders to deploy:"
          echo "  ✓ src/shared (always deployed)"
          
          if [ "${{ github.event.inputs.use_case }}" = "all" ]; then
            echo "  ✓ src/usecase-1"
            echo "  ✓ src/usecase-2"
          else
            echo "  ✓ src/${{ github.event.inputs.use_case }}"
          fi
          echo "================================================"
      
      
      - name: Deploy Selected Use Cases to TEST
        env:
          DATABRICKS_HOST: ${{ vars.DATABRICKS_HOST }}
          DATABRICKS_TOKEN: ${{ secrets.DATABRICKS_TOKEN }}
        run: |
          echo "Deploying selected use cases to TEST..."
          echo "Use Case: ${{ github.event.inputs.use_case }}"
          
          # Create temporary directory for deployment
          TEMP_DIR="./temp-deploy-$(date +%s)"
          mkdir -p "$TEMP_DIR"
          
          # Always copy shared folder
          cp -r src/shared "$TEMP_DIR/"
          
          # Copy selected use case(s)
          if [ "${{ github.event.inputs.use_case }}" = "all" ]; then
            echo "Deploying ALL use cases..."
            cp -r src/usecase-1 "$TEMP_DIR/"
            cp -r src/usecase-2 "$TEMP_DIR/"
          else
            echo "Deploying ${{ github.event.inputs.use_case }} only..."
            cp -r src/${{ github.event.inputs.use_case }} "$TEMP_DIR/"
          fi
          
          # Ensure deployment directories exist
          databricks workspace mkdirs /Workspace/Deployments/test/files/src --profile DEFAULT || true
          
          # Upload shared folder
          echo "Uploading shared folder..."
          databricks workspace import-dir \
            "$TEMP_DIR/shared" \
            "/Workspace/Deployments/test/files/src/shared" \
            --profile DEFAULT \
            --overwrite
          
          # Upload use cases based on selection
          if [ "${{ github.event.inputs.use_case }}" = "all" ]; then
            echo "Uploading usecase-1..."
            databricks workspace import-dir \
              "$TEMP_DIR/usecase-1" \
              "/Workspace/Deployments/test/files/src/usecase-1" \
              --profile DEFAULT \
              --overwrite
            
            echo "Uploading usecase-2..."
            databricks workspace import-dir \
              "$TEMP_DIR/usecase-2" \
              "/Workspace/Deployments/test/files/src/usecase-2" \
              --profile DEFAULT \
              --overwrite
          elif [ "${{ github.event.inputs.use_case }}" = "usecase-1" ]; then
            echo "Uploading usecase-1..."
            databricks workspace import-dir \
              "$TEMP_DIR/usecase-1" \
              "/Workspace/Deployments/test/files/src/usecase-1" \
              --profile DEFAULT \
              --overwrite
          elif [ "${{ github.event.inputs.use_case }}" = "usecase-2" ]; then
            echo "Uploading usecase-2..."
            databricks workspace import-dir \
              "$TEMP_DIR/usecase-2" \
              "/Workspace/Deployments/test/files/src/usecase-2" \
              --profile DEFAULT \
              --overwrite
          fi
          
          # Clean up temp directory
          rm -rf "$TEMP_DIR"
          
          # Deploy cluster configuration
          if [ "${{ github.event.inputs.use_case }}" = "usecase-1" ]; then
            TARGET="test-uc1"
          elif [ "${{ github.event.inputs.use_case }}" = "usecase-2" ]; then
            TARGET="test-uc2"
          else
            TARGET="test-all"
          fi
          
          echo "Deploying cluster configuration with target: $TARGET"
          echo "Running bundle validate..."
          databricks bundle validate -t $TARGET || echo "Validation warnings (continuing)"
          
          echo "Running bundle deploy for cluster resources..."
          # Since we already uploaded files manually, just deploy the resources (clusters)
          # The bundle deploy will skip file sync since files already exist
          databricks bundle deploy -t $TARGET --auto-approve
          
          echo "Verifying cluster creation..."
          databricks clusters list --profile DEFAULT | grep test-cluster || echo "Cluster may take time to appear"
          
          echo "✅ Deployment completed to TEST"
      
      - name: Verify Deployment
        env:
          DATABRICKS_HOST: ${{ vars.DATABRICKS_HOST }}
          DATABRICKS_TOKEN: ${{ secrets.DATABRICKS_TOKEN }}
        run: |
          echo "Verifying deployment..."
          echo "----------------------------------------"
          
          # Determine the target for verification
          if [ "${{ github.event.inputs.use_case }}" = "usecase-1" ]; then
            TARGET="test-uc1"
          elif [ "${{ github.event.inputs.use_case }}" = "usecase-2" ]; then
            TARGET="test-uc2"
          else
            TARGET="test-all"
          fi
          
          # List deployed assets (bundle creates files/ subfolder)
          echo "Bundle deployment structure:"
          databricks workspace list /Workspace/Deployments/test --profile DEFAULT || echo "⚠️ Test folder not found"
          echo ""
          
          echo "Checking deployed notebooks:"
          echo "Shared folder:"
          databricks workspace list /Workspace/Deployments/test/files/src/shared --profile DEFAULT || echo "⚠️ Shared folder not found at expected location"
          echo ""
          
          if [ "${{ github.event.inputs.use_case }}" = "all" ]; then
            echo "UseCase-1:"
            databricks workspace list /Workspace/Deployments/test/files/src/usecase-1 --profile DEFAULT || echo "⚠️ usecase-1 not found"
            echo ""
            echo "UseCase-2:"
            databricks workspace list /Workspace/Deployments/test/files/src/usecase-2 --profile DEFAULT || echo "⚠️ usecase-2 not found"
          elif [ "${{ github.event.inputs.use_case }}" = "usecase-1" ]; then
            echo "UseCase-1:"
            databricks workspace list /Workspace/Deployments/test/files/src/usecase-1 --profile DEFAULT || echo "⚠️ usecase-1 not found"
          elif [ "${{ github.event.inputs.use_case }}" = "usecase-2" ]; then
            echo "UseCase-2:"
            databricks workspace list /Workspace/Deployments/test/files/src/usecase-2 --profile DEFAULT || echo "⚠️ usecase-2 not found"
          fi
          
          echo ""
          echo "Checking cluster configuration..."
          databricks clusters list --profile DEFAULT | grep test-cluster || echo "⚠️ test-cluster not found"
      
      - name: Run Validation Tests
        env:
          DATABRICKS_HOST: ${{ vars.DATABRICKS_HOST }}
          DATABRICKS_TOKEN: ${{ secrets.DATABRICKS_TOKEN }}
        run: |
          # Make deploy script executable
          chmod +x devops/scripts/deploy.sh || true
          
          # Run validation script
          python devops/scripts/validate_deployment.py \
            --env test \
            --host "$DATABRICKS_HOST" \
            --use-case "${{ github.event.inputs.use_case }}" || echo "⚠️ Validation script not found or failed"
      
      - name: Deployment Summary
        run: |
          echo "## ✅ TEST Deployment Completed" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Deployment Information" >> $GITHUB_STEP_SUMMARY
          echo "- **Environment:** TEST" >> $GITHUB_STEP_SUMMARY
          echo "- **Use Case:** ${{ github.event.inputs.use_case }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Status:** ✅ Success" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Deployed Structure" >> $GITHUB_STEP_SUMMARY
          echo "Bundle deployment creates the following structure:" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ \`/Workspace/Deployments/test/files/src/shared\`" >> $GITHUB_STEP_SUMMARY
          
          USE_CASE="${{ github.event.inputs.use_case }}"
          if [ "$USE_CASE" = "all" ]; then
            echo "- ✅ \`/Workspace/Deployments/test/files/src/usecase-1\`" >> $GITHUB_STEP_SUMMARY
            echo "- ✅ \`/Workspace/Deployments/test/files/src/usecase-2\`" >> $GITHUB_STEP_SUMMARY
          elif [ "$USE_CASE" = "usecase-1" ]; then
            echo "- ✅ \`/Workspace/Deployments/test/files/src/usecase-1\`" >> $GITHUB_STEP_SUMMARY
          elif [ "$USE_CASE" = "usecase-2" ]; then
            echo "- ✅ \`/Workspace/Deployments/test/files/src/usecase-2\`" >> $GITHUB_STEP_SUMMARY
          fi
          
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Next Steps" >> $GITHUB_STEP_SUMMARY
          echo "1. Validate the deployment in TEST environment" >> $GITHUB_STEP_SUMMARY
          echo "2. Run any necessary tests" >> $GITHUB_STEP_SUMMARY
          echo "3. Deploy to PROD when ready" >> $GITHUB_STEP_SUMMARY