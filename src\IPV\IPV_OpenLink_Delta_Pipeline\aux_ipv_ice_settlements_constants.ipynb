{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "53e5ee3c-0665-4778-b6b7-89fd8e0e0c3d", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["from pyspark.sql import types as T"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "e0b978ce-a92a-4f7d-bf97-eefb34951f90", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["ol_delta_report_gas_schema = T.StructType([\n", "    <PERSON><PERSON>('Contract', T.<PERSON>(), True),\n", "    <PERSON><PERSON>('NG_MICHCON_IF', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_NWROCK_IF', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_REXZ3_IF', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_ANRLA_IF', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_NGPLMC_IF', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_TGPZ4L2_IF', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_TRNLEIDY_REC_GD', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_SUMAS_IF', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_PGE_NGI', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_TETCOM3_IF', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_SSTXOKKS_IF', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_TRNZ6_GD', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_TGTZ1_IF', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_FGTZ3_GD', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_TRNZ6_NNY_IF', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_CGMAIN_IF', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_TRNZ4_IF', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_TRNZ6_NNY_GD', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_AGT_IF', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_TRNLEIDY_REC_IF', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_TGPZ4L3_IF', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_TRNZ5S_IF', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_TETCOM2REC_IF', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_TRNZ5_IF', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_DOMAP_IF', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_REXZ3_GD', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_SOCALCG_NGI', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_PERMIAN_IF', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_HEHUB_IF', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_FGTZ3_IF', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_OGT_IF', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_CGTAP_IF', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_WAHA_IF', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_HSC_IF', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_CIGRK_IF', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_CHICG_NGI', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_ANROK_IF', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_TRNZ3_IF', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_TRNZ6_IF', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_AECO_CGPR', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_TETCOELA_IF', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_TENLA5_IF', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_SOCAL_NGI', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_MALIN_NGI', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_SONAT_IF', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_CNTPT_IF', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_NNGVENT_IF', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_PEPL_IF', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_NNGDMK_IF', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_NGPTO_IF', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_DAWN_IF', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_TETCOM3_GD', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_DOMAP_GD', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_MALIN_GD', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_TENLA5_GD', T.StringT<PERSON>(), True),\n", "    <PERSON><PERSON>('NG_PEPL_GD', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_SANJUAN_GD', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_SANJUAN_IF', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_HEHUB_GD', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_CHICG_GD', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_HSC_GD', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_SONAT_GD', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_OGT_GD', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_WAHA_GD', T.StringT<PERSON>(), True),\n", "    <PERSON><PERSON>('NG_NYMEX', T.StringType(), True),\n", "    <PERSON><PERSON>('IR_SOFR_3M.USD', T.StringType(), True),\n", "    <PERSON><PERSON>('Total', T<PERSON>(), True),\n", "    <PERSON><PERSON>('RunT<PERSON>l', T<PERSON>(), True),\n", "    <PERSON><PERSON>('NG_MICHCON_IF_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_NWROCK_IF_RT', T.StringT<PERSON>(), True),\n", "    <PERSON><PERSON>('NG_REXZ3_IF_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_ANRLA_IF_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_NGPLMC_IF_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_TGPZ4L2_IF_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_TRNLEIDY_REC_GD_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_SUMAS_IF_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_PGE_NGI_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_TETCOM3_IF_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_SSTXOKKS_IF_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_TRNZ6_GD_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_TGTZ1_IF_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_FGTZ3_GD_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_TRNZ6_NNY_IF_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_CGMAIN_IF_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_TRNZ4_IF_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_TRNZ6_NNY_GD_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_AGT_IF_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_TRNLEIDY_REC_IF_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_TGPZ4L3_IF_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_TRNZ5S_IF_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_TETCOM2REC_IF_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_TRNZ5_IF_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_DOMAP_IF_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_REXZ3_GD_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_SOCALCG_NGI_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_PERMIAN_IF_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_HEHUB_IF_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_FGTZ3_IF_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_OGT_IF_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_CGTAP_IF_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_WAHA_IF_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_HSC_IF_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_CIGRK_IF_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_CHICG_NGI_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_ANROK_IF_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_TRNZ3_IF_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_TRNZ6_IF_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_AECO_CGPR_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_TETCOELA_IF_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_TENLA5_IF_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_SOCAL_NGI_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_MALIN_NGI_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_SONAT_IF_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_CNTPT_IF_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_NNGVENT_IF_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_PEPL_IF_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_NNGDMK_IF_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_NGPTO_IF_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_DAWN_IF_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_TETCOM3_GD_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_DOMAP_GD_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_MALIN_GD_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_TENLA5_GD_RT', T.StringT<PERSON>(), True),\n", "    <PERSON><PERSON>('NG_PEPL_GD_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_SANJUAN_GD_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_SANJUAN_IF_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_HEHUB_GD_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_CHICG_GD_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_HSC_GD_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_SONAT_GD_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_OGT_GD_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('NG_WAHA_GD_RT', T.StringT<PERSON>(), True),\n", "    <PERSON><PERSON>('NG_NYMEX_RT', T.StringType(), True),\n", "    <PERSON><PERSON>('IR_SOFR_3M.USD_RT', T.StringType(), True),\n", "])\n", "\n", "ol_delta_report_power_schema = T.StructType([\n", "    <PERSON><PERSON>('Contract', T.<PERSON>(), True),\n", "\t<PERSON><PERSON>('NG_TETCOM3_IF', T.StringType(), True),\n", "\t<PERSON><PERSON>('NG_CHICG_NGI', T.StringType(), True),\n", "\t<PERSON><PERSON>('NG_TRNZ6_GD', T.StringType(), True),\n", "\t<PERSON><PERSON>('NG_DOMAP_IF', T.StringType(), True),\n", "\t<PERSON><PERSON>('NG_TRNZ6_IF', T.StringType(), True),\n", "\t<PERSON><PERSON>('NG_TRNZ6_NNY_GD', T.StringType(), True),\n", "\t<PERSON><PERSON>('NG_TETCOM3_GD', T.StringType(), True),\n", "\t<PERSON><PERSON>('NG_DOMAP_GD', T.StringType(), True),\n", "\t<PERSON><PERSON>('NG_TENLA5_GD', T.StringT<PERSON>(), True),\n", "\t<PERSON><PERSON>('NG_CHICG_GD', T.StringType(), True),\n", "\t<PERSON><PERSON>('NG_NYMEX', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_METED_ZONE_OP_DA_2X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PSEG_ZONE_OP_DA_2X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PSEG_ZONE_PK_DA_5X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PSEG_ZONE_OP_DA_7X8', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_BGE_ZONE_PK_DA_5X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_NEPOOL_MHUB_PK_DA_5X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_METED_ZONE_OP_DA_7X8', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_METED_ZONE_PK_DA_5X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_NEPOOL_MHUB_OP_DA_2X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_NEPOOL_MHUB_OP_DA_7X8', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PENELEC_ZONE_OP_DA_2X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_BGE_ZONE_OP_DA_7X8', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_MISO_INDYHUB_PK_DA_5X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_APS_ZONE_OP_DA_7X8', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_APS_ZONE_PK_DA_5X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PENELEC_ZONE_PK_DA_5X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PENELEC_ZONE_OP_DA_7X8', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_BGE_ZONE_OP_DA_2X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_JCPL_ZONE_OP_DA_2X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_JCPL_ZONE_OP_DA_7X8', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_JCPL_ZONE_PK_DA_5X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_NEPOOL_NEMA_PK_DA_5X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_MISO_INDYHUB_OP_DA_2X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_COMED_ZONE_OP_DA_2X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_COMED_ZONE_OP_DA_7X8', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_COMED_ZONE_PK_DA_5X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PEPCO_ZONE_OP_DA_7X8', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PEPCO_ZONE_OP_DA_2X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_AEP_ZONE_OP_DA_7X8', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PEPCO_ZONE_PK_DA_5X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_DUQ_ZONE_PK_DA_5X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_DUQ_ZONE_OP_DA_2X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_AEP_ZONE_PK_DA_5X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_AEP_ZONE_OP_DA_2X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_MISO_INDYHUB_OP_DA_7X8', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_NEPOOL_NEMA_OP_DA_2X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_MISO_INDYHUB_PK_RT_5X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_MISO_INDYHUB_OP_RT_2X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_MISO_INDYHUB_OP_RT_7X8', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_NEPOOL_NEMA_OP_DA_7X8', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_NYISO_C_PK_DA_5X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_NYISO_C_OP_DA_2X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_NYISO_C_OP_DA_7X8', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_APS_ZONE_OP_DA_2X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PPL_ZONE_OP_DA_7X8', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PALO_OP_DA_7X8', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PALO_OP_DA_1X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_CAISO_SP15_OP_DA_7X8', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_CAISO_NP15_OP_DA_7X8', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_MIDC_OP_DA_7X8', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_CAISO_NP15_PK_DA_6X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_CAISO_SP15_PK_DA_6X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_CAISO_NP15_OP_DA_1X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_MIDC_PK_DA_6X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_MIDC_OP_DA_1X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_CAISO_SP15_OP_DA_1X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PALO_PK_DA_6X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PJM_WH_OP_DA_2X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PJM_WH_OP_RT_2X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PJM_WH_OP_RT_7X8', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PJM_WH_PK_RT_5X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_NYISO_J_OP_DA_2X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PJM_WH_OP_DA_7X8', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PJM_WH_PK_DA_5X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_NYISO_J_OP_DA_7X8', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_NYISO_J_PK_DA_5X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_NYISO_G_PK_DA_5X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PPL_ZONE_PK_DA_5X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_NYISO_G_OP_DA_2X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PJM_NI_OP_RT_7X8', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PJM_NI_PK_DA_5X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PJM_NI_OP_RT_2X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PJM_NI_PK_RT_5X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PECO_ZONE_PK_DA_5X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PJM_NI_OP_DA_2X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PECO_ZONE_OP_DA_2X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PECO_ZONE_OP_DA_7X8', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PPL_ZONE_OP_DA_2X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PJM_NI_OP_DA_7X8', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PJM_AD_OP_DA_7X8', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_NYISO_G_OP_DA_7X8', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_NYISO_A_OP_DA_2X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_NYISO_A_PK_DA_5X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_NYISO_A_OP_DA_7X8', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PJM_AD_PK_RT_5X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PJM_AD_PK_DA_5X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PJM_AD_OP_RT_2X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PJM_AD_OP_RT_7X8', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PJM_AD_OP_DA_2X16', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_DUQ_ZONE_OP_DA_7X8', T.StringType(), True),\n", "\t<PERSON><PERSON>('IR_SOFR_3M_USD', T.StringType(), True),\n", "\t<PERSON><PERSON>('Total', T<PERSON>(), True),\n", "\t<PERSON><PERSON>('RunT<PERSON>l', T<PERSON>(), True),\n", "\t<PERSON><PERSON>('NG_TETCOM3_IF_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('NG_CHICG_NGI_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('NG_TRNZ6_GD_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('NG_DOMAP_IF_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('NG_TRNZ6_IF_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('NG_TRNZ6_NNY_GD_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('NG_TETCOM3_GD_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('NG_DOMAP_GD_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('NG_TENLA5_GD_RT', T.StringT<PERSON>(), True),\n", "\t<PERSON><PERSON>('NG_CHICG_GD_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('NG_NYMEX_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_METED_ZONE_OP_DA_2X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PSEG_ZONE_OP_DA_2X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PSEG_ZONE_PK_DA_5X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PSEG_ZONE_OP_DA_7X8_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_BGE_ZONE_PK_DA_5X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_NEPOOL_MHUB_PK_DA_5X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_METED_ZONE_OP_DA_7X8_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_METED_ZONE_PK_DA_5X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_NEPOOL_MHUB_OP_DA_2X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_NEPOOL_MHUB_OP_DA_7X8_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PENELEC_ZONE_OP_DA_2X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_BGE_ZONE_OP_DA_7X8_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_MISO_INDYHUB_PK_DA_5X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_APS_ZONE_OP_DA_7X8_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_APS_ZONE_PK_DA_5X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PENELEC_ZONE_PK_DA_5X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PENELEC_ZONE_OP_DA_7X8_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_BGE_ZONE_OP_DA_2X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_JCPL_ZONE_OP_DA_2X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_JCPL_ZONE_OP_DA_7X8_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_JCPL_ZONE_PK_DA_5X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_NEPOOL_NEMA_PK_DA_5X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_MISO_INDYHUB_OP_DA_2X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_COMED_ZONE_OP_DA_2X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_COMED_ZONE_OP_DA_7X8_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_COMED_ZONE_PK_DA_5X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PEPCO_ZONE_OP_DA_7X8_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PEPCO_ZONE_OP_DA_2X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_AEP_ZONE_OP_DA_7X8_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PEPCO_ZONE_PK_DA_5X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_DUQ_ZONE_PK_DA_5X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_DUQ_ZONE_OP_DA_2X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_AEP_ZONE_PK_DA_5X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_AEP_ZONE_OP_DA_2X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_MISO_INDYHUB_OP_DA_7X8_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_NEPOOL_NEMA_OP_DA_2X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_MISO_INDYHUB_PK_RT_5X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_MISO_INDYHUB_OP_RT_2X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_MISO_INDYHUB_OP_RT_7X8_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_NEPOOL_NEMA_OP_DA_7X8_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_NYISO_C_PK_DA_5X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_NYISO_C_OP_DA_2X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_NYISO_C_OP_DA_7X8_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_APS_ZONE_OP_DA_2X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PPL_ZONE_OP_DA_7X8_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PALO_OP_DA_7X8_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PALO_OP_DA_1X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_CAISO_SP15_OP_DA_7X8_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_CAISO_NP15_OP_DA_7X8_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_MIDC_OP_DA_7X8_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_CAISO_NP15_PK_DA_6X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_CAISO_SP15_PK_DA_6X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_CAISO_NP15_OP_DA_1X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_MIDC_PK_DA_6X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_MIDC_OP_DA_1X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_CAISO_SP15_OP_DA_1X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PALO_PK_DA_6X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PJM_WH_OP_DA_2X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PJM_WH_OP_RT_2X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PJM_WH_OP_RT_7X8_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PJM_WH_PK_RT_5X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_NYISO_J_OP_DA_2X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PJM_WH_OP_DA_7X8_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PJM_WH_PK_DA_5X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_NYISO_J_OP_DA_7X8_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_NYISO_J_PK_DA_5X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_NYISO_G_PK_DA_5X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PPL_ZONE_PK_DA_5X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_NYISO_G_OP_DA_2X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PJM_NI_OP_RT_7X8_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PJM_NI_PK_DA_5X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PJM_NI_OP_RT_2X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PJM_NI_PK_RT_5X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PECO_ZONE_PK_DA_5X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PJM_NI_OP_DA_2X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PECO_ZONE_OP_DA_2X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PECO_ZONE_OP_DA_7X8_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PPL_ZONE_OP_DA_2X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PJM_NI_OP_DA_7X8_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PJM_AD_OP_DA_7X8_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_NYISO_G_OP_DA_7X8_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_NYISO_A_OP_DA_2X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_NYISO_A_PK_DA_5X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_NYISO_A_OP_DA_7X8_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PJM_AD_PK_RT_5X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PJM_AD_PK_DA_5X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PJM_AD_OP_RT_2X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PJM_AD_OP_RT_7X8_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_PJM_AD_OP_DA_2X16_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('PWR_DUQ_ZONE_OP_DA_7X8_RT', T.StringType(), True),\n", "\t<PERSON><PERSON>('IR_SOFR_3M_USD_RT', T.StringType(), True),\n", "])\n", "\n", "quarantine_log_schema = T.StructType([\n", "    <PERSON><PERSON>(\"file_path\", T.StringType(), True),\n", "    <PERSON><PERSON>(\"batch_id\", T<PERSON>(), True),\n", "    <PERSON><PERSON>(\"reason\", <PERSON><PERSON>(), True),\n", "    <PERSON><PERSON>(\"timestamp\", T.TimestampType(), True),\n", "    <PERSON><PERSON>(\"moved\", <PERSON><PERSON>(), True)\n", "])"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "89606954-e02a-43a9-a274-6f6b82646950", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["def print_schema_of_table(table):\n", "    for field in table.schema.fields:\n", "        print(\"\\tT.StructField('\" + field.name.upper() + \"', T.\" + field.dataType.__class__.__name__ + \"(), True),\")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "f505c04f-4e2e-4506-9b31-84e590c3ca51", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": []}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "inputWidgetPreferences": null, "language": "python", "notebookMetadata": {"pythonIndentUnit": 4}, "notebookName": "aux_ipv_ice_settlements_constants", "widgets": {}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}