# Databricks notebook source
# MAGIC %md
# MAGIC
# MAGIC # 📘 xva{gold_catalog}.{schema_name}Dataset_MissingTradesReport – Documentation
# MAGIC
# MAGIC This notebook identifies **missing or mismatched trades** between <PERSON><PERSON><PERSON>'s `xva_tradeeod` and <PERSON><PERSON><PERSON><PERSON>'s `xva_tradedatareconciliation` datasets by reconciling `deal_tracking_num` values.
# MAGIC
# MAGIC ---
# MAGIC
# MAGIC ## 📦 Libraries Used
# MAGIC
# MAGIC ```python
# MAGIC from pyspark.sql import SparkSession
# MAGIC from pyspark.sql.functions import col, udf, date_format
# MAGIC from pyspark.sql.types import StringType
# MAGIC from datetime import datetime
# MAGIC ```
# MAGIC
# MAGIC ---
# MAGIC
# MAGIC ## 🚀 Step 1 – Initialize Spark & Load Source Tables
# MAGIC
# MAGIC ```python
# MAGIC spark = SparkSession.builder.appName("TradeReconciliation").getOrCreate()
# MAGIC
# MAGIC tradeeod = spark.table("{bronze_catalog}.{schema_name}.tradeeod")
# MAGIC tradereconciliation = spark.table("{bronze_catalog}.{schema_name}.tradedatareconciliation")
# MAGIC ```
# MAGIC
# MAGIC ---
# MAGIC
# MAGIC ## 🛠️ Step 2 – Helper UDF: Strip Suffix After Underscore
# MAGIC
# MAGIC ```python
# MAGIC def remove_underscore_and_after(s):
# MAGIC     return str(s).split('_')[0]
# MAGIC
# MAGIC remove_underscore_udf = udf(remove_underscore_and_after, StringType())
# MAGIC ```
# MAGIC
# MAGIC 🔹 **Purpose**: Normalizes `deal_tracking_num` by removing suffixes such as `_1`, `_T1`, etc., for accurate comparison.
# MAGIC
# MAGIC ---
# MAGIC
# MAGIC ## 📅 Step 3 – Capture Business Dates
# MAGIC
# MAGIC ```python
# MAGIC business_date = tradereconciliation.select('business_date').distinct().collect()[0]['business_date']
# MAGIC trade_date = tradeeod.select('trade_date').distinct().collect()[0]['trade_date']
# MAGIC ```
# MAGIC
# MAGIC ---
# MAGIC
# MAGIC ## 📊 Step 4 – Filter Out Underscored Deals
# MAGIC
# MAGIC ```python
# MAGIC tradeeod_filtered = tradeeod.filter(~col("deal_tracking_num").contains("_"))
# MAGIC tradereconciliation_filtered = tradereconciliation.filter(~col("deal_tracking_num").contains("_"))
# MAGIC ```
# MAGIC
# MAGIC ---
# MAGIC
# MAGIC ## 🔢 Step 5 – Count Distinct Deals in Both Datasets
# MAGIC
# MAGIC ```python
# MAGIC total_tradeeod_deals = tradeeod_filtered.select('deal_tracking_num').distinct().count()
# MAGIC total_tradereconciliation_deals = tradereconciliation_filtered.select('deal_tracking_num').distinct().count()
# MAGIC
# MAGIC print(f"Distinct count in tradeeod: {total_tradeeod_deals}")
# MAGIC print(f"Distinct count in tradereconciliation: {total_tradereconciliation_deals}")
# MAGIC ```
# MAGIC
# MAGIC
# MAGIC ---
# MAGIC
# MAGIC ## ✅ Summary
# MAGIC
# MAGIC This notebook supports XVA trade validation by:
# MAGIC - Filtering malformed deals
# MAGIC - Counting unique deal entries
# MAGIC - Laying the foundation for detecting breaks between systems
# MAGIC

# COMMAND ----------

dbutils.widgets.text("env", "")
dbutils.widgets.text("entity", "")

env = dbutils.widgets.get("env").lower()
entity = dbutils.widgets.get("entity").upper()

bronze_catalog = f"mbcl_{env}_bronze"
gold_catalog = f"mbcl_{env}_gold"
schema_name = entity.lower()



# COMMAND ----------

# MAGIC %run ./Utils_functions

# COMMAND ----------

from pyspark.sql import SparkSession
from pyspark.sql.functions import udf, col
from pyspark.sql.types import StringType

# Initialize Spark session
spark = SparkSession.builder.appName("RemoveUnderscore").getOrCreate()

# Define the function
def remove_underscore_and_after(s):
    return str(s).split('_')[0]

# Register the UDF
remove_underscore_udf = udf(remove_underscore_and_after, StringType())



# COMMAND ----------

# Check if all required tables for Dataset_MissingTradesReport are present

used_tables = [
  f"{bronze_catalog}.{schema_name}.tradeeod",
  f"{bronze_catalog}.{schema_name}.tradedatareconciliation"
]

if not tables_exists(used_tables):
  dbutils.notebook.exit(f"At least one of the following tables is missing: {used_tables}")

# COMMAND ----------



from pyspark.sql import SparkSession
from pyspark.sql.functions import col, date_format, to_timestamp
from pyspark.sql.types import StructType, StructField, StringType, IntegerType, DateType, TimestampType
from datetime import datetime

# Initialize Spark session
spark = SparkSession.builder.appName("TradeReconciliation").getOrCreate()

# Load the data
tradeeod = spark.table(f"{bronze_catalog}.{schema_name}.tradeeod")
tradereconciliation = spark.table(f"{bronze_catalog}.{schema_name}.tradedatareconciliation")

tradeeodcnt = tradeeod.count()
print(f"Distinct count in tradeeodtab: {tradeeodcnt}")

# # Define business_date and trade_date from the respective tables
business_date = tradereconciliation.select('businessDate').distinct().collect()[0]['businessDate']
trade_date = tradeeod.select('tradeDate').distinct().collect()[0]['tradeDate']

# Count total deals
# Filter tradeeod DataFrame to exclude rows containing "_"
tradeeod_filtered = tradeeod.filter(~col("dealtrackingnum").contains("_"))
tradeeod_filtered_cnt = tradeeod_filtered.count()


# Filter tradereconciliation DataFrame to exclude rows containing "_"
tradereconciliation_filtered = tradereconciliation.filter(~col("dealtrackingnum").contains("_"))


total_tradeeod_deals = tradeeod_filtered.select('dealtrackingnum').distinct().count()
total_tradereconciliation_deals = tradereconciliation_filtered.select('dealtrackingnum').distinct().count()


# # Output results
print(f"Distinct count in tradeeod: {total_tradeeod_deals}")
print(f"Distinct count in tradereconciliation: {total_tradereconciliation_deals}")

# Apply the UDF to the specified field
tradeeod = tradeeod.withColumn('cleaned_deal_tracking_num', remove_underscore_udf(tradeeod['dealtrackingnum']))
tradereconciliation = tradereconciliation.withColumn('cleaned_deal_tracking_num', remove_underscore_udf(tradereconciliation['dealtrackingnum']))

# Find matches and missing deals
matches = tradeeod.join(tradereconciliation, on='cleaned_deal_tracking_num', how='inner')
missing_in_tradeeod = tradereconciliation.join(tradeeod, on='cleaned_deal_tracking_num', how='left_anti')
missing_in_tradereconciliation = tradeeod.join(tradereconciliation, on='cleaned_deal_tracking_num', how='left_anti')


summary_report = [
    {
        'Report_System': 'Quantifi',
        'Total_Trades': matches.select('cleaned_deal_tracking_num').distinct().count() + missing_in_tradeeod.select('cleaned_deal_tracking_num').distinct().count(),
        'Total_Matched': matches.select('cleaned_deal_tracking_num').distinct().count(),
        'Total_Missing': missing_in_tradeeod.select('cleaned_deal_tracking_num').distinct().count(),
        'Business_date': business_date
    },
    {
        'Report_System': 'Mitsui',
        'Total_Trades': matches.select('cleaned_deal_tracking_num').distinct().count() + missing_in_tradereconciliation.select('cleaned_deal_tracking_num').distinct().count(),
        'Total_Matched': matches.select('cleaned_deal_tracking_num').distinct().count(),
        'Total_Missing': missing_in_tradereconciliation.select('cleaned_deal_tracking_num').distinct().count(),
        'Business_date': business_date
    }
]


# Define the schema for the summary report
schema = StructType([
    StructField('Report_System', StringType(), True),
    StructField('Total_Trades', IntegerType(), True),
    StructField('Total_Matched', IntegerType(), True),
    StructField('Total_Missing', IntegerType(), True),
    StructField('Business_date', StringType(), True)
])

# Convert to Spark DataFrame using the defined schema
summary_df = spark.createDataFrame(summary_report, schema=schema)
summary_df = cast_to_inferred_type(summary_df); 

summary_df.write.format("delta").mode("overwrite").option("overwriteSchema", "true").saveAsTable(f"{gold_catalog}.{schema_name}.missingtrades_summaryreport")


# COMMAND ----------


from pyspark.sql import SparkSession
from pyspark.sql.functions import col
from datetime import datetime

# Initialize Spark session
spark = SparkSession.builder.appName("TradeReconciliation").getOrCreate()

# Load the data
tradeeod = spark.table(f'{bronze_catalog}.{schema_name}.tradeeod')
tradereconciliation = spark.table(f'{bronze_catalog}.{schema_name}.tradedatareconciliation')

# Count total deals
total_tradeeod_deals = tradeeod.select('dealtrackingnum').distinct().count()
total_tradereconciliation_deals = tradereconciliation.select('dealtrackingnum').distinct().count()

# Apply the UDF to the specified field
tradeeod = tradeeod.withColumn('cleaned_deal_tracking_num', remove_underscore_udf(tradeeod['dealtrackingnum']))
tradereconciliation = tradereconciliation.withColumn('cleaned_deal_tracking_num', remove_underscore_udf(tradereconciliation['dealtrackingnum']))

# Find matches and missing deals
missing_in_tradeeod = tradereconciliation.join(tradeeod, on='cleaned_deal_tracking_num', how='left_anti')
missing_in_tradereconciliation = tradeeod.join(tradereconciliation, on='cleaned_deal_tracking_num', how='left_anti')

# Rename the column
tradeeod = tradeeod.withColumnRenamed('cleaned_deal_tracking_num', 'dealtrackingnum')
tradereconciliation = tradereconciliation.withColumnRenamed('cleaned_deal_tracking_num', 'dealtrackingnum')

# Define the distinct fields you want to select
missing_trades_report_mitsui_distinct_fields = [
    'businessDate',
    'dealTrackingNum',
    'internallEntity',
    'externallEntity',
    'externallEntityId',
    'brokerId',
    'internalPortfolio',
    'internalContact',
    'buySell',
    'toolset',
    'insType',
    'holdingInsRef',
    'tradeCurrency',
    'position',
    'price',
    'tradeDate',
    'tradeStartDate',
    'tradeEndDate',
    'mtm'
]

# Define the distinct fields you want to select
missing_trades_report_quantifi_distinct_fields = [
    'tradeDate',
    'dealTrackingNum',
    'internallEntity',
    'externallEntity',
    'externalEntityId',
    'brokerId',
    'internalPortfolio',
    'internalContact',
    'buySell',
    'toolset',
    'insType',
    'holdingInsRef',
    'tradeCurrency',
    'position',
    'price',
    'tradeRate',
    'tradeStartDate',
    'tradeEndDate',
    'dateTradeMatures'
]


# Select the distinct fields for missing deals in tradereconciliation
missing_trades_report_mitsui = missing_in_tradeeod.select(*missing_trades_report_mitsui_distinct_fields).distinct()
missing_trades_report_mitsui = cast_to_inferred_type(missing_trades_report_mitsui)

missing_trades_report_quantifi = missing_in_tradereconciliation.select(*missing_trades_report_quantifi_distinct_fields).distinct()
missing_trades_report_quantifi = cast_to_inferred_type(missing_trades_report_quantifi)

missing_trades_report_quantifi.write.format("delta").mode("overwrite").option("overwriteSchema", "true").saveAsTable(f"{gold_catalog}.{schema_name}.TradesMissingInQuantifiFile")

missing_trades_report_mitsui.write.format("delta").mode("overwrite").option("overwriteSchema", "true").saveAsTable(f"{gold_catalog}.{schema_name}.TradesMissingInMitusiFile")


