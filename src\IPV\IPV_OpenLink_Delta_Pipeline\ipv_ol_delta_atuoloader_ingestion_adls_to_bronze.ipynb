{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "65a911c8-a8c5-4129-919d-20a3f4fa16c0", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["dbutils.widgets.text(\"ipv_bronze_catalog\", \"mbcl_dev_bronze\", \"ipv_bronze_catalog\")\n", "dbutils.widgets.text(\"ipv_bronze_schema\", \"ipv\", \"ipv_bronze_schema\")\n", "dbutils.widgets.text(\"moving_files_to_quarantine\", \"no\", \"moving_files_to_quarantine\")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "9c2537dd-bc45-4f66-91ab-b7dccd77ce89", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["import pyspark.sql.functions as F\n", "import pyspark.sql.types as T\n", "from pyspark.sql import DataFrame\n", "# from pyspark.sql.streaming import Trigger\n", "from datetime import datetime\n", "import re\n", "import json\n", "import os"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "1d95e44a-e7db-4e92-849d-9228c0bb11f7", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["CATALOG = dbutils.widgets.get(\"ipv_bronze_catalog\")\n", "SCHEMA = dbutils.widgets.get(\"ipv_bronze_schema\")\n", "\n", "MOVING_FILES_TO_QUARANTINE = True if dbutils.widgets.get(\"moving_files_to_quarantine\").lower() == \"yes\" else False\n", "\n", "BASE_PATH = '/Volumes/mbcl_dev_bronze/ipv'\n", "INBOUND_PATH = 'temp_inbound'\n", "PROCESSED_PATH = 'temp_processed'\n", "QUARANTINE_PATH = 'temp_quarantine'\n", "\n", "REFERENCE_GAS_COLUMNS = ['Gas HUB', 'ICE CODE', 'OL Code', 'GD Code']\n", "REFERENCE_POWER_COLUMNS = ['Power OL Name', 'ICE CODE', 'Region']"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "9a2cc15e-32ae-4ba9-ba54-76fb40535a2d", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%run ./aux_ipv_ice_settlements_constants"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "8e76a791-11f5-4080-8225-86acae7f16ec", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# %run ../IPV_Settlements_Prices_Pipeline/ipv_ice_settlements_constants"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "b36cb509-31e8-4dbc-bf12-ed1466886349", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["def clean_column_name(col_name):\n", "    return re.sub(r'[^a-zA-Z0-9_]', '_', col_name.strip()).lower()\n", "\n", "def clean_columns(df):\n", "    return df.toDF(*[clean_column_name(col_name) for col_name in df.columns])\n", "    \n", "def autoload_csv(\n", "    source_path, bronze_table, checkpoint_path, \n", "    schema_path, quarantine_path, quarantine_log_table, \n", "    schema=None):\n", "    \"\"\"\n", "    Auto Loader CSV with file-level quarantine:\n", "    - if any row is bad, quarantine the entire file\n", "    - if all rows are good, write to bronze table\n", "    - logs quarantined files to a delta table for traceability\n", "    - supports schema evolutio\n", "    - supports reprocessing of quarantined fiels later\n", "    \"\"\"\n", "    if schema is None:\n", "        print(\"Using autoloader inferred schema.\")\n", "        try:\n", "            with open(schema_path + f\"/_schemas/{schema_path.split('/')[-1]}\", 'r') as f:\n", "                schema = f.read()\n", "            \n", "            schema = T.StructType.fromJson(schema)\n", "        except:\n", "            inferred_df = spark.read \\\n", "                .format('csv') \\\n", "                .option('header', 'true') \\\n", "                .option(\"skipRows\", 1) \\\n", "                .load(source_path)\n", "            schema = inferred_df.schema\n", "            if not os.path.exists(schema_path + '/_schemas/'):\n", "                os.makedirs(schema_path + '/_schemas/')\n", "\n", "            with open(schema_path + f\"/_schemas/{schema_path.split('/')[-1]}\", 'w') as f:\n", "                f.write(json.dumps(schema.jsonValue()))\n", "    else:\n", "        print(\"Using provided tables schema.\")\n", "\n", "    df = (\n", "        spark.readStream\n", "        .format('cloudFiles')\n", "        .option('cloudFiles.format', 'csv')\n", "        .option(\"badRecordsPath\", quarantine_path)\n", "        .option('cloudFiles.schemaLocation', schema_path)\n", "        .option('header', 'true')\n", "        .option(\"skipRows\", 1)\n", "        .schema(schema)\n", "        .load(source_path)\n", "    )\n", "\n", "    df = clean_columns(df)\n", "\n", "    # remove rows where contract is null; as those rows are not linked to any date so they are not usefull in calculations\n", "    df = df.filter(\n", "        (df.contract.isNotNull()) & \n", "        (F.trim(df.contract) != \"\")\n", "    )\n", "\n", "    df = (\n", "        df\n", "        .withColumn('ingestion_timestamp', F.current_timestamp())\n", "        .withColumn('_file_path', F.col('_metadata.file_path'))\n", "    )\n", "\n", "    # Create quarantine log table if not exists\n", "    spark.sql(f\"\"\"\n", "        CREATE TABLE IF NOT EXISTS {quarantine_log_table} (\n", "            file_path STRING,\n", "            batch_id BIGINT,\n", "            reason STRING,\n", "            timestamp TIMESTAMP,\n", "            moved BOOLEAN\n", "        ) USING DELTA  \n", "    \"\"\")\n", "\n", "    # micro batch logic\n", "    def process_batch(batch_df: DataFrame, batch_id: int):\n", "        if batch_df.isEmpty():\n", "            return\n", "        \n", "        files = [r[\"_file_path\"] for r in batch_df.select(\"_file_path\").distinct().collect()]\n", "\n", "        print(files)\n", "        metadata_list = []\n", "        for file_path in files:\n", "            local_path = file_path.replace(\"dbfs:\", \"/dbfs\").replace('%20', ' ')\n", "            with open(local_path, \"r\") as f:\n", "                metadata = f.readline().strip().split(',')[0]\n", "                metadata_list.append((file_path, metadata))\n", "\n", "        metadata_df = spark.createDataFrame(metadata_list, [\"_file_path\", \"first_row_date\"])\n", "\n", "        try:\n", "            bad_df = spark.read \\\n", "                .format('json') \\\n", "                .load(f'{quarantine_path}/bad_records') \\\n", "                .select(\"path\").distinct()\n", "\n", "            bad_files = [r[\"path\"] for r in bad_df.select(\"path\").collect()]\n", "        except Exception as e:\n", "            bad_files = []\n", "\n", "        quarantiend = []\n", "\n", "        for file in files:\n", "            if file in bad_files:\n", "                quarantiend.append(file)\n", "                \n", "                spark \\\n", "                    .createDataFrame(\n", "                        [(file, batch_id, \"Bad row detected\", datetime.now(), False)],\n", "                        schema=quarantine_log_schema\n", "                    ) \\\n", "                    .write \\\n", "                    .mode(\"append\") \\\n", "                    .saveAsTable(quarantine_log_table)\n", "            else:\n", "\n", "                (\n", "                    batch_df.filter(batch_df[\"_file_path\"] == file)\n", "                        .join(\n", "                            metadata_df,\n", "                            batch_df[\"_file_path\"] == metadata_df[\"_file_path\"],\n", "                            \"left\"\n", "                        )\n", "                        .drop(\"_file_path\")\n", "                        .write.format(\"delta\")\n", "                        .mode(\"append\")\n", "                        .saveAsTable(bronze_table)\n", "                )\n", "        if quarantiend:\n", "            print(f\"Batch {batch_id} quarantined {len(quarantiend)} files: {quarantiend}\")\n", "        else:\n", "            print(f\"Batch {batch_id} processed succesfull\")\n", "\n", "    query = (df.writeStream\n", "        .foreachBatch(process_batch)\n", "        .option('checkpointLocation', checkpoint_path)\n", "        .trigger(availableNow=True)\n", "        .start()\n", "    )\n", "\n", "    return query\n", "\n", "def move_files(quarantine_log_table):\n", "    quarantine_log_df = \\\n", "        spark.sql(f\"\"\"\n", "            SELECT * FROM {quarantine_log_table}\n", "            WHERE moved = false\"\"\"\n", "        ).collect()\n", "\n", "    for row in quarantine_log_df:\n", "        file_path = row[\"file_path\"]\n", "        path_split = row.file_path.split(\"/\")\n", "        if len(path_split) < 2:\n", "            print(f\"Invalid file path: {file_path}\")\n", "            continue\n", "        filename = path_split[-1]\n", "        file_folder = path_split[-2]\n", "        print(file_path, filename)\n", "        dbutils.fs.mv(file_path, f\"{BASE_PATH}/temp_quarantine/{file_folder}/{filename}\")\n", "        \n", "    spark.sql(f\"\"\" UPDATE {quarantine_log_table} SET moved = true \"\"\")\n"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "9d40ebf1-704b-437f-84c2-c87727d569b6", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["files = [\n", "    (\"ol_delta_report_gas\", \"bronze_ol_delta_report_gas\", None),\n", "    (\"ol_delta_report_power\", \"bronze_ol_delta_report_power\", None),\n", "]"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "79c6d44b-2c28-4e3c-bd7a-aef41435f636", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["for name, current_table, current_table_schema in files:\n", "    autoload_csv(\n", "        f\"{BASE_PATH}/temp_inbound/{name}\",\n", "        f\"{CATALOG}.{SCHEMA}.{current_table}\",\n", "        f\"{BASE_PATH}/temp_checkpoints/{name}\",\n", "        f\"{BASE_PATH}/temp_schema_tracking/{name}\",\n", "        f\"{BASE_PATH}/temp_quarantine/{name}\",\n", "        f\"{CATALOG}.{SCHEMA}.quarantine_log_table\",\n", "        current_table_schema\n", "    )\n", "    # break"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "cea83194-9620-4a9d-afed-6c44e0474076", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["if MOVING_FILES_TO_QUARANTINE:\n", "    move_files(f\"{CATALOG}.{SCHEMA}.quarantine_log_table\")"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "220fa84a-5443-48aa-a45a-6d4a51fc41d6", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["# Code to clean bronze for ipv ol delta for dev and debugging purposes"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "0098800d-86a1-499a-ba22-67df43b8284a", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# tables = spark.sql(\"show tables in mbcl_dev_bronze.ipv\").collect()\n", "\n", "# bronze_tables = [\"bronze_ol_delta_report_gas\", \"bronze_ol_delta_report_power\"]\n", "\n", "# tables = [x for x in tables if x.tableName in bronze_tables]\n", "\n", "# for t in tables:\n", "    \n", "#     table_name = t.tableName\n", "#     print(f\"Dropping table {table_name}\")\n", "#     spark.sql(f\"DROP TABLE IF EXISTS mbcl_dev_bronze.ipv.{table_name}\")"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "inputWidgetPreferences": null, "language": "python", "notebookMetadata": {"pythonIndentUnit": 4}, "notebookName": "ipv_ol_delta_atuoloader_ingestion_adls_to_bronze", "widgets": {"ipv_bronze_catalog": {"currentValue": "mbcl_dev_bronze", "nuid": "7aee83db-3fc6-4e17-8aa8-1b50d4248101", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "mbcl_dev_bronze", "label": "ipv_bronze_catalog", "name": "ipv_bronze_catalog", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "mbcl_dev_bronze", "label": "ipv_bronze_catalog", "name": "ipv_bronze_catalog", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}, "ipv_bronze_schema": {"currentValue": "ipv", "nuid": "a7ba1c03-79b1-46f0-9552-634d746fd02e", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "ipv", "label": "ipv_bronze_schema", "name": "ipv_bronze_schema", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "ipv", "label": "ipv_bronze_schema", "name": "ipv_bronze_schema", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}, "moving_files_to_quarantine": {"currentValue": "no", "nuid": "34890223-afcd-4aeb-a2e8-8132e1c9e876", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "no", "label": "moving_files_to_quarantine", "name": "moving_files_to_quarantine", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "no", "label": "moving_files_to_quarantine", "name": "moving_files_to_quarantine", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}