# Databricks notebook source
# MAGIC %md
# MAGIC # Environment Configuration Manager
# MAGIC Manages environment-specific configurations for the medallion platform

# COMMAND ----------

import json
from datetime import datetime
from typing import Dict, Any, Optional

class EnvironmentConfigManager:
    """
    Manages environment-specific configurations including:
    - Databricks catalog and schema settings
    - Oracle connection parameters
    - Key Vault configurations
    - Performance settings
    """
    
    def __init__(self):
        self.supported_environments = ["dev", "test", "prod"]
        self.supported_source_systems = ["MOS", "XVA"]
    
    def get_databricks_config(self, environment: str, source_system: str, catalog_override: str = None) -> Dict[str, Any]:
        """Get Databricks configuration for environment with layer-specific catalogs"""

        # Layer-specific catalog configuration
        env_configs = {
            "dev": {
                "catalogs": {
                    "bronze": "mbcl_dev_bronze",
                    "silver": "mbcl_dev_silver",
                    "gold": "mbcl_dev_gold",
                    "utility": "mbcl_dev_utility"
                },
                "cluster_config": {
                    "node_type": "i3.xsmall",
                    "min_workers": 1,
                    "max_workers": 2
                }
            },
            "test": {
                "catalogs": {
                    "bronze": "mbcl_test_bronze",
                    "silver": "mbcl_test_silver",
                    "gold": "mbcl_test_gold",
                    "utility": "mbcl_test_utility"
                },
                "cluster_config": {
                    "node_type": "i3.xsmall",
                    "min_workers": 1,
                    "max_workers": 2
                }
            },
            "prod": {
                "catalogs": {
                    "bronze": "mbcl_prod_bronze",
                    "silver": "mbcl_prod_silver",
                    "gold": "mbcl_prod_gold",
                    "utility": "mbcl_prod_utility"
                },
                "cluster_config": {
                    "node_type": "i3.xsmall",
                    "min_workers": 1,
                    "max_workers": 3

                }
            }
        }

        config = env_configs.get(environment, env_configs["dev"])

        # Create schemas configuration based on source system
        source_schema = source_system.lower()  # e.g., "mos" or "xva"

        config["schemas"] = {
            "bronze": source_schema,      # e.g., "mos" in mbcl_dev_bronze
            "silver": source_schema,      # e.g., "mos" in mbcl_dev_silver
            "gold": source_schema,        # e.g., "mos" in mbcl_dev_gold
            "metadata": "metadata"        # "metadata" in mbcl_dev_utility
        }

        # Override catalogs if provided (for backward compatibility)
        if catalog_override and catalog_override.strip():
            # If override provided, use it for all layers (legacy mode)
            override_catalog = catalog_override.strip()
            config["catalogs"] = {
                "bronze": override_catalog,
                "silver": override_catalog,
                "gold": override_catalog,
                "utility": override_catalog
            }
            config["schemas"] = {
                "bronze": "bronze",
                "silver": "silver",
                "gold": "gold",
                "metadata": "metadata"
            }
            print(f"📝 Using catalog override (legacy mode): {override_catalog}")

        return config
    
    def get_oracle_config(self, environment: str, source_system: str) -> Dict[str, Any]:
        """Get Oracle connection configuration"""
        
        # Environment-specific Oracle configurations
        oracle_configs = {
            "dev": {
                "MOS": {
                    "host": "************",
                    "port": "1539",
                    "database": "DLN330_ITDEV",
                    "username_key": "oracle-mos-username",
                    "password_key": "oracle-mos-password",
                    "key_vault_scope": "akv_dataplatform",
                    "schema": "mosprod"
                },
                "XVA": {
                    "host": "************",
                    "port": "1539", 
                    "database": "XVA_ITDEV",
                    "username_key": "oracle-xva-username",
                    "password_key": "oracle-xva-password",
                    "key_vault_scope": "akv_dataplatform"
                }
            },
            "test": {
                "MOS": {
                    "host": "************",
                    "port": "1539",
                    "database": "DLN330_TEST",
                    "username_key": "oracle-mos-username",
                    "password_key": "oracle-mos-password",
                    "key_vault_scope": "akv_dataplatform",
                    "schema": "mosprod"
                },
                "XVA": {
                    "host": "************",
                    "port": "1539",
                    "database": "XVA_TEST", 
                    "username_key": "oracle-xva-username",
                    "password_key": "oracle-xva-password",
                    "key_vault_scope": "akv_dataplatform"
                }
            },
            "prod": {
                "MOS": {
                    "host": "************",
                    "port": "1526",
                    "database": "PLN010A",
                    "username_key": "oracle-mos-username",
                    "password_key": "oracle-mos-password",
                    "key_vault_scope": "akv_dataplatform",
                    "schema": "mosprod"
                },
                "XVA": {
                    "host": "************",
                    "port": "1539",
                    "database": "XVA_PROD",
                    "username_key": "oracle-xva-username", 
                    "password_key": "oracle-xva-password",
                    "key_vault_scope": "akv_dataplatform"
                }
            }
        }
        
        env_config = oracle_configs.get(environment, oracle_configs["dev"])
        return env_config.get(source_system, env_config["MOS"])
    
    def get_performance_config(self, environment: str) -> Dict[str, Any]:
        """Get performance configuration for environment"""
        
        perf_configs = {
            "dev": {
                "batch_size": 10000,
                "max_parallel_jobs": 5,
                "timeout_seconds": 3600,
                "retry_count": 2
            },
            "test": {
                "batch_size": 5000,
                "max_parallel_jobs": 3,
                "timeout_seconds": 1800,
                "retry_count": 1
            },
            "prod": {
                "batch_size": 20000,
                "max_parallel_jobs": 10,
                "timeout_seconds": 7200,
                "retry_count": 3
            }
        }
        
        return perf_configs.get(environment, perf_configs["dev"])
    
    def get_credentials(self, oracle_config: Dict[str, Any]) -> Dict[str, str]:
        """Get Oracle credentials from Key Vault"""
        
        try:
            username = dbutils.secrets.get(
                scope=oracle_config["key_vault_scope"],
                key=oracle_config["username_key"]
            )
            password = dbutils.secrets.get(
                scope=oracle_config["key_vault_scope"], 
                key=oracle_config["password_key"]
            )
            
            return {
                "username": username,
                "password": password
            }
            
        except Exception as e:
            print(f"❌ Error retrieving credentials: {str(e)}")
            raise e
    
    def validate_configuration(self, environment: str = None, source_system: str = None) -> bool:
        """Validate configuration parameters"""
        
        if environment and environment not in self.supported_environments:
            print(f"❌ Unsupported environment: {environment}")
            print(f"Supported environments: {self.supported_environments}")
            return False
        
        if source_system and source_system not in self.supported_source_systems:
            print(f"❌ Unsupported source system: {source_system}")
            print(f"Supported source systems: {self.supported_source_systems}")
            return False
        
        # Test Key Vault access if parameters provided
        if environment and source_system:
            try:
                oracle_config = self.get_oracle_config(environment, source_system)
                credentials = self.get_credentials(oracle_config)
                print(f"✅ Configuration validation successful")
                return True
            except Exception as e:
                print(f"❌ Configuration validation failed: {str(e)}")
                return False
        
        return True
    
    def get_complete_config(self, environment: str, source_system: str, catalog_override: str = None) -> Dict[str, Any]:
        """Get complete configuration for environment and source system"""

        # Get all configuration components
        databricks_config = self.get_databricks_config(environment, source_system, catalog_override)
        oracle_config = self.get_oracle_config(environment, source_system)
        performance_config = self.get_performance_config(environment)
        credentials = self.get_credentials(oracle_config)

        # Merge Oracle config with credentials
        oracle_config.update(credentials)

        return {
            "environment": environment,
            "source_system": source_system,
            "databricks": databricks_config,
            "oracle_source": oracle_config,
            "performance": performance_config,
            "runtime_info": {
                "config_loaded_at": str(datetime.now()),
                "total_pipelines": 0  # Will be updated by config manager
            }
        }

# COMMAND ----------

print("✅ Environment Configuration Manager loaded successfully!")
