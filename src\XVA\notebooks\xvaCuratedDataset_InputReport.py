# Databricks notebook source
# DBTITLE 1,Import PySpark Libraries and Functions
# Declare Libraries
from pyspark.sql.functions import to_date, date_format, col, lag, year, month, sum as spark_sum
from pyspark.sql import SparkSession
from pyspark.sql import functions as F
from pyspark.sql import SparkSession
from pyspark.sql.window import Window
from pyspark.sql import types as T

# COMMAND ----------

# MAGIC %run ./Utils_functions

# COMMAND ----------

# Spark Custom Settings
# Set the legacy time parser policy
spark.conf.set("spark.sql.legacy.timeParserPolicy", "LEGACY")

# COMMAND ----------

# DBTITLE 1,Set Environment and Catalog Variables for XVA Schema
dbutils.widgets.text("env", "")
dbutils.widgets.text("entity", "")

env = dbutils.widgets.get("env").lower()
entity = dbutils.widgets.get("entity").upper()

bronze_catalog = f"mbcl_{env}_bronze"
silver_catalog = f"mbcl_{env}_silver"
gold_catalog = f"mbcl_{env}_gold"
schema_name = entity.lower()

# COMMAND ----------

@F.pandas_udf(T.StringType())
def parse_and_format_date(date_series):
    dates = pd.to_datetime(date_series, errors='coerce')
    return dates.dt.strftime('%d-%m-%y').where(dates.notna(), None)

# COMMAND ----------

# Check if all required tables are present
used_tables = [
  f"{bronze_catalog}.{schema_name}.xvasummaryrisksensitivitiesnetbycounterparty"
]

if tables_exists(used_tables):
   # Select the required columns
   result_df_xvasummaryrisksensitivitiesnetbycounterparty = spark.sql(f"""
   select    
      businessdate,
      counterpartyName,
      xvaSens,
      xvaFx01,
      replace(fxCurveName,"USD","") AS Ccy
   from {bronze_catalog}.{schema_name}.xvasummaryrisksensitivitiesnetbycounterparty
   """)

   result_df_xvasummaryrisksensitivitiesnetbycounterparty = cast_to_inferred_type(result_df_xvasummaryrisksensitivitiesnetbycounterparty)
   result_df_xvasummaryrisksensitivitiesnetbycounterparty.write.format("delta").mode("overwrite").option("overwriteSchema", "true").saveAsTable(f"{gold_catalog}.{schema_name}.xvasummaryrisksensitivitiesnetbycounterparty")
else:
   print(f"At least one of the following tables is missing: {used_tables}")

# COMMAND ----------

# DBTITLE 1,Extract and Save Vega Strike Surface Data
# Check if all required tables are present
used_tables = [
  f"{bronze_catalog}.{schema_name}.vwmosvegastrikesurface"
]

if tables_exists(used_tables):
   # Select the required columns
   result_df_xva_vwmosvegastrikesurface = spark.sql(f"""
   select    
      runDate,
      businessdate,
      indexname,
      strike,
      to_date(expiry, 'dd-MMM-yy') as expiry,
      vega 
   from {bronze_catalog}.{schema_name}.vwmosvegastrikesurface
   """)

   result_df_xva_vwmosvegastrikesurface = cast_to_inferred_type(result_df_xva_vwmosvegastrikesurface)
   result_df_xva_vwmosvegastrikesurface.write.format("delta").mode("overwrite").option("overwriteSchema", "true").saveAsTable(f"{gold_catalog}.{schema_name}.vwmosvegastrikesurface")
else:
   print(f"At least one of the following tables is missing: {used_tables}")

# COMMAND ----------

# DBTITLE 1,Aggregate and Save Quantificommodity Vega Data
from pyspark.sql.functions import sum

# Check if all required tables are present
used_tables = [
  f"{bronze_catalog}.{schema_name}.quantificommodityvegareport"
]

if tables_exists(used_tables):
    df_dcr = spark.table(f"{bronze_catalog}.{schema_name}.quantificommodityvegareport")
    result_df_xva_quantificommodityvegareport = df_dcr.select(
        "date",
        "businessdate",
        "index",
        # "strike",
        "expiry",
        "xvacommodityvega"
    )

    # Rename columns
    result_df_xva_quantificommodityvegareport = result_df_xva_quantificommodityvegareport.withColumnRenamed("date", "run_date") \
                .withColumnRenamed("xvacommodityvega", "vega") \
                .withColumnRenamed("index", "index_name")

    # Group by all columns and sum vega values
    grouped_df = result_df_xva_quantificommodityvegareport.groupBy(
        "run_date",
        "businessdate",
        "index_name",
        "expiry"
    ).agg(sum("vega").alias("vega"))


    grouped_df = cast_to_inferred_type(grouped_df)

    # Save the result
    grouped_df.write.format("delta").mode("overwrite").option("overwriteSchema", "true").saveAsTable(f"{gold_catalog}.{schema_name}.quantificommodityvegareport")
else:
    print(f"At least one of the following tables is missing: {used_tables}")


# COMMAND ----------

# DBTITLE 1,Transform and Save Tables from Bronze to Gold Catalog
from pyspark.sql.functions import col, to_date, to_timestamp, expr
from pyspark.sql.types import StringType, DateType, TimestampType
import re

tables = [table.name for table in spark.catalog.listTables(f"{bronze_catalog}.{schema_name}")]

for table in tables:
    df = spark.table(f"{bronze_catalog}.{schema_name}.{table}")
    
    for field in df.schema.fields:  
        fname = field.name
        lname = fname.lower()
        
        if lname.startswith("date") or lname.endswith("date"):
            if isinstance(field.dataType, StringType):
                # Apply transformation only if NOT in dd-MMM-yy format (e.g., 12-Jun-25)
                # Check sample values to decide
                sample = [row[fname] for row in df.select(fname).dropna().limit(10).collect()]
                looks_like_dd_MMM_yy = all(re.match(r"\d{2}-[A-Za-z]{3}-\d{2}$", s) for s in sample if isinstance(s, str))

                if not looks_like_dd_MMM_yy:
                    df = df.withColumn(fname, parse_and_format_date(col(fname)))
            
            elif isinstance(field.dataType, DateType):
                df = df.withColumn(fname, to_timestamp(col(fname)))
            
            # Rename to lowercase
            df = df.withColumnRenamed(fname, lname)
    
    df = cast_to_inferred_type(df)
    df.write.format("delta").mode("overwrite").option("overwriteSchema", "true").saveAsTable(f"{gold_catalog}.{schema_name}.xva_{table}")

