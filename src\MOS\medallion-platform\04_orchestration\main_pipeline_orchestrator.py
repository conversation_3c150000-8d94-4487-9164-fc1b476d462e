# Databricks notebook source
# MAGIC %md
# MAGIC # Main Pipeline Orchestrator
# MAGIC Master orchestration notebook with dynamic parameter support for the medallion architecture pipeline

# COMMAND ----------

# MAGIC %md
# MAGIC ## Setup Widgets for Parameter Passing

# COMMAND ----------

from datetime import datetime

# Create widgets for workflow parameter passing
dbutils.widgets.text("environment", "dev", "Environment (dev/test/prod)")
dbutils.widgets.text("source_system", "MOS", "Source System")
dbutils.widgets.text("run_id", "", "Optional Run ID")
dbutils.widgets.dropdown("run_mode", "full_pipeline", ["full_pipeline", "bronze_only", "silver_only", "bronze_silver"], "Run Mode")
dbutils.widgets.dropdown("parallel_execution", "true", ["true", "false"], "Enable Parallel Execution")
dbutils.widgets.text("max_parallel_jobs", "3", "Maximum Parallel Jobs")
dbutils.widgets.text("catalog_name", "", "Catalog Name Override (optional)")

# Get parameters
environment = dbutils.widgets.get("environment")
source_system = dbutils.widgets.get("source_system")
run_id = dbutils.widgets.get("run_id") or f"orchestrator_{source_system}_{environment}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
run_mode = dbutils.widgets.get("run_mode")
parallel_execution = dbutils.widgets.get("parallel_execution").lower() == "true"
max_parallel_jobs = int(dbutils.widgets.get("max_parallel_jobs"))
catalog_override = dbutils.widgets.get("catalog_name")

print(f"🚀 Starting Pipeline Orchestration")
print(f"Environment: {environment}")
print(f"Source System: {source_system}")
print(f"Run ID: {run_id}")
print(f"Run Mode: {run_mode}")
print(f"Parallel Execution: {parallel_execution}")
print(f"Max Parallel Jobs: {max_parallel_jobs}")
print("="*80)

# COMMAND ----------

# MAGIC %md
# MAGIC ## Load Configuration Framework

# COMMAND ----------

# MAGIC %run ../06_utilities/config_manager

# COMMAND ----------

import json
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

# COMMAND ----------

# MAGIC %md
# MAGIC ## Initialize Configuration and Validation

# COMMAND ----------

# Create configuration manager
config_manager = create_config_manager(environment, source_system, catalog_override)

# Validate configuration
if not config_manager.env_config_manager.validate_configuration():
    print("❌ Configuration validation failed!")
    dbutils.notebook.exit(json.dumps({"status": "config_error", "message": "Configuration validation failed"}))

# Get complete runtime configuration
runtime_config = config_manager.get_complete_runtime_config()

# Extract catalog information for display
catalogs = runtime_config['databricks']['catalogs']
schemas = runtime_config['databricks']['schemas']

print(f"✅ Configuration loaded successfully!")
print(f"Bronze Catalog: {catalogs['bronze']}")
print(f"Silver Catalog: {catalogs['silver']}")
print(f"Gold Catalog: {catalogs['gold']}")
print(f"Utility Catalog: {catalogs['utility']}")
print(f"Source Schema: {schemas['bronze']} (across all layers)")
print(f"Catalog Override: {catalog_override or 'None (using environment default)'}")
print(f"Total Pipelines: {runtime_config['runtime_info']['total_pipelines']}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Create Schemas if Needed

# COMMAND ----------

# Create schemas if they don't exist
print("🏗️ Ensuring schemas exist...")
schema_creation_success = config_manager.create_schemas_if_not_exist()
if not schema_creation_success:
    print("⚠️ Some schemas could not be created, but continuing...")
else:
    print("✅ All schemas are ready")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Define Pipeline Notebooks

# COMMAND ----------

# Define pipeline notebooks with paths and parameters
pipeline_notebooks = {
    "bronze": [
        {
            "name": "Oracle Full Load",
            "path": "../01_bronze_layer/oracle_full_load",
            "timeout": 7200,  # 2 hours
            "retry_count": 2,
            "required_for": ["full_pipeline", "bronze_only", "bronze_silver"],
            "parameters": {
                "environment": environment,
                "source_system": source_system,
                "run_id": run_id
            }
        },
        {
            "name": "Oracle Incremental Load",
            "path": "../01_bronze_layer/oracle_incremental_load",
            "timeout": 3600,  # 1 hour
            "retry_count": 2,
            "required_for": ["full_pipeline", "bronze_silver"],
            "depends_on": ["Oracle Full Load"],
            "parameters": {
                "environment": environment,
                "source_system": source_system,
                "run_id": run_id
            }
        }
    ],
    "silver": [
        {
            "name": "Silver Orchestrator",
            "path": "../02_silver_layer/silver_orchestrator",
            "timeout": 5400,  # 1.5 hours
            "retry_count": 2,
            "required_for": ["full_pipeline", "silver_only", "bronze_silver"],
            "depends_on": ["Oracle Full Load", "Oracle Incremental Load"],  # Silver must wait for ALL bronze data
            "parameters": {
                "environment": environment,
                "source_system": source_system,
                "run_id": run_id
            }
        }
    ],
    "utilities": [
        {
            "name": "Data Quality Check",
            "path": "../06_utilities/data_quality_check",
            "timeout": 1800,  # 30 minutes
            "retry_count": 1,
            "required_for": ["full_pipeline"],
            "depends_on": ["Silver Orchestrator"],
            "parameters": {
                "environment": environment,
                "source_system": source_system,
                "run_id": run_id
            }
        },
        {
            "name": "Final Verification",
            "path": "../06_utilities/final_verification",
            "timeout": 900,  # 15 minutes
            "retry_count": 1,
            "required_for": ["full_pipeline"],
            "depends_on": ["Data Quality Check"],
            "parameters": {
                "environment": environment,
                "source_system": source_system,
                "run_id": run_id
            }
        }
    ]
}

# COMMAND ----------

# MAGIC %md
# MAGIC ## Execution Functions

# COMMAND ----------

def execute_notebook_with_retry(notebook_info, max_retries=2):
    """Execute notebook with retry logic"""
    notebook_name = notebook_info["name"]
    notebook_path = notebook_info["path"]
    timeout = notebook_info.get("timeout", 3600)
    retry_count = notebook_info.get("retry_count", max_retries)
    parameters = notebook_info.get("parameters", {})

    for attempt in range(retry_count + 1):
        try:
            print(f"📓 Executing: {notebook_name} (Attempt {attempt + 1}/{retry_count + 1})")
            start_time = datetime.now()

            result = dbutils.notebook.run(
                notebook_path,
                timeout,
                parameters
            )

            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            print(f"✅ {notebook_name} completed successfully in {duration:.2f} seconds")

            # Parse result if it's JSON
            try:
                result_data = json.loads(result) if result else {}
            except:
                result_data = {"raw_result": result}

            return {
                "notebook": notebook_name,
                "status": "success",
                "duration": duration,
                "attempt": attempt + 1,
                "result": result_data,
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat()
            }

        except Exception as e:
            error_msg = str(e)
            print(f"❌ {notebook_name} failed on attempt {attempt + 1}: {error_msg}")

            if attempt == retry_count:  # Last attempt
                return {
                    "notebook": notebook_name,
                    "status": "failed",
                    "error": error_msg,
                    "attempt": attempt + 1,
                    "total_attempts": retry_count + 1
                }
            else:
                print(f"🔄 Retrying {notebook_name} in 30 seconds...")
                time.sleep(30)

def check_dependencies(notebook_info, completed_notebooks, all_notebook_names):
    """Check if notebook dependencies are satisfied"""
    depends_on = notebook_info.get("depends_on", [])
    notebook_name = notebook_info["name"]

    if not depends_on:
        print(f"🟢 {notebook_name}: No dependencies - ready to execute")
        return True

    print(f"🔍 {notebook_name}: Checking dependencies: {depends_on}")

    for dependency in depends_on:
        # Check if dependency is even part of this run
        if dependency not in all_notebook_names:
            print(f"⚠️ {notebook_name}: Dependency '{dependency}' not in current run - skipping check")
            continue

        if dependency not in completed_notebooks:
            print(f"⏸️ {notebook_name}: Waiting for dependency '{dependency}' to complete")
            return False
        if completed_notebooks[dependency]["status"] != "success":
            print(f"❌ {notebook_name}: Dependency '{dependency}' failed - cannot proceed")
            return False
        else:
            print(f"✅ {notebook_name}: Dependency '{dependency}' satisfied")

    print(f"🟢 {notebook_name}: All dependencies satisfied - ready to execute")
    return True

def filter_notebooks_by_run_mode(notebooks, run_mode):
    """Filter notebooks based on run mode"""
    filtered = {}
    for layer, notebook_list in notebooks.items():
        filtered[layer] = [
            nb for nb in notebook_list
            if run_mode in nb.get("required_for", [])
        ]
    return filtered

# COMMAND ----------

# MAGIC %md
# MAGIC ## Execute Pipeline

# COMMAND ----------

# Filter notebooks based on run mode
filtered_notebooks = filter_notebooks_by_run_mode(pipeline_notebooks, run_mode)

# Flatten notebook list for execution
all_notebooks = []
for layer, notebook_list in filtered_notebooks.items():
    all_notebooks.extend(notebook_list)

print(f"📋 Executing {len(all_notebooks)} notebooks for run mode: {run_mode}")

# Show dependency graph for debugging
print(f"\n🔗 DEPENDENCY GRAPH:")
print("-" * 60)
for notebook in all_notebooks:
    deps = notebook.get("depends_on", [])
    if deps:
        print(f"📓 {notebook['name']} → depends on: {deps}")
    else:
        print(f"📓 {notebook['name']} → no dependencies")
print("-" * 60)

# Track execution
execution_results = {}
completed_notebooks = {}
all_notebook_names = [nb["name"] for nb in all_notebooks]  # For dependency checking
start_time = datetime.now()

if parallel_execution and len(all_notebooks) > 1:
    print(f"🔄 Using parallel execution with max {max_parallel_jobs} jobs")

    # Parallel execution with dependency management
    remaining_notebooks = all_notebooks.copy()

    while remaining_notebooks:
        # Find notebooks ready to execute (dependencies satisfied)
        ready_notebooks = [
            nb for nb in remaining_notebooks
            if check_dependencies(nb, completed_notebooks, all_notebook_names)
        ]

        if not ready_notebooks:
            print("❌ Dependency deadlock detected!")
            break

        # Execute ready notebooks in parallel (but respect max parallel limit)
        batch_size = min(max_parallel_jobs, len(ready_notebooks))
        current_batch = ready_notebooks[:batch_size]

        print(f"🔄 Executing batch of {len(current_batch)} notebooks: {[nb['name'] for nb in current_batch]}")

        with ThreadPoolExecutor(max_workers=batch_size) as executor:
            future_to_notebook = {
                executor.submit(execute_notebook_with_retry, nb): nb
                for nb in current_batch
            }

            # Wait for ALL notebooks in current batch to complete before proceeding
            for future in as_completed(future_to_notebook):
                notebook_info = future_to_notebook[future]
                result = future.result()

                execution_results[notebook_info["name"]] = result
                completed_notebooks[notebook_info["name"]] = result
                remaining_notebooks.remove(notebook_info)

                print(f"✅ Completed: {notebook_info['name']} - Status: {result['status']}")
                print(f"📊 Progress: {len(completed_notebooks)}/{len(all_notebooks)} notebooks completed")

else:
    print("🔄 Using sequential execution")

    # Sequential execution
    for notebook_info in all_notebooks:
        # Check dependencies
        if not check_dependencies(notebook_info, completed_notebooks, all_notebook_names):
            print(f"⏸️ Skipping {notebook_info['name']} - dependencies not satisfied")
            continue

        result = execute_notebook_with_retry(notebook_info)
        execution_results[notebook_info["name"]] = result
        completed_notebooks[notebook_info["name"]] = result

        print(f"📊 Progress: {len(completed_notebooks)}/{len(all_notebooks)} notebooks completed")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Generate Execution Report

# COMMAND ----------

end_time = datetime.now()
total_duration = (end_time - start_time).total_seconds()

# Calculate summary statistics
successful_notebooks = [r for r in execution_results.values() if r["status"] == "success"]
failed_notebooks = [r for r in execution_results.values() if r["status"] == "failed"]

print(f"\n📊 ORCHESTRATION EXECUTION SUMMARY")
print(f"{'='*80}")
print(f"Environment: {environment}")
print(f"Source System: {source_system}")
print(f"Run Mode: {run_mode}")
print(f"Run ID: {run_id}")
print(f"Parallel Execution: {parallel_execution}")
print(f"Total Duration: {total_duration:.2f} seconds")
print(f"Total Notebooks: {len(execution_results)}")
print(f"Successful: {len(successful_notebooks)}")
print(f"Failed: {len(failed_notebooks)}")
print(f"{'='*80}")

# Display detailed results
if execution_results:
    from pyspark.sql import Row

    results_for_df = []
    for notebook_name, result in execution_results.items():
        results_for_df.append(Row(
            notebook=notebook_name,
            status=result["status"],
            duration=result.get("duration", 0),
            attempts=result.get("attempt", 1),
            error=result.get("error", "")[:200]  # Truncate long errors
        ))

    results_df = spark.createDataFrame(results_for_df)
    display(results_df)

# Log orchestration execution
try:
    # Prepare error message safely
    error_msg = ""
    if failed_notebooks:
        failed_names = [str(r.get("notebook", "unknown")) for r in failed_notebooks]
        error_msg = f"Failed notebooks: {', '.join(failed_names)}"

    config_manager.log_pipeline_execution(
        f"orchestrator_{source_system}",
        "success" if len(failed_notebooks) == 0 else "failed",
        run_id=str(run_id),
        source_table="orchestrator",
        start_time=start_time,
        end_time=end_time,
        records_read=int(len(all_notebooks)),
        records_written=int(len(successful_notebooks)),
        error_message=str(error_msg),
        duration_seconds=float(total_duration)
    )
    print("✅ Pipeline execution logged successfully")
except Exception as log_error:
    print(f"⚠️ Could not log pipeline execution: {log_error}")
    # Continue execution even if logging fails

# COMMAND ----------

# MAGIC %md
# MAGIC ## Exit with Results

# COMMAND ----------

# Prepare exit result
exit_result = {
    "status": "success" if len(failed_notebooks) == 0 else "failed",
    "environment": environment,
    "source_system": source_system,
    "run_id": run_id,
    "run_mode": run_mode,
    "parallel_execution": parallel_execution,
    "summary": {
        "total_notebooks": len(execution_results),
        "successful": len(successful_notebooks),
        "failed": len(failed_notebooks),
        "duration_seconds": total_duration
    },
    "execution_results": execution_results
}

if failed_notebooks:
    exit_result["failed_notebooks"] = [r["notebook"] for r in failed_notebooks]
    print(f"\n❌ Pipeline completed with {len(failed_notebooks)} failures")
else:
    print(f"\n✅ Pipeline completed successfully!")

print(f"Exiting with status: {exit_result['status']}")
dbutils.notebook.exit(json.dumps(exit_result))
