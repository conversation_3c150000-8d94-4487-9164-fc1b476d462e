# Databricks notebook source
# MAGIC %md
# MAGIC # Oracle Incremental Load Ingestion
# MAGIC This notebook performs incremental load ingestion from Oracle source tables to Bronze layer

# COMMAND ----------

import json
from datetime import datetime
from pyspark.sql import SparkSession

# COMMAND ----------

# MAGIC %md
# MAGIC ## Setup Widgets for Parameter Passing

# COMMAND ----------

# Create widgets for workflow parameter passing
dbutils.widgets.text("environment", "dev", "Environment (dev/test/prod)")
dbutils.widgets.text("source_system", "MOS", "Source System")
dbutils.widgets.text("run_id", "", "Optional Run ID")
dbutils.widgets.text("catalog_name", "", "Catalog Name Override (optional)")

# Get parameters
environment = dbutils.widgets.get("environment")
source_system = dbutils.widgets.get("source_system")
run_id = dbutils.widgets.get("run_id") or f"bronze_incremental_load_{source_system}_{environment}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
catalog_override = dbutils.widgets.get("catalog_name")

print(f"🚀 Starting Bronze Incremental Load")
print(f"Environment: {environment}")
print(f"Source System: {source_system}")
print(f"Run ID: {run_id}")
print("="*60)

# COMMAND ----------

# MAGIC %md
# MAGIC ## Load Configuration Framework

# COMMAND ----------

# MAGIC %run ../06_utilities/config_manager

# COMMAND ----------

# MAGIC %run ./ingestion_framework

# COMMAND ----------

# MAGIC %md
# MAGIC ## Initialize Configuration

# COMMAND ----------

# Create configuration manager
config_manager = create_config_manager(environment, source_system, catalog_override)

# Validate configuration
if not config_manager.env_config_manager.validate_configuration():
    print("❌ Configuration validation failed!")
    dbutils.notebook.exit(json.dumps({"status": "config_error", "message": "Configuration validation failed"}))

# Get complete runtime configuration
runtime_config = config_manager.get_complete_runtime_config()
config = runtime_config

print(f"✅ Configuration loaded successfully!")
print(f"Bronze Catalog: {config['databricks']['catalogs']['bronze']}")
print(f"Utility Catalog: {config['databricks']['catalogs']['utility']}")
print(f"Oracle Host: {config['oracle_source']['host']}")
print(f"Database: {config['oracle_source']['database']}")

# Initialize the framework
bronze_framework = BronzeIngestionFramework(spark, config)

# COMMAND ----------

# MAGIC %md
# MAGIC ## Oracle Connection Setup

# COMMAND ----------

from pyspark.sql.types import *
from datetime import timedelta
import random

def get_oracle_jdbc_url():
    """Build Oracle JDBC URL"""
    oracle_config = config['oracle_source']
    return f"jdbc:oracle:thin:@//{oracle_config['host']}:{oracle_config['port']}/{oracle_config['database']}"

def read_oracle_incremental(table_name, watermark_column, last_watermark, oracle_schema=None):
    """Read incremental data from Oracle database"""
    oracle_config = config['oracle_source']
    jdbc_url = get_oracle_jdbc_url()

    # Build fully qualified table name with schema prefix
    if oracle_schema:
        full_table_name = f"{oracle_schema}.{table_name}"
    else:
        full_table_name = table_name

    try:
        print(f"Reading incremental data from: {full_table_name}")
        print(f"Watermark column: {watermark_column}")
        print(f"Last watermark: {last_watermark}")

        # Build query with watermark filter
        if last_watermark:
            query = f"(SELECT * FROM {full_table_name} WHERE {watermark_column} > '{last_watermark}') AS t"
        else:
            query = f"(SELECT * FROM {full_table_name}) AS t"

            
        return spark.read \
            .format("jdbc") \
            .option("url", jdbc_url) \
            .option("dbtable", query) \
            .option("user", oracle_config['username']) \
            .option("password", oracle_config['password']) \
            .option("driver", "oracle.jdbc.driver.OracleDriver") \
            .option("fetchsize", "10000") \
            .load()
    except Exception as e:
        print(f"Error reading from Oracle: {str(e)}")
        if "ClassNotFoundException" in str(e):
            print("\n⚠️ Oracle JDBC driver not found!")
            print("Using sample data instead...")
            return create_sample_incremental_data(table_name, watermark_column, last_watermark)
        raise e

def create_sample_incremental_data(table_name, watermark_column, last_watermark):
    """Create sample incremental data for testing"""
    
    # Generate data with timestamps after last_watermark
    base_time = datetime.now() if not last_watermark else datetime.strptime(str(last_watermark), '%Y-%m-%d %H:%M:%S')
    
    if "customer" in table_name.lower():
        schema = StructType([
            StructField("customer_id", IntegerType(), False),
            StructField("customer_name", StringType(), True),
            StructField("email", StringType(), True),
            StructField("last_modified", TimestampType(), True)
        ])
        data = [(i + 100, f"Customer {i + 100}", f"customer{i + 100}@example.com", 
                base_time + timedelta(hours=i)) for i in range(1, 11)]
    else:
        schema = StructType([
            StructField("id", IntegerType(), False),
            StructField("data", StringType(), True),
            StructField(watermark_column, TimestampType(), True)
        ])
        data = [(i + 100, f"Data {i + 100}", base_time + timedelta(hours=i)) 
                for i in range(1, 11)]
    
    return spark.createDataFrame(data, schema)

# COMMAND ----------

# MAGIC %md
# MAGIC ## Process Incremental Load Tables

# COMMAND ----------

# Get all tables configured for incremental load
utility_catalog = config['databricks']['catalogs']['utility']
metadata_schema = config['databricks']['schemas']['metadata']

try:
    incremental_tables = spark.sql(f"""
        SELECT * FROM `{utility_catalog}`.`{metadata_schema}`.pipeline_config
        WHERE ingestion_type = 'incremental'
        AND is_active = true
        AND watermark_column IS NOT NULL
    """).collect()
    
    print(f"Found {len(incremental_tables)} tables for incremental load processing")
except:
    print("\n⚠️ Pipeline config table not found. Using default configuration.")
    incremental_tables = []

# COMMAND ----------

# Helper function to convert Row to dict
def row_to_dict(row):
    """Convert Row to dictionary"""
    if hasattr(row, 'asDict'):
        return row.asDict()
    else:
        return {attr: getattr(row, attr) for attr in dir(row) 
                if not attr.startswith('_')}

# COMMAND ----------

# Process each incremental load table
results = []

for table_config in incremental_tables:
    print(f"\n{'='*60}")
    print(f"Processing: {table_config.source_table}")
    print(f"Watermark Column: {table_config.watermark_column}")
    print(f"{'='*60}")
    
    try:
        # Get last watermark
        last_watermark = bronze_framework.get_last_watermark(
            table_config.pipeline_id,
            table_config.source_table
        )
        
        # Read incremental data from Oracle with schema prefix
        oracle_schema = getattr(table_config, 'oracle_schema', None)
        source_df = read_oracle_incremental(
            table_config.source_table,
            table_config.watermark_column,
            last_watermark,
            oracle_schema
        )
        
        # Show source info
        record_count = source_df.count()
        print(f"Incremental records found: {record_count}")
        
        if record_count > 0:
            # Convert config to dict
            config_dict = row_to_dict(table_config)
            
            # Execute incremental load
            result = bronze_framework.execute_incremental_load(
                source_df, 
                config_dict,
                table_config.watermark_column
            )
            
            results.append({
                "table": table_config.source_table,
                "status": "success",
                "records": result.get('records_written', 0),
                "watermark": str(result.get('watermark', 'N/A')),
                "run_id": result.get('run_id', 'N/A')
            })
            
            print(f"\n✅ Successfully loaded {result.get('records_written', 0)} incremental records")
            print(f"New watermark: {result.get('watermark', 'N/A')}")
        else:
            results.append({
                "table": table_config.source_table,
                "status": "success",
                "records": 0,
                "watermark": str(last_watermark),
                "message": "No new records found"
            })
            print("\n✅ No new records to process")
            
    except Exception as e:
        results.append({
            "table": table_config.source_table,
            "status": "failed",
            "error": str(e)
        })
        print(f"\n❌ Error: {str(e)}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Summary Report

# COMMAND ----------

# Display results
from pyspark.sql import Row
if results:
    results_df = spark.createDataFrame([Row(**r) for r in results])
    display(results_df)
else:
    print("No incremental tables were processed.")

# COMMAND ----------

# Show recent pipeline runs
if len(results) > 0:
    recent_runs = spark.sql(f"""
        SELECT pipeline_id, source_table, run_date, start_time, status,
               records_read, records_written, watermark_value
        FROM `{utility_catalog}`.`{metadata_schema}`.pipeline_runs
        WHERE run_date = current_date()
        AND pipeline_id IN ({','.join([f"'{r['table']}'" for r in results if 'table' in r])})
        ORDER BY start_time DESC
        LIMIT 20
    """)
    
    print("\nRecent Pipeline Runs Today:")
    display(recent_runs)

# COMMAND ----------

if results:
    success_count = sum(1 for r in results if r['status'] == 'success')
    total_records = sum(r.get('records', 0) for r in results)
    print(f"\n✅ Incremental load completed! Processed {len(results)} tables ({success_count} successful).")
    print(f"Total new records loaded: {total_records}")
    dbutils.notebook.exit(json.dumps(results))
else:
    print("\n⚠️ No tables found for incremental load processing.")
    dbutils.notebook.exit(json.dumps({"status": "no_tables_found"}))
