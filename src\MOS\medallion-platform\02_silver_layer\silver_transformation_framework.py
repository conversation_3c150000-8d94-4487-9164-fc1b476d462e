# Databricks notebook source
# MAGIC %md
# MAGIC # Silver Layer Transformation Framework
# MAGIC Generic framework for data transformations in the silver layer

# COMMAND ----------

import json
from datetime import datetime
from pyspark.sql.functions import *
from pyspark.sql.types import *

# COMMAND ----------

# Default Configuration (will be overridden by dynamic config when available)
bronze_catalog = "mbcl_dev_bronze"
silver_catalog = "mbcl_dev_silver"
utility_catalog = "mbcl_dev_utility"
bronze_schema = "mos"
silver_schema = "mos"
metadata_schema = "metadata"

# Try to get dynamic configuration if available
try:
    # This will work when called from notebooks that have loaded config_manager
    if 'runtime_config' in globals():
        bronze_catalog = runtime_config['databricks']['catalogs']['bronze']
        silver_catalog = runtime_config['databricks']['catalogs']['silver']
        utility_catalog = runtime_config['databricks']['catalogs']['utility']
        bronze_schema = runtime_config['databricks']['schemas']['bronze']
        silver_schema = runtime_config['databricks']['schemas']['silver']
        metadata_schema = runtime_config['databricks']['schemas']['metadata']
except:
    # Use defaults if dynamic config not available
    pass

# COMMAND ----------

class SilverTransformationFramework:
    def __init__(self, spark, config=None):
        self.spark = spark
        self.bronze_catalog = bronze_catalog
        self.silver_catalog = silver_catalog
        self.utility_catalog = utility_catalog
        self.bronze_schema = bronze_schema
        self.silver_schema = silver_schema
        self.metadata_schema = metadata_schema
        
    def apply_data_quality_rules(self, df, table_name):
        """Apply data quality rules to dataframe"""
        try:
            # Get active rules for this table
            rules_df = self.spark.sql(f"""
                SELECT * FROM `{self.utility_catalog}`.`{self.metadata_schema}`.data_quality_rules
                WHERE is_active = true
                AND (column_name IN ({','.join([f"'{c}'" for c in df.columns])})
                     OR column_name = '*')
            """)
            
            rules = rules_df.collect()
            
            # Apply each rule
            for rule in rules:
                if rule.rule_type == 'not_null':
                    df = df.filter(col(rule.column_name).isNotNull())
                elif rule.rule_type == 'range':
                    df = df.filter(expr(rule.rule_definition))
                elif rule.rule_type == 'pattern':
                    df = df.filter(expr(rule.rule_definition))
                    
            return df
        except:
            # If no rules table, return original df
            return df
    
    def standardize_column_names(self, df):
        """Standardize column names to snake_case"""
        for column in df.columns:
            new_name = column.lower().replace(" ", "_").replace("-", "_")
            if column != new_name:
                df = df.withColumnRenamed(column, new_name)
        return df
    
    def add_silver_audit_columns(self, df):
        """Add silver layer audit columns"""
        return df.withColumn("_silver_processed_timestamp", current_timestamp()) \
                 .withColumn("_silver_version", lit("1.0"))
    
    def execute_transformation(self, config):
        """Execute transformation based on configuration"""
        try:
            # Check if custom Spark SQL is provided
            if 'transformations' in config and 'spark_sql' in config['transformations']:
                # Execute custom Spark SQL
                transformed_df = self.spark.sql(config['transformations']['spark_sql'])
                transformed_df = self.add_silver_audit_columns(transformed_df)
            else:
                # Standard transformation flow - read from bronze catalog
                source_table_path = f"`{self.bronze_catalog}`.`{self.bronze_schema}`.`{config['source_table']}`"
                source_df = self.spark.table(source_table_path)

                # Apply transformations
                transformed_df = self.standardize_column_names(source_df)
                transformed_df = self.apply_data_quality_rules(transformed_df, config['source_table'])
                transformed_df = self.add_silver_audit_columns(transformed_df)
                
                # Apply custom transformations if specified
                if 'transformations' in config:
                    # Add columns
                    if 'add_columns' in config['transformations']:
                        for col_name, expression in config['transformations']['add_columns'].items():
                            transformed_df = transformed_df.withColumn(col_name, expr(expression))
                    
                    # Rename columns
                    if 'rename_columns' in config['transformations']:
                        for old_name, new_name in config['transformations']['rename_columns'].items():
                            if old_name in transformed_df.columns:
                                transformed_df = transformed_df.withColumnRenamed(old_name, new_name)
                    
                    # Filter rows
                    if 'filter' in config['transformations']:
                        transformed_df = transformed_df.filter(config['transformations']['filter'])
            
            # Write to silver catalog
            target_table_path = f"`{self.silver_catalog}`.`{self.silver_schema}`.`{config['target_table']}`"

            # Ensure silver schema exists
            self.spark.sql(f"CREATE SCHEMA IF NOT EXISTS `{self.silver_catalog}`.`{self.silver_schema}`")

            transformed_df.write \
                .mode("overwrite") \
                .format("delta") \
                .saveAsTable(target_table_path)
            
            return {
                "status": "success",
                "records_processed": transformed_df.count(),
                "target_table": config['target_table']
            }
            
        except Exception as e:
            return {
                "status": "failed",
                "error": str(e)
            }

# COMMAND ----------

# MAGIC %md
# MAGIC ## Standardization Functions

# COMMAND ----------

class StandardizationFramework:
    def __init__(self, spark, config=None):
        self.spark = spark
        
    def standardize_dataframe(self, df, rules):
        """Apply standardization rules to dataframe"""
        for column, rule in rules.items():
            if column in df.columns:
                if rule == "upper":
                    df = df.withColumn(column, upper(col(column)))
                elif rule == "lower":
                    df = df.withColumn(column, lower(col(column)))
                elif rule == "initcap":
                    df = df.withColumn(column, initcap(col(column)))
                elif rule == "trim":
                    df = df.withColumn(column, trim(col(column)))
        return df
    
    def standardize_phone_numbers(self, df, phone_column):
        """Standardize phone numbers to digits only"""
        return df.withColumn(
            phone_column,
            regexp_replace(col(phone_column), "[^0-9]", "")
        )
    
    def standardize_dates(self, df, date_columns, target_format="yyyy-MM-dd"):
        """Standardize date formats"""
        for date_col in date_columns:
            if date_col in df.columns:
                df = df.withColumn(
                    date_col,
                    to_date(col(date_col), target_format)
                )
        return df

# COMMAND ----------

# MAGIC %md
# MAGIC ## Data Quality Framework

# COMMAND ----------

class DataQualityFramework:
    def __init__(self, spark, catalog=None, environment=None):
        self.spark = spark
        self.utility_catalog = catalog or utility_catalog
        self.metadata_schema = "metadata"
        self.environment = environment
        
    def run_quality_checks(self, df, source_table):
        """Run data quality checks and return results"""
        results = []
        
        try:
            # Get rules from metadata
            rules_df = self.spark.sql(f"""
                SELECT * FROM `{self.utility_catalog}`.`{self.metadata_schema}`.data_quality_rules
                WHERE is_active = true
            """)
            
            rules = rules_df.collect()
            
            for rule in rules:
                # Check if column exists in dataframe
                if rule.column_name in df.columns or rule.column_name == '*':
                    # Count violations
                    if rule.rule_type == 'not_null':
                        violations = df.filter(col(rule.column_name).isNull()).count()
                    else:
                        violations = df.filter(~expr(rule.rule_expression)).count()
                    
                    total_records = df.count()
                    passed = violations == 0
                    
                    results.append({
                        'rule_name': rule.rule_name,
                        'rule_type': rule.rule_type,
                        'column': rule.column_name,
                        'passed': passed,
                        'violations': violations,
                        'total_records': total_records,
                        'violation_rate': (violations / total_records * 100) if total_records > 0 else 0
                    })
        except:
            # Return empty results if no rules table
            pass
            
        return results

    def run_comprehensive_quality_checks(self, df, table_name, source_system):
        """Run comprehensive data quality checks and return detailed results"""

        # Run basic quality checks
        basic_results = self.run_quality_checks(df, table_name)

        # Additional comprehensive checks
        total_records = df.count()
        null_counts = {}
        duplicate_count = 0

        # Check for nulls in each column
        for col_name in df.columns:
            if not col_name.startswith('_'):  # Skip audit columns
                null_count = df.filter(col(col_name).isNull()).count()
                null_counts[col_name] = null_count

        # Check for duplicates (if table has an ID column)
        id_columns = [c for c in df.columns if 'id' in c.lower() and not c.startswith('_')]
        if id_columns:
            primary_id = id_columns[0]  # Use first ID column found
            duplicate_count = df.count() - df.select(primary_id).distinct().count()

        # Calculate overall quality score
        total_issues = sum(null_counts.values()) + duplicate_count + len([r for r in basic_results if not r['passed']])
        quality_score = max(0, 100 - (total_issues / total_records * 100)) if total_records > 0 else 100

        # Determine quality level
        if quality_score >= 95:
            quality_level = "Excellent"
        elif quality_score >= 85:
            quality_level = "Good"
        elif quality_score >= 70:
            quality_level = "Fair"
        else:
            quality_level = "Poor"

        # Generate issues list
        issues = []
        for col_name, null_count in null_counts.items():
            if null_count > 0:
                issues.append(f"Column '{col_name}' has {null_count} null values ({null_count/total_records*100:.1f}%)")

        if duplicate_count > 0:
            issues.append(f"Found {duplicate_count} duplicate records")

        for result in basic_results:
            if not result['passed']:
                issues.append(f"Rule '{result['rule_name']}' failed: {result['violations']} violations")

        # Generate recommendations
        recommendations = []
        if any(null_count > total_records * 0.1 for null_count in null_counts.values()):
            recommendations.append("Consider implementing null value handling strategies")

        if duplicate_count > 0:
            recommendations.append("Implement deduplication logic in the transformation")

        if quality_score < 85:
            recommendations.append("Review and enhance data quality rules")

        return {
            "table_name": table_name,
            "source_system": source_system,
            "total_records": total_records,
            "overall_score": quality_score,
            "quality_level": quality_level,
            "null_counts": null_counts,
            "duplicate_count": duplicate_count,
            "basic_checks": basic_results,
            "issues": issues,
            "recommendations": recommendations
        }

# COMMAND ----------

# Create instances for use in other notebooks
transform_framework = SilverTransformationFramework(spark)
std_framework = StandardizationFramework(spark)
dq_framework = DataQualityFramework(spark)

print("Silver Transformation Framework loaded successfully!")

# COMMAND ----------

# Return success
#dbutils.notebook.exit("SUCCESS")
