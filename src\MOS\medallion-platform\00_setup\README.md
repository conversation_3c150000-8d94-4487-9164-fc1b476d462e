# Medallion Platform Setup Guide

## Overview

This guide walks you through setting up the medallion platform with the new layer-specific catalog architecture.

## Architecture

### Layer-Specific Catalogs

The platform uses separate catalogs for each medallion layer and environment:

```
Environment: dev
├── mbcl_dev_bronze.{source_system}    # Raw data ingestion
├── mbcl_dev_silver.{source_system}    # Cleaned & transformed data
├── mbcl_dev_gold.{source_system}      # Business-ready analytics
└── mbcl_dev_utility.metadata          # Metadata & configuration

Environment: test
├── mbcl_test_bronze.{source_system}
├── mbcl_test_silver.{source_system}
├── mbcl_test_gold.{source_system}
└── mbcl_test_utility.metadata

Environment: prod
├── mbcl_prod_bronze.{source_system}
├── mbcl_prod_silver.{source_system}
├── mbcl_prod_gold.{source_system}
└── mbcl_prod_utility.metadata
```

### Source System Schemas

Each catalog contains schemas based on source systems:
- `mos` - MOS system data
- `xva` - XVA system data (future)
- `metadata` - Platform metadata (utility catalog only)

## Setup Process

### Step 1: Initial Setup

Run the initial setup notebook to create catalogs and schemas:

```python
# Notebook: 00_setup/initial_setup
# Parameters:
# - environment: dev/test/prod
# - source_system: MOS/XVA
```

This creates:
- All layer-specific catalogs for the environment
- Source system schemas in each catalog
- Metadata schema in utility catalog

### Step 2: Create Metadata Tables

Run the metadata creation notebook:

```python
# Notebook: 05_metadata/create_metadata_tables
# Parameters:
# - environment: dev/test/prod
# - source_system: MOS/XVA
```

This creates:
- `pipeline_config` - Pipeline configuration
- `pipeline_runs` - Execution audit log
- `data_quality_rules` - Quality validation rules
- Sample configuration data

### Step 3: Setup Silver Metadata (Optional)

For advanced silver transformations:

```python
# Notebook: 02_silver_layer/setup_silver_metadata
# Parameters:
# - environment: dev/test/prod
# - source_system: MOS/XVA
```

### Step 4: Verify Setup

Run final verification:

```python
# Notebook: 06_utilities/final_verification
# Parameters:
# - environment: dev/test/prod
# - source_system: MOS/XVA
```

## Running the Platform

### Full Pipeline

```python
# Notebook: 04_orchestration/main_pipeline_orchestrator
# Parameters:
# - environment: dev/test/prod
# - source_system: MOS/XVA
# - run_mode: full_pipeline
# - parallel_execution: true
```

### Individual Layers

**Bronze Only:**
```python
# run_mode: bronze_only
```

**Silver Only:**
```python
# run_mode: silver_only
```

**Bronze + Silver:**
```python
# run_mode: bronze_silver
```

## Configuration

The platform automatically determines catalog and schema names based on:
- Environment (dev/test/prod)
- Source system (MOS/XVA)

No manual catalog configuration needed!

## Benefits

1. **Environment Isolation**: Complete separation between dev/test/prod
2. **Source System Separation**: Each system has its own schemas
3. **Scalability**: Easy to add new environments and source systems
4. **Governance**: Clear data lineage and access control
5. **Maintenance**: Simplified backup and recovery per environment

## Troubleshooting

### Catalog Access Issues
- Ensure you have CREATE CATALOG permissions
- Check Unity Catalog is enabled
- Verify storage credentials are configured

### Schema Creation Failures
- Check catalog exists first
- Verify naming conventions (lowercase)
- Ensure proper permissions

### Pipeline Failures
- Check source system connectivity
- Verify metadata tables exist
- Review configuration parameters

## Next Steps

After setup:
1. Configure source system connections
2. Set up data quality rules
3. Schedule pipeline execution
4. Monitor through audit logs
5. Set up alerting and notifications
