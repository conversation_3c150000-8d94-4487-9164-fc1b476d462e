# Databricks notebook source
# MAGIC %md
# MAGIC # Silver Layer Orchestrator
# MA<PERSON>C Orchestrates all silver layer transformations

# COMMAND ----------

# MAGIC %md
# MAGIC ## Setup Widgets for Parameter Passing

# COMMAND ----------

# Create widgets for workflow parameter passing
dbutils.widgets.text("environment", "dev", "Environment (dev/test/prod)")
dbutils.widgets.text("source_system", "MOS", "Source System")
dbutils.widgets.text("run_id", "", "Optional Run ID")
dbutils.widgets.text("catalog_name", "", "Catalog Name Override (optional)")

# Get parameters
environment = dbutils.widgets.get("environment")
source_system = dbutils.widgets.get("source_system")
run_id = dbutils.widgets.get("run_id") or f"silver_orchestrator_{source_system}_{environment}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
catalog_override = dbutils.widgets.get("catalog_name")

print(f"🚀 Starting Silver Layer Orchestration")
print(f"Environment: {environment}")
print(f"Source System: {source_system}")
print(f"Run ID: {run_id}")
print("="*60)

# COMMAND ----------

# MAGIC %md
# MAGIC ## Load Configuration Framework

# COMMAND ----------

# MAGIC %run ../06_utilities/config_manager

# COMMAND ----------

from datetime import datetime
import json

# COMMAND ----------

# MAGIC %md
# MAGIC ## Initialize Configuration

# COMMAND ----------

# Create configuration manager
config_manager = create_config_manager(environment, source_system, catalog_override)

# Validate configuration
if not config_manager.env_config_manager.validate_configuration():
    print("❌ Configuration validation failed!")
    dbutils.notebook.exit(json.dumps({"status": "config_error", "message": "Configuration validation failed"}))

# Get complete runtime configuration
runtime_config = config_manager.get_complete_runtime_config()

# Extract configuration values
bronze_catalog = runtime_config['databricks']['catalogs']['bronze']
silver_catalog = runtime_config['databricks']['catalogs']['silver']
bronze_schema = runtime_config['databricks']['schemas']['bronze']
silver_schema = runtime_config['databricks']['schemas']['silver']

print(f"✅ Configuration loaded successfully!")
print(f"Bronze Catalog: {bronze_catalog}")
print(f"Silver Catalog: {silver_catalog}")
print(f"Bronze Schema: {bronze_schema}")
print(f"Silver Schema: {silver_schema}")

# COMMAND ----------

# MAGIC %run ./silver_transformation_framework

# COMMAND ----------

silver_transformations = [
    {
        "name": "Process - vw_account_receivable_by_client",
        "source_table": f"`{bronze_catalog}`.`{bronze_schema}`.vw_account_receivable_by_client",
        "target_table": f"`{silver_catalog}`.`{silver_schema}`.vw_account_receivable_by_client",
        "transformations": {
            "spark_sql": f"""
-- API Logic: x.RunDate != null && x.RunDate.Value.Date == businessDate.Date
-- Load all dates, users filter by runDate for specific businessDate
SELECT
    RUN_DATE            AS runDate,
    ENTITY_ID           AS entityId,
    COUNTERPARTY_NAME   AS counterpartyName,
    CE_LIMIT            AS ceLimit,
    ACCOUNT_RECEIVABLE  AS accountReceivable,
    UTIL_PCT            AS utilPct,
    PEAK_LIMIT          AS peakLimit,
    PEAK_RECEIVABLE     AS peakReceivable,
    PEAK_UTIL_PCT       AS peakUtilPct,
    SEGREGATION         AS segregation
FROM `{bronze_catalog}`.`{bronze_schema}`.vw_account_receivable_by_client
WHERE RUN_DATE IS NOT NULL
ORDER BY RUN_DATE, ENTITY_ID
            """
        }
    },
    {
        "name": "Process - vw_new_der",
        "source_table": f"`{bronze_catalog}`.`{bronze_schema}`.vw_new_der",
        "target_table": f"`{silver_catalog}`.`{silver_schema}`.vw_new_der",
        "transformations": {
            "spark_sql": f"""
SELECT
    REPORT_DATE                 AS reportDate,
    CODE                        AS code,
    COUNTERPARTY                AS counterparty,
    INDUSTRY                    AS industry,
    RATING                      AS rating,
    LIMIT_TYPE                  AS limitType,
    CE_LIMIT                    AS ceLimit,
    PFE_LIMIT                   AS pfeLimit,
    C10_DAY_PFE_LIMIT           AS c10DayPfeLimit,
    TENOR                       AS tenor,
    LIMIT_EXPIRY                AS limitExpiry,
    AUTHORIZATION_LOCATION      AS authorizationLocation,
    RENEWAL_STATUS              AS renewalStatus,
    MTM                         AS mtm,
    PEAK_10_DAY_VAR             AS peak10DayVar,
    COLLATERAL                  AS collateral,
    NET_EXPOSURE                AS netExposure,
    MTM_ALERT                   AS mtmAlert,
    MARGINING                   AS margining,
    PEAK_PFE                    AS peakPfe,
    CURRENT_10DAY_PFE_EXPOSURE  AS current10dayPfeExposure,
    MAXIMUM_TENOR               AS maximumTenor,
    KEY_RISK_INDICATOR          AS keyRiskIndicator,
    PFE_ALERT                   AS pfeAlert,
    DOCUMENTATION               AS documentation,
    MARGIN_AGREEMENT            AS marginAgreement,
    CP_THRESHOLD                AS cpThreshold,
    MCRM_THRESHOLD              AS mcrmThreshold,
    CP_MTA                      AS cpMta,
    MCRM_MTA                    AS mcrmMta,
    BASE_CURRENCY               AS baseCurrency,
    COUNTRY_INCORP              AS countryIncorp,
    USER_NAME                   AS userName,
    GUARANTOR_NAME              AS guarantorName,
    LIMIT                       AS limit,
    LIMIT_CURRENCY              AS limitCurrency,
    CREDIT_STATUS               AS creditStatus,
    LEGAL_STATUS                AS legalStatus,
    COMPLIANCE_CT_STATUS        AS complianceCtStatus,
    COMPLIANCE_AB_STATUS        AS complianceAbStatus,
    LAST_TRADE_DATE             AS lastTradeDate,
    ALLOCATED_CE_LIMIT          AS allocatedCeLimit,
    ALLOCATED_PFE_LIMIT         AS allocatedPfeLimit,
    ALLOCATED_10_DAY_PFE_LIMIT  AS allocated10DayPfeLimit,
    ALLOCATED_TENOR             AS allocatedTenor,
    MAX_DEFER_OPTION_TENOR_LIMIT AS maxDeferOptionTenorLimit,
    MAX_DEFER_OPTION_TENOR      AS maxDeferOptionTenor,
    ZERO_10_DAY_PFE             AS zero10DayPfe,
    TOTAL_PREMIUM_OVER_24_MONTH AS totalPremiumOver24Month,
    NOTIONALOVER60MONTHS        AS notionalover60months,
    SEGREGATION                 AS segregation,
    DATASHARED                  AS datashared,
    ACCOUNT_RECEIVABLE          AS accountReceivable,
    GROSS_MTM                   AS grossMtm,
    ENTITY_ID                   AS entityId,
    DATA_SOURCE                 AS dataSource,
    LEDGER_BALANCE              AS ledgerBalance,
    IM                          AS im,
    IM_LIMIT                    AS imLimit,
    VM_LIMIT                    AS vmLimit,
    FUT_B                       AS futB,
    FUT_N                       AS futN,
    FUT_S                       AS futS,
    MOP                         AS mop,
    DELTA                       AS delta,
    LESS_COLLATERAL             AS lessCollateral,
    GROSS_MARGIN_LIMIT          AS grossMarginLimit,
    MAX_OP_PERCUTIL             AS maxOpPercutil,
    UTIL_VM_LIMIT               AS utilVmLimit,
    UTIL_MOP                    AS utilMop,
    REPORT_SET                  AS reportSet,
    NUM_LIMITS                  AS numLimits,
    FIRST_APPROVED_FOR_TRADING  AS firstApprovedForTrading
FROM `{bronze_catalog}`.`{bronze_schema}`.vw_new_der
WHERE REPORT_DATE IS NOT NULL
ORDER BY REPORT_DATE, ENTITY_ID
            """
        }
    },
    {
        "name": "Process - vw_new_der_tma",
        "source_table": f"`{bronze_catalog}`.`{bronze_schema}`.vw_new_der_tma",
        "target_table": f"`{silver_catalog}`.`{silver_schema}`.vw_new_der_tma",
        "transformations": {
            "spark_sql": f"""
-- API Logic: x.ReportDate.Date == businessDate.Date && (x.FirstApprovedForTrading.HasValue || includeUnapproved)
-- Silver includes both approved and unapproved, users filter as needed
SELECT
    REPORT_DATE                 AS reportDate,
    DATA_SOURCE                 AS dataSource,
    SEGREGATION                 AS segregation,
    LIMIT_TYPE                  AS limitType,
    REPORT_SET                  AS reportSet,
    COUNTERPARTY                AS counterparty,
    COUNTRY_INCORP              AS countryIncorp,
    RATING                      AS rating,
    ALLOCATED_TENOR             AS allocatedTenor,
    MAX_OP_PERCUTIL             AS maxOpPercutil,
    LEDGER_BALANCE              AS ledgerBalance,
    IM                          AS im,
    VM                          AS vm,
    LESS_COLLATERAL             AS lessCollateral,
    COLLATERAL                  AS collateral,
    IM_LIMIT                    AS imLimit,
    VM_LIMIT                    AS vmLimit,
    UTIL_VM_LIMIT               AS utilVmLimit,
    FUT_B                       AS futB,
    FUT_S                       AS futS,
    FUT_N                       AS futN,
    MOP                         AS mop,
    UTIL_MOP                    AS utilMop,
    DELTA                       AS delta,
    MAXIMUM_TENOR               AS maximumTenor,
    TENOR_CHECK                 AS tenorCheck,
    ENTITY_ID                   AS entityId,
    FIRST_APPROVED_FOR_TRADING  AS firstApprovedForTrading,
    -- Silver processing columns
    CASE
        WHEN FIRST_APPROVED_FOR_TRADING IS NOT NULL THEN true
        ELSE false
    END AS isApproved
FROM `{bronze_catalog}`.`{bronze_schema}`.vw_new_der_tma
WHERE REPORT_DATE IS NOT NULL
ORDER BY REPORT_DATE, ENTITY_ID
            """
        }
    },
    {
        "name": "Process - vw_suspended_counterparties",
        "source_table": f"`{bronze_catalog}`.`{bronze_schema}`.vw_suspended_counterparties",
        "target_table": f"`{silver_catalog}`.`{silver_schema}`.vw_suspended_counterparties",
        "transformations": {
            "spark_sql": f"""
-- API Logic: businessDate >= SuspendedDate AND businessDate <= SuspendedUntilDate
-- API Logic: Override SnapshotDate with businessDate
-- Silver implements exact API logic for all possible business dates
WITH all_business_dates AS (
    -- Get all unique dates where suspensions could be active (excluding future dates)
    SELECT DISTINCT business_date
    FROM (
        SELECT DATE(SUSPENDED_DATE) AS business_date
        FROM `{bronze_catalog}`.`{bronze_schema}`.vw_suspended_counterparties
        WHERE SUSPENDED_DATE IS NOT NULL
          AND DATE(SUSPENDED_DATE) <= CURRENT_DATE()
        UNION ALL
        SELECT DATE(SUSPENDED_UNTIL_DATE) AS business_date
        FROM `{bronze_catalog}`.`{bronze_schema}`.vw_suspended_counterparties
        WHERE SUSPENDED_UNTIL_DATE IS NOT NULL
          AND DATE(SUSPENDED_UNTIL_DATE) <= CURRENT_DATE()
        UNION ALL
        -- Add intermediate dates for long suspensions (only up to today)
        SELECT date_add(DATE(SUSPENDED_DATE), day_offset) AS business_date
        FROM `{bronze_catalog}`.`{bronze_schema}`.vw_suspended_counterparties
        LATERAL VIEW posexplode(
            split(repeat(',', LEAST(datediff(LEAST(SUSPENDED_UNTIL_DATE, CURRENT_DATE()), SUSPENDED_DATE), 365)), ',')
        ) t AS day_offset, val
        WHERE SUSPENDED_DATE IS NOT NULL
          AND SUSPENDED_UNTIL_DATE IS NOT NULL
          AND datediff(SUSPENDED_UNTIL_DATE, SUSPENDED_DATE) > 0
          AND date_add(DATE(SUSPENDED_DATE), day_offset) <= CURRENT_DATE()
    )
    WHERE business_date <= CURRENT_DATE()
),

suspended_by_business_date AS (
    SELECT
        -- API Output Columns (exact match)
        bd.business_date AS snapshotDate,  -- API Override: snapshotDate = businessDate
        sp.ENTITY_ID                AS entityId,
        sp.COUNTERPARTY_NAME        AS counterpartyName,
        sp.SEGREGATION              AS segregation,
        sp.CREDIT_APPLICATION_ID    AS creditApplicationId,
        sp.LIMIT_TYPE               AS limitType,
        sp.APPROVED_FROM            AS approvedFrom,
        sp.APPROVED_TO              AS approvedTo,
        sp.SUSPENDED_DATE           AS suspendedDate,
        sp.SUSPENDED_UNTIL_DATE     AS suspendedUntilDate,
        sp.SUSPENDED_BY             AS suspendedBy,
        sp.SUSPENSION_COMMENTS      AS suspensionComments,
        sp.REINSTATED_DATE          AS reinstatedDate,
        sp.REINSTATION_COMMENTS     AS reinstationComments,
        sp.REINSTATED_BY            AS reinstatedBy,

        -- Silver Processing Columns (standard audit fields)
        CURRENT_TIMESTAMP()         AS _silver_processed_timestamp,
        sp.SNAPSHOT_DATE            AS _original_snapshot_date,
        'suspended_counterparties'  AS _source_table,
        CASE
            WHEN sp.REINSTATED_DATE IS NOT NULL THEN 'reinstated'
            WHEN bd.business_date <= DATE(sp.SUSPENDED_UNTIL_DATE) THEN 'active'
            ELSE 'expired'
        END AS _suspension_status
    FROM all_business_dates bd
    INNER JOIN `{bronze_catalog}`.`{bronze_schema}`.vw_suspended_counterparties sp
        ON bd.business_date >= DATE(sp.SUSPENDED_DATE)
        AND bd.business_date <= DATE(sp.SUSPENDED_UNTIL_DATE)
)

SELECT * FROM suspended_by_business_date
ORDER BY snapshotDate, entityId
            """
        }
    },
    {
        "name": "Process - vw_new_der_noncsa",
        "source_table": f"`{bronze_catalog}`.`{bronze_schema}`.vw_new_der_noncsa",
        "target_table": f"`{silver_catalog}`.`{silver_schema}`.vw_new_der_noncsa",
        "transformations": {
            "spark_sql": f"""
-- API Logic: x.ReportDate.Date == businessDate.Date && (x.FirstApprovedForTrading.HasValue || includeUnapproved)
-- Silver includes both approved and unapproved, users filter as needed
SELECT
    REPORT_DATE                   AS reportDate,
    DATA_SOURCE                   AS dataSource,
    COUNTERPARTY                  AS counterparty,
    SEGREGATION                   AS segregation,
    LIMIT_TYPE                    AS limitType,
    TRADING_STATUS                AS tradingStatus,
    ALLOCATED_CE_LIMIT            AS allocatedCeLimit,
    MTM                           AS mtm,
    GROSS_MTM                     AS grossMtm,
    COLLATERAL                    AS collateral,
    CE_UTILISATION                AS ceUtilisation,
    GROSS_CE_UTILISATION          AS grossCeUtilisation,
    ALLOCATED_PFE_LIMIT           AS allocatedPfeLimit,
    PEAK_PFE                      AS peakPfe,
    PFE_UTILISATION               AS pfeUtilisation,
    TENOR_CHECK                   AS tenorCheck,
    OPTIONS_TENOR_CHECK           AS optionsTenorCheck,
    LONGEST_TRADE                 AS longestTrade,
    ALLOCATED_TENOR               AS allocatedTenor,
    TOTAL_PREMIUM_OVER_24_MONTH   AS totalPremiumOver24Month,
    NON_CSA_PFE_FLAG              AS nonCsaPfeFlag,
    NON_NETTING                   AS nonNetting,
    ENTITY_ID                     AS entityId,
    FIRST_APPROVED_FOR_TRADING    AS firstApprovedForTrading,
    -- Silver processing columns
    CASE
        WHEN FIRST_APPROVED_FOR_TRADING IS NOT NULL THEN true
        ELSE false
    END AS isApproved
FROM `{bronze_catalog}`.`{bronze_schema}`.vw_new_der_noncsa
WHERE REPORT_DATE IS NOT NULL
ORDER BY REPORT_DATE, ENTITY_ID
            """
        }
    },
    {
        "name": "Process - vw_new_der_csa",
        "source_table": f"`{bronze_catalog}`.`{bronze_schema}`.vw_new_der_csa",
        "target_table": f"`{silver_catalog}`.`{silver_schema}`.vw_new_der_csa",
        "transformations": {
            "spark_sql": f"""
-- API Logic: x.ReportDate.Date == businessDate.Date && (x.FirstApprovedForTrading.HasValue || includeUnapproved)
-- Silver includes both approved and unapproved, users filter as needed
SELECT
    REPORT_DATE                  AS reportDate,
    DATA_SOURCE                  AS dataSource,
    COUNTERPARTY                 AS counterparty,
    SEGREGATION                  AS segregation,
    LIMIT_TYPE                   AS limitType,
    TRADING_STATUS               AS tradingStatus,
    ALLOCATED_CE_LIMIT           AS allocatedCeLimit,
    MTM                          AS mtm,
    GROSS_MTM                    AS grossMtm,
    GROSS_CE_UTILISATION         AS grossCeUtilisation,
    COLLATERAL                   AS collateral,
    ALLOCATED_10_DAY_PFE_LIMIT   AS allocated10DayPfeLimit,
    NET_EXPOSURE                 AS netExposure,
    TEN_DAY_VAR                  AS tenDayVar,
    TEN_DAY_PFE_EXPOSURE         AS tenDayPfeExposure,
    TEN_DAY_PFE_UTILISATION      AS tenDayPfeUtilisation,
    TEN_DAY_VAR_ALERT            AS tenDayVarAlert,
    TENOR_CHECK                  AS tenorCheck,
    OPTIONS_TENOR_CHECK          AS optionsTenorCheck,
    LONGEST_TRADE                AS longestTrade,
    ALLOCATED_TENOR              AS allocatedTenor,
    TOTAL_PREMIUM_OVER_24_MONTH  AS totalPremiumOver24Month,
    NON_NETTING                  AS nonNetting,
    ENTITY_ID                    AS entityId,
    FIRST_APPROVED_FOR_TRADING   AS firstApprovedForTrading,
    -- Silver processing columns
    CASE
        WHEN FIRST_APPROVED_FOR_TRADING IS NOT NULL THEN true
        ELSE false
    END AS isApproved
FROM `{bronze_catalog}`.`{bronze_schema}`.vw_new_der_csa
WHERE REPORT_DATE IS NOT NULL
ORDER BY REPORT_DATE, ENTITY_ID
            """
        }
    },
    {
    "name": "Calculate - account_receivable_summary_for_all",
    "source_table": f"`{bronze_catalog}`.`{bronze_schema}`.vw_account_receivable_details",
    "target_table": f"`{silver_catalog}`.`{silver_schema}`.account_receivable_summary_for_all",
    "transformations": {
        "spark_sql": f"""WITH base_data AS (
    SELECT
        CAST(ENTITY_ID AS BIGINT) AS entityId,
        DELIVERY_END_DATE AS deliveryEndDate,
        PAYMENT_DATE AS paymentDate,
        CAST(ACCOUNT_RECEIVABLE AS DECIMAL(38,8)) AS accountReceivable,
        CAST(COALESCE(CE_LIMIT, 0) AS DECIMAL(38,8)) AS ceLimit,
        BASE_REP_CCY AS baseRepCurrency,
        CAST(RUN_DATE AS TIMESTAMP) AS reportDate  -- Keep actual run date as reportDate
    FROM `{bronze_catalog}`.`{bronze_schema}`.vw_account_receivable_details
    WHERE RUN_DATE IS NOT NULL
),

-- Step 1: DELIVERIES - Group by reportDate, entityId and DeliveryEndDate
deliveries AS (
    SELECT
        entityId,
        deliveryEndDate AS eventDate,
        SUM(accountReceivable) AS amount,
        MIN(ceLimit) AS limit,
        'Delivery' AS summaryType,
        FIRST(baseRepCurrency) AS baseRepCurrency,
        reportDate
    FROM base_data
    WHERE deliveryEndDate IS NOT NULL
    GROUP BY reportDate, entityId, deliveryEndDate
),

-- Step 2: PAYMENTS - Group by reportDate, entityId and PaymentDate, negate amount, add 1 hour
payments AS (
    SELECT
        entityId,
        -- C#: t.Key.Value.Date + TimeSpan.FromHours(1)
        TIMESTAMP(DATE(paymentDate)) + INTERVAL 1 HOUR AS eventDate,
        SUM(accountReceivable) * -1 AS amount,  -- NEGATE the amount
        MIN(ceLimit) AS limit,
        'Payment' AS summaryType,
        FIRST(baseRepCurrency) AS baseRepCurrency,
        reportDate
    FROM base_data
    WHERE paymentDate IS NOT NULL
    GROUP BY reportDate, entityId, paymentDate
),

-- Step 3: UNION and ORDER BY DATE (per entity)
events_union AS (
    SELECT * FROM deliveries
    UNION ALL
    SELECT * FROM payments
),

-- Step 4: Calculate ACCRUED (running total per reportDate + entityId, ordered by eventDate)
events_with_accrued AS (
    SELECT
        entityId,
        eventDate,
        amount,
        limit,
        summaryType,
        baseRepCurrency,
        reportDate,
        ROW_NUMBER() OVER (PARTITION BY reportDate, entityId ORDER BY eventDate ASC) AS position,
        SUM(amount) OVER (
            PARTITION BY reportDate, entityId
            ORDER BY eventDate ASC
            ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
        ) AS accrued
    FROM events_union
),

-- Step 5: PEAK CALCULATION - Complex forward-looking logic matching C# exactly
events_with_peak AS (
    SELECT
        e1.*,
        -- C# Logic: results.Skip(count++).GroupBy(t => t.EventDate.Date).Select(x => x.Max(p => p.Accrued)).Max()
        (
            SELECT MAX(daily_max_accrued)
            FROM (
                -- Group events from current position forward by date, get max accrued per date
                SELECT DATE(e2.eventDate) AS event_date, MAX(e2.accrued) AS daily_max_accrued
                FROM events_with_accrued e2
                WHERE e2.reportDate = e1.reportDate
                  AND e2.entityId = e1.entityId
                  AND e2.position >= e1.position  -- Skip(count++) equivalent
                GROUP BY DATE(e2.eventDate)
            ) daily_peaks
        ) AS peak
    FROM events_with_accrued e1
)

-- Final SELECT matching API exactly
SELECT
    entityId,
    eventDate,
    amount,
    accrued,
    limit,
    -- API Logic: Limit > 0.01M ? Accrued / Limit : 0M
    CASE
        WHEN limit > 0.01 THEN accrued / limit
        ELSE 0.0
    END AS outstandingUtilization,
    peak,
    summaryType,
    -- API Logic: Limit > 0.01M ? Peak / Limit : 0M
    CASE
        WHEN limit > 0.01 THEN peak / limit
        ELSE 0.0
    END AS peakUtilization,
    baseRepCurrency,
    reportDate
FROM events_with_peak
ORDER BY reportDate, entityId, eventDate
        """
    }
}
]

# COMMAND ----------

# Execute transformations
results = []

for transform in silver_transformations:
    print(f"\nProcessing: {transform['name']}")
    print("-" * 40)
    
    try:
        # Check if transformation has custom Spark SQL
        if 'spark_sql' in transform.get('transformations', {}):
            # Execute custom Spark SQL
            sql_query = transform['transformations']['spark_sql']
            print(f"Executing Spark SQL: {sql_query}")
            
            result_df = spark.sql(sql_query)
            
            # Add silver audit columns
            result_df = result_df.withColumn("_silver_processed_timestamp", current_timestamp())
            
            # Write to silver
            result_df.write \
                .mode("overwrite") \
                .format("delta") \
                .saveAsTable(transform['target_table'])
            
            records_count = result_df.count()
            print(f"✅ Success: {records_count} records processed")
            print(f"   Target: {transform['target_table']}")
            
            results.append({
                "transformation": transform['name'],
                "status": "success",
                "records": records_count
            })
        else:
            # Use standard transformation framework
            result = transform_framework.execute_transformation(transform)
            
            if result['status'] == 'success':
                print(f"✅ Success: {result['records_processed']} records processed")
                print(f"   Target: {result['target_table']}")
            else:
                print(f"❌ Failed: {result.get('error', 'Unknown error')}")
                
            results.append({
                "transformation": transform['name'],
                "status": result['status'],
                "records": result.get('records_processed', 0)
            })
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        results.append({
            "transformation": transform['name'],
            "status": "failed",
            "error": str(e)
        })

# COMMAND ----------

## Create additional analytics tables
#print("\n" + "="*60)
#print("Creating Analytics Tables")
#print("="*60)
#
## Customer Purchase Summary
#try:
#    spark.sql(f"""
#        CREATE OR REPLACE TABLE {catalog_name}.{silver_schema}.customer_purchase_summary AS
#        SELECT 
#            c.customer_id,
#            c.customer_name,
#            c.city,
#            c.country,
#            COUNT(DISTINCT t.transaction_id) as total_orders,
#            SUM(t.quantity) as total_items,
#            ROUND(SUM(t.amount), 2) as total_spent,
#            ROUND(AVG(t.amount), 2) as avg_order_value,
#            MIN(t.transaction_date) as first_purchase,
#            MAX(t.transaction_date) as last_purchase
#        FROM {catalog_name}.{bronze_schema}.customers c
#        LEFT JOIN {catalog_name}.{bronze_schema}.transactions t
#            ON c.customer_id = t.customer_id
#        GROUP BY c.customer_id, c.customer_name, c.city, c.country
#    """)
#    print("✅ Created customer_purchase_summary")
#except Exception as e:
#    print(f"❌ Failed to create customer_purchase_summary: {str(e)}")
#
## Product Performance
#try:
#    spark.sql(f"""
#        CREATE OR REPLACE TABLE {catalog_name}.{silver_schema}.product_performance AS
#        SELECT 
#            p.product_id,
#            p.product_name,
#            p.category,
#            p.price,
#            COUNT(DISTINCT t.transaction_id) as times_sold,
#            SUM(t.quantity) as units_sold,
#            ROUND(SUM(t.amount), 2) as revenue,
#            ROUND(SUM(t.amount) - (SUM(t.quantity) * p.cost), 2) as profit
#        FROM {catalog_name}.{bronze_schema}.products p
#        LEFT JOIN {catalog_name}.{bronze_schema}.transactions t
#            ON p.product_id = t.product_id
#        GROUP BY p.product_id, p.product_name, p.category, p.price, p.cost
#    """)
#    print("✅ Created product_performance")
#except Exception as e:
#    print(f"❌ Failed to create product_performance: {str(e)}")

# COMMAND ----------

# Summary
print("\n" + "="*60)
print("SILVER LAYER ORCHESTRATION SUMMARY")
print("="*60)

success_count = 0
for r in results:
    if r['status'] == 'success':
        success_count += 1
        
total_count = len(results)

print(f"\nTransformations Completed: {total_count}")
print(f"Successful: {success_count}")
print(f"Failed: {total_count - success_count}")
print(f"Success Rate: {(success_count/total_count*100) if total_count > 0 else 0:.1f}%")

# Show all silver tables
print("\nSilver Tables Created:")
try:
    silver_tables = spark.sql(f"SHOW TABLES IN {catalog_name}.{silver_schema}")
    silver_tables.show()
except:
    print("Could not list silver tables")

# COMMAND ----------

# Return status
dbutils.notebook.exit(json.dumps({
    "status": "SUCCESS" if success_count == total_count else "PARTIAL",
    "transformations_run": total_count,
    "successful": success_count,
    "failed": total_count - success_count,
    "timestamp": datetime.now().isoformat()
}))
