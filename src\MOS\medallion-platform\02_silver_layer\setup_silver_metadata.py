# Databricks notebook source
# MAGIC %md
# MAGIC # Setup Silver Layer Metadata Tables
# MAGIC Creates metadata tables for configuring Silver transformations

# COMMAND ----------

# MAGIC %run ../06_utilities/config_manager

# COMMAND ----------

# Create widgets for workflow parameter passing
dbutils.widgets.text("environment", "dev", "Environment (dev/test/prod)")
dbutils.widgets.text("source_system", "MOS", "Source System (MOS/XVA)")
dbutils.widgets.text("catalog_name", "", "Catalog Name (optional - will use environment default if empty)")

# Get parameters
environment = dbutils.widgets.get("environment")
source_system = dbutils.widgets.get("source_system")
catalog_name_param = dbutils.widgets.get("catalog_name")

# Initialize configuration manager
config_manager = create_config_manager(environment, source_system, catalog_name_param)
config = config_manager.get_complete_runtime_config()

utility_catalog = config["databricks"]["catalogs"]["utility"]
metadata_schema = config["databricks"]["schemas"]["metadata"]
silver_schema = config["databricks"]["schemas"]["silver"]

print(f"✅ Configuration loaded:")
print(f"   Utility Catalog: {utility_catalog}")
print(f"   Metadata Schema: {metadata_schema}")
print(f"   Silver Schema: {silver_schema}")

# Create schemas if they don't exist
config_manager.create_schemas_if_not_exist()

# COMMAND ----------

# MAGIC %md
# MAGIC ## Create Silver Transformations Configuration Table

# COMMAND ----------

spark.sql(f"""
    CREATE TABLE IF NOT EXISTS `{utility_catalog}`.`{metadata_schema}`.silver_transformations (
        transformation_id STRING,
        transformation_name STRING,
        source_table STRING,
        target_table STRING,
        transformation_type STRING, -- overwrite, append, scd_type2
        standardize_columns BOOLEAN,
        transformation_sql STRING, -- Custom SQL transformation
        table_alias STRING, -- Alias for custom SQL
        business_keys STRING, -- JSON array for SCD Type 2
        deduplication_columns STRING, -- JSON array
        deduplication_order_columns STRING, -- JSON array
        exclude_columns STRING, -- JSON array
        execution_order INT,
        is_active BOOLEAN,
        created_date DATE,
        created_by STRING,
        modified_date DATE,
        modified_by STRING
    ) USING DELTA
    COMMENT 'Configuration table for Silver layer transformations'
""")

print("✅ Created silver_transformations table")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Create Silver Transformation Runs Table

# COMMAND ----------

spark.sql(f"""
    CREATE TABLE IF NOT EXISTS `{utility_catalog}`.`{metadata_schema}`.silver_transformation_runs (
        run_id STRING,
        transformation_id STRING,
        run_date DATE,
        start_time TIMESTAMP,
        end_time TIMESTAMP,
        status STRING,
        records_read BIGINT,
        records_written BIGINT,
        error_message STRING,
        duration_seconds INT
    ) USING DELTA
    PARTITIONED BY (run_date)
    COMMENT 'Audit table for Silver transformation runs'
""")

print("✅ Created silver_transformation_runs table")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Insert Sample Transformation Configurations

# COMMAND ----------

# Insert configurations (using MERGE to avoid duplicates)
# Note: Removed DELETE statement as requested - using INSERT OVERWRITE instead

# Insert configurations for our Bronze tables
spark.sql(f"""
    INSERT INTO `{utility_catalog}`.`{metadata_schema}`.silver_transformations VALUES
    -- 1. Simple standardization for test_products
    ('silver_test_products', 'Test Products - Standardized', 'oracle_test_products', 'test_products_silver', 
     'overwrite', true, null, null, null, null, null, null, 1, true, current_date(), 'system', current_date(), 'system'),
    
    -- 2. Test transactions with custom SQL transformation
    ('silver_test_transactions', 'Test Transactions - Enriched', 'oracle_test_transactions', 'test_transactions_silver',
     'overwrite', true, 
     'SELECT *, 
            CASE 
                WHEN amount > 1000 THEN "HIGH" 
                WHEN amount > 500 THEN "MEDIUM" 
                ELSE "LOW" 
            END as transactionCategory,
            CASE 
                WHEN transactionType = "SALE" THEN amount * 0.1 
                ELSE 0 
            END as estimatedProfit
      FROM {src}',
     'src', null, '["transactionId"]', '["transactionDate"]', null, 2, true, current_date(), 'system', current_date(), 'system'),
    
    -- 3. BK Customers with history tracking
    ('silver_bk_customers', 'BK Customers - History Tracking', 'oracle_bkcustomers', 'bkcustomers_silver',
     'scd_type2', true, null, null, '["customerId"]', null, null, '["_ingestion_timestamp", "_ingestion_date", "_pipeline_id"]',
     3, true, current_date(), 'system', current_date(), 'system'),
    
    -- 4. Test orders with additional date logic
    ('silver_test_orders', 'Test Orders - Date Enhanced', 'oracle_test_orders', 'test_orders_silver',
     'overwrite', true,
     'SELECT *,
            DATE_FORMAT(orderDate, "yyyy") as orderYear,
            DATE_FORMAT(orderDate, "MM") as orderMonth,
            DATE_FORMAT(orderDate, "yyyy-MM") as orderYearMonth
      FROM {src}',
     'src', null, '["orderId"]', '["orderDate"]', null, 4, true, current_date(), 'system', current_date(), 'system')
""")

print("✅ Inserted transformation configurations")

# COMMAND ----------

# Display configurations
display(spark.sql(f"""
    SELECT transformation_id, transformation_name, source_table, target_table,
           transformation_type, standardize_columns, execution_order
    FROM `{utility_catalog}`.`{metadata_schema}`.silver_transformations
    WHERE is_active = true
    ORDER BY execution_order
"""))

# COMMAND ----------

# MAGIC %md
# MAGIC ## Create Data Quality Rules Table (Optional)

# COMMAND ----------

spark.sql(f"""
    CREATE TABLE IF NOT EXISTS `{utility_catalog}`.`{metadata_schema}`.silver_data_quality_rules (
        rule_id STRING,
        transformation_id STRING,
        rule_name STRING,
        rule_type STRING, -- not_null, unique, range, pattern, custom_sql
        column_name STRING,
        rule_expression STRING,
        severity STRING, -- error, warning
        is_active BOOLEAN,
        created_date DATE
    ) USING DELTA
    COMMENT 'Data quality rules for Silver transformations'
""")

# Insert sample rules
spark.sql(f"""
    INSERT INTO `{utility_catalog}`.`{metadata_schema}`.silver_data_quality_rules VALUES
    ('dq_001', 'silver_test_products', 'Product price not null', 'not_null', 'price', 'price IS NOT NULL', 'error', true, current_date()),
    ('dq_002', 'silver_test_products', 'Product price positive', 'range', 'price', 'price > 0', 'error', true, current_date()),
    ('dq_003', 'silver_test_transactions', 'Transaction amount positive', 'range', 'amount', 'amount >= 0', 'error', true, current_date()),
    ('dq_004', 'silver_bk_customers', 'Email format valid', 'pattern', 'email', "email RLIKE '^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+$'", 'warning', true, current_date())
""")

print("✅ Created data quality rules table and sample rules")

# COMMAND ----------

print("\n✅ Silver metadata setup complete!")
