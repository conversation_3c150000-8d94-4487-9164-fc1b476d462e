dbutils.widgets.text("ipv_silver_catalog", "mbcl_silver", "ipv_silver_catalog")
dbutils.widgets.text("ipv_silver_schema", "mbcl_ipv", "ipv_silver_schema")

dbutils.widgets.text("ipv_bronze_catalog", "mbcl_bronze", "ipv_bronze_catalog")
dbutils.widgets.text("ipv_bronze_schema", "mbcl_ipv", "ipv_bronze_schema")

dbutils.widgets.text("reference_gas_table", "bronze_reference_gas", "reference_gas_table")
dbutils.widgets.text("reference_power_table", "bronze_reference_power", "reference_power_table")
dbutils.widgets.text("reference_months_strips_table", "bronze_reference_months_strips", "reference_months_strips_table")

from pyspark.sql import functions as F
from pyspark.sql.window import Window
from delta.tables import DeltaTable

ipv_silver_catalog = dbutils.widgets.get("ipv_silver_catalog")
ipv_silver_schema = dbutils.widgets.get("ipv_silver_schema")

ipv_bronze_catalog = dbutils.widgets.get("ipv_bronze_catalog")
ipv_bronze_schema = dbutils.widgets.get("ipv_bronze_schema")

reference_gas_table_name = dbutils.widgets.get("reference_gas_table")
reference_power_table_name = dbutils.widgets.get("reference_power_table")
reference_months_strips_table_name = dbutils.widgets.get("reference_months_strips_table")

OL_DELTA_REPORT_GAS_PK = ['contract_date', 'report_date', 'index_name']
OL_DELTA_REPORT_POWER_PK = ['contract_date', 'report_date', 'index_name']

BRONZE_OL_DELTA_REPORT_POWER_INGESTION_TIMESTAMP_COLUMN = 'ingestion_timestamp'
BRONZE_OL_DELTA_REPORT_GAS_INGESTION_TIMESTAMP_COLUMN = 'ingestion_timestamp'

OL_DELTA_REPORT_KNOWN_COLS = ["contract", "total", "runtotal", "ingestion_timestamp", "first_row_date"]

REFERENCE_POWER_OL_CODE_TO_OL_DELTA_INDEX_TABLE_NAME = "bronze_reference_power_ol_code_to_ol_delta_index"

def get_last_processed_ts(pipeline_name, watermark_table):
    try:
        last_ts = (
            spark.read.table(watermark_table)
            .filter(F.col("pipeline_name") == pipeline_name)
            .select("last_processed")
            .collect()[0]["last_processed"]
        )
    except Exception:
        last_ts = None
    return last_ts


def read_incremental_bronze(bronze_table, ingestion_col, last_ts):
    df = spark.read.table(bronze_table)
    if last_ts:
        df = df.filter(F.col(ingestion_col) > last_ts)
    return df


def unpivot_bronze(df, known_cols):
    unpivot_cols = [c for c in df.columns if c not in known_cols]
    expr = "stack({0}, {1}) as (index_name, value)".format(
        len(unpivot_cols),
        ", ".join([f"'{c}', `{c}`" for c in unpivot_cols])
    )
    return df.selectExpr(*known_cols, expr)


def clean_and_format_dates(df):
    df = (
        df
        .withColumnRenamed("first_row_date", "report_date")
        .withColumnRenamed("contract", "contract_date")
        .withColumn(
            "report_date_str",
            F.regexp_extract(F.col("report_date"), r'(\d{2}-[A-Za-z]{3}-\d{2})', 1)
        )
        .withColumn(
            "report_date",
            F.to_date(F.col("report_date_str"), "dd-MMM-yy")
        )
        .drop('report_date_str')
    )
    df = (
        df
        .withColumn("report_year", F.year("report_date"))
        .withColumn(
            "contract_date",
            F.when(
                F.regexp_extract(F.col("contract_date"), r'^[A-Za-z]{3}-\d{2}$', 0) != "",
                F.to_date(F.concat(F.lit("01-"), F.col("contract_date")), "dd-MMM-yy")
            )
            .when(
                F.regexp_extract(F.col("contract_date"), r'^\d{2}-[A-Za-z]{3}$', 0) != "",
                F.to_date(F.concat(F.col("contract_date"), F.lit("-"), F.col("report_year").cast("string")), "dd-MMM-yyyy")
            )
        )
    )
    return df


def add_aux_columns(df):
    df = df.drop('runtotal', 'total')
    df = df.withColumn("contract_year", F.year("contract_date")).withColumn("contract_month", F.month("contract_date"))
    df = df.withColumn(
        "next_month",
        F.expr("CASE WHEN month(report_date) = 12 THEN 1 ELSE month(report_date) + 1 END")
    ).withColumn(
        "next_year",
        F.expr("CASE WHEN month(report_date) = 12 THEN year(report_date) + 1 ELSE year(report_date) END")
    )
    return df


def apply_power_reference_flags(df):
    """
    Casts pk, op, rt, da columns to boolean and adds is_peak and is_rt flags.
    """
    return (
        df
        .withColumn("pk", F.col("pk").cast("boolean"))
        .withColumn("op", F.col("op").cast("boolean"))
        .withColumn("rt", F.col("rt").cast("boolean"))
        .withColumn("da", F.col("da").cast("boolean"))
        .withColumn("is_peak", F.col("pk"))
        .withColumn("is_rt", F.col("rt"))
    )


def assign_ingestion_and_report_date(initial_df, meta_df, ingestion_col="ingestion_timestamp", report_col="report_date"):
    """
    Assigns the first ingestion_timestamp and report_date from the DataFrame to all rows.
    """
    ingestion_ts = meta_df.select(ingestion_col).limit(1).collect()[0][ingestion_col]
    report_dt = meta_df.select(report_col).limit(1).collect()[0][report_col]
    return initial_df.withColumn(ingestion_col, F.lit(ingestion_ts)).withColumn(report_col, F.lit(report_dt))


def compute_balmo(bronze_df, descriptive_configs):
    balmo_df = (
        bronze_df
        .filter(
            (F.month("contract_date") == F.month("report_date")) &
            (F.year("contract_date") == F.year("report_date")) &
            (F.col("contract_date") >= F.col("report_date"))
        )
        .groupby("index_name", "contract_year", "contract_month")
        .agg(F.sum("value").alias("balmo"))
    )

    balmo_df = assign_ingestion_and_report_date(balmo_df, bronze_df)

    balmo_df = (
        balmo_df
        .join(
            descriptive_configs['balmo_join_table_params'][0],
            descriptive_configs['balmo_join_table_params'][1],
            descriptive_configs['balmo_join_table_params'][2]
        )
    )

    balmo_df = (
        balmo_df
        .groupby("ice_code")
        .agg(
            F.first("index_name").alias("index_name"),
            F.sum("balmo").alias("balmo"),
            F.first("contract_year").alias("contract_year"),
            F.first("contract_month").alias("contract_month"),
            F.first("ingestion_timestamp").alias("ingestion_timestamp"),
            F.first("report_date").alias("report_date")
        )
        .withColumnRenamed("ice_code", "auxiliar_ice_cod")
    )

    balmo_df = (
        balmo_df
        .join(
            descriptive_configs['balmo_join_table_params'][0],
            descriptive_configs['balmo_join_table_params'][1],
            descriptive_configs['balmo_join_table_params'][2]
        )
    )

    balmo_df = balmo_df.drop("index_name", "auxiliar_ice_cod")

    if not descriptive_configs["is_gas"]:
        balmo_df = apply_power_reference_flags(balmo_df)
    return balmo_df


def compute_prompt(bronze_df, descriptive_configs):
    prompt_df = (
        bronze_df
        .filter(
            (F.month("contract_date") == F.col("next_month")) &
            (F.year("contract_date") == F.col("next_year"))
        )
        .groupby("index_name", "contract_year", "contract_month")
        .agg(F.sum("value").alias("prompt"))
    )

    prompt_df = assign_ingestion_and_report_date(prompt_df, bronze_df)

    prompt_df = (
        prompt_df
        .join(
            descriptive_configs['prompt_join_table_params'][0],
            descriptive_configs['prompt_join_table_params'][1],
            descriptive_configs['prompt_join_table_params'][2]
        )
    )

    prompt_df = (
        prompt_df
        .groupby("ice_code")
        .agg(
            F.first("index_name").alias("index_name"),
            F.sum("prompt").alias("prompt"),
            F.first("contract_year").alias("contract_year"),
            F.first("contract_month").alias("contract_month"),
            F.first("ingestion_timestamp").alias("ingestion_timestamp"),
            F.first("report_date").alias("report_date")
        )
        .withColumnRenamed("ice_code", "auxiliar_ice_cod")
    )

    prompt_df = (
        prompt_df
        .join(
            descriptive_configs['prompt_join_table_params'][0],
            descriptive_configs['prompt_join_table_params'][1],
            descriptive_configs['prompt_join_table_params'][2]
        )
    )

    prompt_df = prompt_df.drop("index_name", "auxiliar_ice_cod")

    if not descriptive_configs["is_gas"]:
        prompt_df = apply_power_reference_flags(prompt_df)
    return prompt_df


def ensure_delta_table_exists(spark, source_df, table_name):
    if not spark.catalog.tableExists(table_name):
        source_df.write.format("delta").mode("overwrite").saveAsTable(table_name)


def upsert_to_delta_table(
    spark,
    source_df,
    target_table_name,
    merge_keys,
    update_columns,
    insert_columns,
    timestamp_column="ingestion_timestamp"
):
    """
    Upserts source_df into target_table_name using Delta Lake merge.
    Only updates if source timestamp is newer.
    """
    ensure_delta_table_exists(spark, source_df, target_table_name)

    target = DeltaTable.forName(spark, target_table_name)
    source = source_df.alias("source")
    target_alias = target.alias("target")

    #merge condition
    merge_condition = " AND ".join([
        f"target.{k} = source.{k}" for k in merge_keys
    ])

    #update set dict
    update_set = {col: f"source.{col}" for col in update_columns}

    #insert values dict
    if insert_columns:
        insert_values = {col: f"source.{col}" for col in insert_columns}
    else:
        insert_values = {col: f"source.{col}" for col in source_df.columns}

    (
        target_alias.merge(
            source,
            merge_condition
        )
        .whenMatchedUpdate(
            condition=f"source.{timestamp_column} > target.{timestamp_column}",
            set=update_set
        )
        .whenNotMatchedInsert(
            values=insert_values
        )
        .execute()
    )
    

def dynamic_agg(df, group_cols, sum_col):
    # All columns except group_cols and sum_col
    other_cols = [c for c in df.columns if c not in group_cols + [sum_col]]
    agg_exprs = [F.sum(sum_col).alias(sum_col)] + [
        F.first(c).alias(c) for c in other_cols
    ]
    return df.groupBy(*group_cols).agg(*agg_exprs)


def join_and_upsert_silver(
    bronze_df, balmo_df, prompt_df, bronze_primary_key, reference_tables, is_gas, silver_table
):
    # Standardize value column
    bronze_df = bronze_df.withColumnRenamed("value", "value")
    
    if prompt_df:
        prompt_df = prompt_df.withColumnRenamed("prompt", "value")
        prompt_df = prompt_df.withColumnRenamed("ref_index_name", "index_name")
        prompt_df = prompt_df.select("value", "contract_year", "contract_month", "ingestion_timestamp", "report_date", "index_name")

    if balmo_df:
        balmo_df = balmo_df.withColumnRenamed("balmo", "value")
        balmo_df = balmo_df.withColumnRenamed("ref_index_name", "index_name")
        balmo_df = balmo_df.select("value", "contract_year", "contract_month", "ingestion_timestamp", "report_date", "index_name")
    
    # Filter out current/next month as before
    if is_gas:
        filtered_bronze = bronze_df.filter(
            ~(
                (F.month("contract_date") == F.month("report_date")) &
                (F.year("contract_date") == F.year("report_date"))
            )
        )
        final_df = filtered_bronze.unionByName(balmo_df, allowMissingColumns=True)
    else:
        filtered_bronze = bronze_df.filter(
            ~(
                ((F.month("contract_date") == F.month("report_date")) &
                 (F.year("contract_date") == F.year("report_date")))
                |
                ((F.month("contract_date") == F.col("next_month")) &
                 (F.year("contract_date") == F.col("next_year")))
            )
        )
        final_df = (
            filtered_bronze
            .unionByName(balmo_df, allowMissingColumns=True)
            .unionByName(prompt_df, allowMissingColumns=True)
        )

    # Reference table joins if needed
    for reference_table, join_condition, join_type in reference_tables.values():
        final_df = final_df.join(reference_table, join_condition, join_type)

    final_df = dynamic_agg(final_df, ["ref_index_name", "contract_date", "report_date"], "value")

    final_df = final_df.withColumn("primary_key", F.sha2(F.concat_ws("||", *bronze_primary_key), 256))
    final_df = final_df.dropDuplicates(bronze_primary_key)

    if not is_gas:
        final_df = apply_power_reference_flags(final_df)

    # # Upsert to delta table
    upsert_to_delta_table(
        spark,
        final_df,
        silver_table,
        merge_keys=bronze_primary_key,
        update_columns=final_df.columns,  # or specify only the columns you want to update
        insert_columns=None
    )
    return final_df


def update_watermark(pipeline_name, new_last_ts, watermark_table):
    watermark_update = spark.createDataFrame(
        [(pipeline_name, new_last_ts)],
        ["pipeline_name", "last_processed"]
    )
    (watermark_update
        .write
        .mode("overwrite")
        .option("replaceWhere", f"pipeline_name = '{pipeline_name}'")
        .saveAsTable(watermark_table)
    )

def process_incremental_bronze_to_silver(
    pipeline_name,
    bronze_table,
    bronze_primary_key,
    bronze_ingestion_timestamp_column,
    silver_table,
    reference_tables,
    descriptive_configs
):
    write_prompt = True if descriptive_configs.get('write_prompt') else False
    write_balmo = True if descriptive_configs.get('write_balmo') else False
    write_delta = True if descriptive_configs.get('write_delta') else False
    
    watermark_table = f"{ipv_silver_catalog}.{ipv_silver_schema}.silver_watermark"
    last_ts = get_last_processed_ts(pipeline_name, watermark_table)
    df_bronze = read_incremental_bronze(bronze_table, bronze_ingestion_timestamp_column, last_ts)
    new_rows_count = df_bronze.count()
    if new_rows_count == 0:
        print("No new rows to process")
        return

    bronze_df = unpivot_bronze(df_bronze, OL_DELTA_REPORT_KNOWN_COLS)
    bronze_df = clean_and_format_dates(bronze_df)
    bronze_df = add_aux_columns(bronze_df)

    balmo_df = compute_balmo(bronze_df, descriptive_configs) if write_balmo else None
    prompt_df = compute_prompt(bronze_df, descriptive_configs) if write_prompt else None

    if write_balmo:
        upsert_to_delta_table(
            spark,
            balmo_df,
            descriptive_configs['balmo_table_name'],
            merge_keys=["ice_code", "contract_year", "contract_month"],
            update_columns=["balmo", "ingestion_timestamp"],
            insert_columns=None
        )

    if write_prompt:
        upsert_to_delta_table(
            spark,
            prompt_df,
            descriptive_configs['prompt_table_name'],
            merge_keys=["ice_code", "contract_year", "contract_month"],
            update_columns=["prompt", "ingestion_timestamp"],
            insert_columns=None
        )

    if write_delta:
        join_and_upsert_silver(
            bronze_df, balmo_df, prompt_df, bronze_primary_key, reference_tables,
            descriptive_configs["is_gas"], silver_table
        )

    # Update watermark
    new_last_ts = df_bronze.agg(F.max(bronze_ingestion_timestamp_column).alias("max_ts")).collect()[0]["max_ts"]
    update_watermark(pipeline_name, new_last_ts, watermark_table)

reference_gas = spark.read.table(f"{ipv_bronze_catalog}.{ipv_bronze_schema}.{reference_gas_table_name}")
reference_power = spark.read.table(f"{ipv_bronze_catalog}.{ipv_bronze_schema}.{reference_power_table_name}")

ol_df = reference_gas.select(
    F.col("region"),
    F.col("location_hub"),
    F.col("ice_code"),
    F.col("ol_code").alias("ref_index_name")
)

gd_df = reference_gas.select(
    F.col("region"),
    F.col("location_hub"),
    F.col("ice_code"),
    F.col("gd_code").alias("ref_index_name")
)
reference_gas_stacked_index_name = ol_df.unionByName(gd_df).filter(F.col("ref_index_name").isNotNull())
reference_gas_stacked_index_name = reference_gas_stacked_index_name.withColumn("ref_index_name", F.lower("ref_index_name"))

reference_power_ol_code_to_ol_delta_index = spark.table(f"{ipv_bronze_catalog}.{ipv_bronze_schema}.{REFERENCE_POWER_OL_CODE_TO_OL_DELTA_INDEX_TABLE_NAME}")

reference_power_stacked_index_name = reference_power.withColumnRenamed("ol_code", "ref_index_name")
reference_power_stacked_index_name = reference_power_stacked_index_name.withColumn("ref_index_name", F.lower("ref_index_name"))
reference_power_stacked_index_name = (
    reference_power_stacked_index_name
    .join(
        F.broadcast(
            reference_power_ol_code_to_ol_delta_index
            .withColumn("ol_code", F.lower("ol_code"))
        ), 
        on=F.col("ref_index_name") == F.col("ol_code"),
        how="inner"
    )
    .drop("ol_code")
)

silver_pipeline_steps = [
    (
        "silver_ol_delta_report_power",
        f"{ipv_bronze_catalog}.{ipv_bronze_schema}.bronze_ol_delta_report_power",
        OL_DELTA_REPORT_POWER_PK,
        BRONZE_OL_DELTA_REPORT_POWER_INGESTION_TIMESTAMP_COLUMN,
        f"{ipv_silver_catalog}.{ipv_silver_schema}.silver_ol_delta_report_power",
        {
            'reference_power': (
                F.broadcast(reference_power_stacked_index_name).withColumnRenamed('ingestion_timestamp', 'reference_power_ingestion_timestamp'), 
                F.col('index_name') == F.col('ol_index'), 
                'inner'
            ),
        },
        {
            'is_gas': False,
            'write_balmo': True,
            'write_prompt': True,
            'write_delta': True,
            'balmo_table_name': f"{ipv_silver_catalog}.{ipv_silver_schema}.silver_ol_delta_report_balmo_power",
            'prompt_table_name': f"{ipv_silver_catalog}.{ipv_silver_schema}.silver_ol_delta_report_prompt_power",
            'balmo_join_table_params': (
                F.broadcast(reference_power_stacked_index_name).withColumnRenamed('ingestion_timestamp', 'reference_gas_ingestion_timestamp'),
                F.col('index_name') == F.col('ol_index'),
                'inner'
            ),
            'prompt_join_table_params': (
                F.broadcast(reference_power_stacked_index_name).withColumnRenamed('ingestion_timestamp', 'reference_gas_ingestion_timestamp'),
                F.col('index_name') == F.col('ol_index'),
                'inner'
            )
        }
    ),
    (
        "silver_ol_delta_report_gas",
        f"{ipv_bronze_catalog}.{ipv_bronze_schema}.bronze_ol_delta_report_gas",
        OL_DELTA_REPORT_GAS_PK,
        BRONZE_OL_DELTA_REPORT_GAS_INGESTION_TIMESTAMP_COLUMN,
        f"{ipv_silver_catalog}.{ipv_silver_schema}.silver_ol_delta_report_gas",
        {
            'reference_gas': (
                F.broadcast(reference_gas_stacked_index_name).withColumnRenamed('ingestion_timestamp', 'reference_gas_ingestion_timestamp'), 
                F.col('index_name') == F.col('ref_index_name'),
                'inner'
            ),
        },
        {
            'is_gas': True,
            'write_balmo': True,
            'write_prompt': False,
            'write_delta': True,
            'balmo_table_name': f"{ipv_silver_catalog}.{ipv_silver_schema}.silver_ol_delta_report_balmo_gas",
            'balmo_join_table_params': (
                F.broadcast(reference_gas_stacked_index_name).withColumnRenamed('ingestion_timestamp', 'reference_gas_ingestion_timestamp'),
                F.col('index_name') == F.col('ref_index_name'),
                'inner'
            )
        }
    ),
]

for pipeline_name, bronze_table, bronze_pk, bronze_ingestion_time_column_name, silver_table, join_tables, descriptive_configs  in silver_pipeline_steps:
    process_incremental_bronze_to_silver(
        pipeline_name,
        bronze_table,
        bronze_pk,
        bronze_ingestion_time_column_name,
        silver_table,
        join_tables,
        descriptive_configs
    )

# names_tb_deleted = ['silver_ol_delta_report_power', 'silver_ol_delta_report_gas', 
#                     'silver_ol_delta_report_prompt_power', 'silver_ol_delta_report_balmo_power',
#                     'silver_ol_delta_report_prompt_gas', 'silver_ol_delta_report_balmo_gas']
# names_str = ','.join([f"'{n}'" for n in names_tb_deleted])
# spark.sql(f"DELETE FROM {ipv_silver_catalog}.{ipv_silver_schema}.silver_watermark WHERE pipeline_name IN ({names_str})")

# tables = spark.sql("show tables in mbcl_dev_silver.ipv").collect()
# tables = [t for t in tables if t.tableName in names_tb_deleted]

# for t in tables:
#     table_name = t.tableName
#     print(f"Dropping table {table_name}")
#     spark.sql(f"DROP TABLE IF EXISTS mbcl_dev_silver.ipv.{table_name}")

