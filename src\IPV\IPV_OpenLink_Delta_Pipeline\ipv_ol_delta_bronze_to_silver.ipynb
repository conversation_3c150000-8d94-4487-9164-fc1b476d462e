{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "64db659c-4ed0-4e09-b173-6115edaed928", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["dbutils.widgets.text(\"ipv_silver_catalog\", \"mbcl_silver\", \"ipv_silver_catalog\")\n", "dbutils.widgets.text(\"ipv_silver_schema\", \"mbcl_ipv\", \"ipv_silver_schema\")\n", "\n", "dbutils.widgets.text(\"ipv_bronze_catalog\", \"mbcl_bronze\", \"ipv_bronze_catalog\")\n", "dbutils.widgets.text(\"ipv_bronze_schema\", \"mbcl_ipv\", \"ipv_bronze_schema\")\n", "\n", "dbutils.widgets.text(\"reference_gas_table\", \"bronze_reference_gas\", \"reference_gas_table\")\n", "dbutils.widgets.text(\"reference_power_table\", \"bronze_reference_power\", \"reference_power_table\")\n", "dbutils.widgets.text(\"reference_months_strips_table\", \"bronze_reference_months_strips\", \"reference_months_strips_table\")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "43646b0e-e8d0-42cd-a623-a8580fe84bda", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["from pyspark.sql import functions as F\n", "from pyspark.sql.window import Window\n", "from delta.tables import DeltaTable"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "81847bab-0c87-43ee-94c2-b9add4c17508", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["ipv_silver_catalog = dbutils.widgets.get(\"ipv_silver_catalog\")\n", "ipv_silver_schema = dbutils.widgets.get(\"ipv_silver_schema\")\n", "\n", "ipv_bronze_catalog = dbutils.widgets.get(\"ipv_bronze_catalog\")\n", "ipv_bronze_schema = dbutils.widgets.get(\"ipv_bronze_schema\")\n", "\n", "reference_gas_table_name = dbutils.widgets.get(\"reference_gas_table\")\n", "reference_power_table_name = dbutils.widgets.get(\"reference_power_table\")\n", "reference_months_strips_table_name = dbutils.widgets.get(\"reference_months_strips_table\")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "662f21f8-fab7-480d-9f66-2749e09e1a72", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["OL_DELTA_REPORT_GAS_PK = ['contract_date', 'report_date', 'index_name']\n", "OL_DELTA_REPORT_POWER_PK = ['contract_date', 'report_date', 'index_name']\n", "\n", "BRONZE_OL_DELTA_REPORT_POWER_INGESTION_TIMESTAMP_COLUMN = 'ingestion_timestamp'\n", "BRONZE_OL_DELTA_REPORT_GAS_INGESTION_TIMESTAMP_COLUMN = 'ingestion_timestamp'\n", "\n", "OL_DELTA_REPORT_KNOWN_COLS = [\"contract\", \"total\", \"runtotal\", \"ingestion_timestamp\", \"first_row_date\"]\n", "\n", "REFERENCE_POWER_OL_CODE_TO_OL_DELTA_INDEX_TABLE_NAME = \"bronze_reference_power_ol_code_to_ol_delta_index\""]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "a18d056e-d7f9-4673-81f0-d4aa927d6567", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["def get_last_processed_ts(pipeline_name, watermark_table):\n", "    try:\n", "        last_ts = (\n", "            spark.read.table(watermark_table)\n", "            .filter(F.col(\"pipeline_name\") == pipeline_name)\n", "            .select(\"last_processed\")\n", "            .collect()[0][\"last_processed\"]\n", "        )\n", "    except Exception:\n", "        last_ts = None\n", "    return last_ts\n", "\n", "\n", "def read_incremental_bronze(bronze_table, ingestion_col, last_ts):\n", "    df = spark.read.table(bronze_table)\n", "    if last_ts:\n", "        df = df.filter(F.col(ingestion_col) > last_ts)\n", "    return df\n", "\n", "\n", "def unpivot_bronze(df, known_cols):\n", "    unpivot_cols = [c for c in df.columns if c not in known_cols]\n", "    expr = \"stack({0}, {1}) as (index_name, value)\".format(\n", "        len(unpivot_cols),\n", "        \", \".join([f\"'{c}', `{c}`\" for c in unpivot_cols])\n", "    )\n", "    return df.selectExpr(*known_cols, expr)\n", "\n", "\n", "def clean_and_format_dates(df):\n", "    df = (\n", "        df\n", "        .withColumnRenamed(\"first_row_date\", \"report_date\")\n", "        .withColumnRenamed(\"contract\", \"contract_date\")\n", "        .withColumn(\n", "            \"report_date_str\",\n", "            F.regexp_extract(<PERSON>.col(\"report_date\"), r'(\\d{2}-[A-Za-z]{3}-\\d{2})', 1)\n", "        )\n", "        .withColumn(\n", "            \"report_date\",\n", "            F.to_date(F.col(\"report_date_str\"), \"dd-MMM-yy\")\n", "        )\n", "        .drop('report_date_str')\n", "    )\n", "    df = (\n", "        df\n", "        .withColumn(\"report_year\", F.year(\"report_date\"))\n", "        .withColumn(\n", "            \"contract_date\",\n", "            F.when(\n", "                F.regexp_extract(<PERSON>.col(\"contract_date\"), r'^[A-Za-z]{3}-\\d{2}$', 0) != \"\",\n", "                F.to_date(<PERSON><PERSON>concat(F.lit(\"01-\"), <PERSON><PERSON>col(\"contract_date\")), \"dd-MMM-yy\")\n", "            )\n", "            .when(\n", "                F.regexp_extract(F.col(\"contract_date\"), r'^\\d{2}-[A-Za-z]{3}$', 0) != \"\",\n", "                F.to_date(<PERSON><PERSON>concat(F.col(\"contract_date\"), F<PERSON>lit(\"-\"), F.col(\"report_year\").cast(\"string\")), \"dd-MMM-yyyy\")\n", "            )\n", "        )\n", "    )\n", "    return df\n", "\n", "\n", "def add_aux_columns(df):\n", "    df = df.drop('runtotal', 'total')\n", "    df = df.withColumn(\"contract_year\", F.year(\"contract_date\")).withColumn(\"contract_month\", F.month(\"contract_date\"))\n", "    df = df.withColumn(\n", "        \"next_month\",\n", "        F.expr(\"CASE WHEN month(report_date) = 12 THEN 1 ELSE month(report_date) + 1 END\")\n", "    ).withColumn(\n", "        \"next_year\",\n", "        F.expr(\"CASE WHEN month(report_date) = 12 THEN year(report_date) + 1 ELSE year(report_date) END\")\n", "    )\n", "    return df\n", "\n", "\n", "def apply_power_reference_flags(df):\n", "    \"\"\"\n", "    Casts pk, op, rt, da columns to boolean and adds is_peak and is_rt flags.\n", "    \"\"\"\n", "    return (\n", "        df\n", "        .withColumn(\"pk\", <PERSON><PERSON>col(\"pk\").cast(\"boolean\"))\n", "        .with<PERSON><PERSON><PERSON>n(\"op\", <PERSON><PERSON>col(\"op\").cast(\"boolean\"))\n", "        .withColumn(\"rt\", <PERSON><PERSON>col(\"rt\").cast(\"boolean\"))\n", "        .with<PERSON><PERSON><PERSON>n(\"da\", <PERSON><PERSON>col(\"da\").cast(\"boolean\"))\n", "        .withColumn(\"is_peak\", F.col(\"pk\"))\n", "        .withColumn(\"is_rt\", <PERSON>.col(\"rt\"))\n", "    )\n", "\n", "\n", "def assign_ingestion_and_report_date(initial_df, meta_df, ingestion_col=\"ingestion_timestamp\", report_col=\"report_date\"):\n", "    \"\"\"\n", "    Assigns the first ingestion_timestamp and report_date from the DataFrame to all rows.\n", "    \"\"\"\n", "    ingestion_ts = meta_df.select(ingestion_col).limit(1).collect()[0][ingestion_col]\n", "    report_dt = meta_df.select(report_col).limit(1).collect()[0][report_col]\n", "    return initial_df.withColumn(ingestion_col, F.lit(ingestion_ts)).withColumn(report_col, F.lit(report_dt))\n", "\n", "\n", "def compute_balmo(bronze_df, descriptive_configs):\n", "    balmo_df = (\n", "        bronze_df\n", "        .filter(\n", "            (F.month(\"contract_date\") == F.month(\"report_date\")) &\n", "            (F.year(\"contract_date\") == F.year(\"report_date\")) &\n", "            (F.col(\"contract_date\") >= F.col(\"report_date\"))\n", "        )\n", "        .groupby(\"index_name\", \"contract_year\", \"contract_month\")\n", "        .agg(F.sum(\"value\").alias(\"balmo\"))\n", "    )\n", "\n", "    balmo_df = assign_ingestion_and_report_date(balmo_df, bronze_df)\n", "\n", "    balmo_df = (\n", "        balmo_df\n", "        .join(\n", "            descriptive_configs['balmo_join_table_params'][0],\n", "            descriptive_configs['balmo_join_table_params'][1],\n", "            descriptive_configs['balmo_join_table_params'][2]\n", "        )\n", "    )\n", "\n", "    balmo_df = (\n", "        balmo_df\n", "        .groupby(\"ice_code\")\n", "        .agg(\n", "            F.first(\"index_name\").alias(\"index_name\"),\n", "            F.sum(\"balmo\").alias(\"balmo\"),\n", "            F.first(\"contract_year\").alias(\"contract_year\"),\n", "            F.first(\"contract_month\").alias(\"contract_month\"),\n", "            F.first(\"ingestion_timestamp\").alias(\"ingestion_timestamp\"),\n", "            F.first(\"report_date\").alias(\"report_date\")\n", "        )\n", "        .withColumnRenamed(\"ice_code\", \"auxiliar_ice_cod\")\n", "    )\n", "\n", "    balmo_df = (\n", "        balmo_df\n", "        .join(\n", "            descriptive_configs['balmo_join_table_params'][0],\n", "            descriptive_configs['balmo_join_table_params'][1],\n", "            descriptive_configs['balmo_join_table_params'][2]\n", "        )\n", "    )\n", "\n", "    balmo_df = balmo_df.drop(\"index_name\", \"auxiliar_ice_cod\")\n", "\n", "    if not descriptive_configs[\"is_gas\"]:\n", "        balmo_df = apply_power_reference_flags(balmo_df)\n", "    return balmo_df\n", "\n", "\n", "def compute_prompt(bronze_df, descriptive_configs):\n", "    prompt_df = (\n", "        bronze_df\n", "        .filter(\n", "            (F.month(\"contract_date\") == F.col(\"next_month\")) &\n", "            (F.year(\"contract_date\") == F.col(\"next_year\"))\n", "        )\n", "        .groupby(\"index_name\", \"contract_year\", \"contract_month\")\n", "        .agg(F.sum(\"value\").alias(\"prompt\"))\n", "    )\n", "\n", "    prompt_df = assign_ingestion_and_report_date(prompt_df, bronze_df)\n", "\n", "    prompt_df = (\n", "        prompt_df\n", "        .join(\n", "            descriptive_configs['prompt_join_table_params'][0],\n", "            descriptive_configs['prompt_join_table_params'][1],\n", "            descriptive_configs['prompt_join_table_params'][2]\n", "        )\n", "    )\n", "\n", "    prompt_df = (\n", "        prompt_df\n", "        .groupby(\"ice_code\")\n", "        .agg(\n", "            F.first(\"index_name\").alias(\"index_name\"),\n", "            F.sum(\"prompt\").alias(\"prompt\"),\n", "            F.first(\"contract_year\").alias(\"contract_year\"),\n", "            F.first(\"contract_month\").alias(\"contract_month\"),\n", "            F.first(\"ingestion_timestamp\").alias(\"ingestion_timestamp\"),\n", "            F.first(\"report_date\").alias(\"report_date\")\n", "        )\n", "        .withColumnRenamed(\"ice_code\", \"auxiliar_ice_cod\")\n", "    )\n", "\n", "    prompt_df = (\n", "        prompt_df\n", "        .join(\n", "            descriptive_configs['prompt_join_table_params'][0],\n", "            descriptive_configs['prompt_join_table_params'][1],\n", "            descriptive_configs['prompt_join_table_params'][2]\n", "        )\n", "    )\n", "\n", "    prompt_df = prompt_df.drop(\"index_name\", \"auxiliar_ice_cod\")\n", "\n", "    if not descriptive_configs[\"is_gas\"]:\n", "        prompt_df = apply_power_reference_flags(prompt_df)\n", "    return prompt_df\n", "\n", "\n", "def ensure_delta_table_exists(spark, source_df, table_name):\n", "    if not spark.catalog.tableExists(table_name):\n", "        source_df.write.format(\"delta\").mode(\"overwrite\").saveAsTable(table_name)\n", "\n", "\n", "def upsert_to_delta_table(\n", "    spark,\n", "    source_df,\n", "    target_table_name,\n", "    merge_keys,\n", "    update_columns,\n", "    insert_columns,\n", "    timestamp_column=\"ingestion_timestamp\"\n", "):\n", "    \"\"\"\n", "    Upserts source_df into target_table_name using Delta Lake merge.\n", "    Only updates if source timestamp is newer.\n", "    \"\"\"\n", "    ensure_delta_table_exists(spark, source_df, target_table_name)\n", "\n", "    target = DeltaTable.forName(spark, target_table_name)\n", "    source = source_df.alias(\"source\")\n", "    target_alias = target.alias(\"target\")\n", "\n", "    #merge condition\n", "    merge_condition = \" AND \".join([\n", "        f\"target.{k} = source.{k}\" for k in merge_keys\n", "    ])\n", "\n", "    #update set dict\n", "    update_set = {col: f\"source.{col}\" for col in update_columns}\n", "\n", "    #insert values dict\n", "    if insert_columns:\n", "        insert_values = {col: f\"source.{col}\" for col in insert_columns}\n", "    else:\n", "        insert_values = {col: f\"source.{col}\" for col in source_df.columns}\n", "\n", "    (\n", "        target_alias.merge(\n", "            source,\n", "            merge_condition\n", "        )\n", "        .whenMatchedUpdate(\n", "            condition=f\"source.{timestamp_column} > target.{timestamp_column}\",\n", "            set=update_set\n", "        )\n", "        .whenNotMatchedInsert(\n", "            values=insert_values\n", "        )\n", "        .execute()\n", "    )\n", "    \n", "\n", "def dynamic_agg(df, group_cols, sum_col):\n", "    # All columns except group_cols and sum_col\n", "    other_cols = [c for c in df.columns if c not in group_cols + [sum_col]]\n", "    agg_exprs = [F.sum(sum_col).alias(sum_col)] + [\n", "        F.first(c).alias(c) for c in other_cols\n", "    ]\n", "    return df.groupBy(*group_cols).agg(*agg_exprs)\n", "\n", "\n", "def join_and_upsert_silver(\n", "    bronze_df, balmo_df, prompt_df, bronze_primary_key, reference_tables, is_gas, silver_table\n", "):\n", "    # Standardize value column\n", "    bronze_df = bronze_df.withColumnRenamed(\"value\", \"value\")\n", "    \n", "    if prompt_df:\n", "        prompt_df = prompt_df.withColumnRenamed(\"prompt\", \"value\")\n", "        prompt_df = prompt_df.withColumnRenamed(\"ref_index_name\", \"index_name\")\n", "        prompt_df = prompt_df.select(\"value\", \"contract_year\", \"contract_month\", \"ingestion_timestamp\", \"report_date\", \"index_name\")\n", "\n", "    if balmo_df:\n", "        balmo_df = balmo_df.withColumnRenamed(\"balmo\", \"value\")\n", "        balmo_df = balmo_df.withColumnRenamed(\"ref_index_name\", \"index_name\")\n", "        balmo_df = balmo_df.select(\"value\", \"contract_year\", \"contract_month\", \"ingestion_timestamp\", \"report_date\", \"index_name\")\n", "    \n", "    # Filter out current/next month as before\n", "    if is_gas:\n", "        filtered_bronze = bronze_df.filter(\n", "            ~(\n", "                (F.month(\"contract_date\") == F.month(\"report_date\")) &\n", "                (F.year(\"contract_date\") == F.year(\"report_date\"))\n", "            )\n", "        )\n", "        final_df = filtered_bronze.unionByName(balmo_df, allowMissingColumns=True)\n", "    else:\n", "        filtered_bronze = bronze_df.filter(\n", "            ~(\n", "                ((F.month(\"contract_date\") == F.month(\"report_date\")) &\n", "                 (F.year(\"contract_date\") == F.year(\"report_date\")))\n", "                |\n", "                ((F.month(\"contract_date\") == F.col(\"next_month\")) &\n", "                 (F.year(\"contract_date\") == F.col(\"next_year\")))\n", "            )\n", "        )\n", "        final_df = (\n", "            filtered_bronze\n", "            .unionByName(balmo_df, allowMissingColumns=True)\n", "            .unionByName(prompt_df, allowMissingColumns=True)\n", "        )\n", "\n", "    # Reference table joins if needed\n", "    for reference_table, join_condition, join_type in reference_tables.values():\n", "        final_df = final_df.join(reference_table, join_condition, join_type)\n", "\n", "    final_df = dynamic_agg(final_df, [\"ref_index_name\", \"contract_date\", \"report_date\"], \"value\")\n", "\n", "    final_df = final_df.withColumn(\"primary_key\", F.sha2(F.concat_ws(\"||\", *bronze_primary_key), 256))\n", "    final_df = final_df.dropDuplicates(bronze_primary_key)\n", "\n", "    if not is_gas:\n", "        final_df = apply_power_reference_flags(final_df)\n", "\n", "    # # Upsert to delta table\n", "    upsert_to_delta_table(\n", "        spark,\n", "        final_df,\n", "        silver_table,\n", "        merge_keys=bronze_primary_key,\n", "        update_columns=final_df.columns,  # or specify only the columns you want to update\n", "        insert_columns=None\n", "    )\n", "    return final_df\n", "\n", "\n", "def update_watermark(pipeline_name, new_last_ts, watermark_table):\n", "    watermark_update = spark.createDataFrame(\n", "        [(pipeline_name, new_last_ts)],\n", "        [\"pipeline_name\", \"last_processed\"]\n", "    )\n", "    (watermark_update\n", "        .write\n", "        .mode(\"overwrite\")\n", "        .option(\"replaceWhere\", f\"pipeline_name = '{pipeline_name}'\")\n", "        .saveAsTable(watermark_table)\n", "    )"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "230bfcba-5ef0-45ec-a1e2-b5c705f799a8", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["def process_incremental_bronze_to_silver(\n", "    pipeline_name,\n", "    bronze_table,\n", "    bronze_primary_key,\n", "    bronze_ingestion_timestamp_column,\n", "    silver_table,\n", "    reference_tables,\n", "    descriptive_configs\n", "):\n", "    write_prompt = True if descriptive_configs.get('write_prompt') else False\n", "    write_balmo = True if descriptive_configs.get('write_balmo') else False\n", "    write_delta = True if descriptive_configs.get('write_delta') else False\n", "    \n", "    watermark_table = f\"{ipv_silver_catalog}.{ipv_silver_schema}.silver_watermark\"\n", "    last_ts = get_last_processed_ts(pipeline_name, watermark_table)\n", "    df_bronze = read_incremental_bronze(bronze_table, bronze_ingestion_timestamp_column, last_ts)\n", "    new_rows_count = df_bronze.count()\n", "    if new_rows_count == 0:\n", "        print(\"No new rows to process\")\n", "        return\n", "\n", "    bronze_df = unpivot_bronze(df_bronze, OL_DELTA_REPORT_KNOWN_COLS)\n", "    bronze_df = clean_and_format_dates(bronze_df)\n", "    bronze_df = add_aux_columns(bronze_df)\n", "\n", "    balmo_df = compute_balmo(bronze_df, descriptive_configs) if write_balmo else None\n", "    prompt_df = compute_prompt(bronze_df, descriptive_configs) if write_prompt else None\n", "\n", "    if write_balmo:\n", "        upsert_to_delta_table(\n", "            spark,\n", "            balmo_df,\n", "            descriptive_configs['balmo_table_name'],\n", "            merge_keys=[\"ice_code\", \"contract_year\", \"contract_month\"],\n", "            update_columns=[\"balmo\", \"ingestion_timestamp\"],\n", "            insert_columns=None\n", "        )\n", "\n", "    if write_prompt:\n", "        upsert_to_delta_table(\n", "            spark,\n", "            prompt_df,\n", "            descriptive_configs['prompt_table_name'],\n", "            merge_keys=[\"ice_code\", \"contract_year\", \"contract_month\"],\n", "            update_columns=[\"prompt\", \"ingestion_timestamp\"],\n", "            insert_columns=None\n", "        )\n", "\n", "    if write_delta:\n", "        join_and_upsert_silver(\n", "            bronze_df, balmo_df, prompt_df, bronze_primary_key, reference_tables,\n", "            descriptive_configs[\"is_gas\"], silver_table\n", "        )\n", "\n", "    # Update watermark\n", "    new_last_ts = df_bronze.agg(F.max(bronze_ingestion_timestamp_column).alias(\"max_ts\")).collect()[0][\"max_ts\"]\n", "    update_watermark(pipeline_name, new_last_ts, watermark_table)"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "93fe5d3a-4b1c-453b-bbb3-3c6e67256829", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["reference_gas = spark.read.table(f\"{ipv_bronze_catalog}.{ipv_bronze_schema}.{reference_gas_table_name}\")\n", "reference_power = spark.read.table(f\"{ipv_bronze_catalog}.{ipv_bronze_schema}.{reference_power_table_name}\")\n", "\n", "ol_df = reference_gas.select(\n", "    <PERSON>.col(\"region\"),\n", "    <PERSON>.col(\"location_hub\"),\n", "    <PERSON>.col(\"ice_code\"),\n", "    F.col(\"ol_code\").alias(\"ref_index_name\")\n", ")\n", "\n", "gd_df = reference_gas.select(\n", "    <PERSON>.col(\"region\"),\n", "    <PERSON>.col(\"location_hub\"),\n", "    <PERSON>.col(\"ice_code\"),\n", "    F.col(\"gd_code\").alias(\"ref_index_name\")\n", ")\n", "reference_gas_stacked_index_name = ol_df.unionByName(gd_df).filter(F.col(\"ref_index_name\").isNotNull())\n", "reference_gas_stacked_index_name = reference_gas_stacked_index_name.withColumn(\"ref_index_name\", F.lower(\"ref_index_name\"))\n", "\n", "reference_power_ol_code_to_ol_delta_index = spark.table(f\"{ipv_bronze_catalog}.{ipv_bronze_schema}.{REFERENCE_POWER_OL_CODE_TO_OL_DELTA_INDEX_TABLE_NAME}\")\n", "\n", "reference_power_stacked_index_name = reference_power.withColumnRenamed(\"ol_code\", \"ref_index_name\")\n", "reference_power_stacked_index_name = reference_power_stacked_index_name.withColumn(\"ref_index_name\", F.lower(\"ref_index_name\"))\n", "reference_power_stacked_index_name = (\n", "    reference_power_stacked_index_name\n", "    .join(\n", "        F.broadcast(\n", "            reference_power_ol_code_to_ol_delta_index\n", "            .withColumn(\"ol_code\", F.lower(\"ol_code\"))\n", "        ), \n", "        on=F.col(\"ref_index_name\") == F.col(\"ol_code\"),\n", "        how=\"inner\"\n", "    )\n", "    .drop(\"ol_code\")\n", ")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "c18c1ead-627c-4beb-99e9-74bec13e6784", "showTitle": false, "tableResultSettingsMap": {"0": {"dataGridStateBlob": "{\"version\":1,\"tableState\":{\"columnPinning\":{\"left\":[\"#row_number#\"],\"right\":[]},\"columnSizing\":{},\"columnVisibility\":{}},\"settings\":{\"columns\":{}},\"syncTimestamp\":1754914664041}", "filterBlob": "{\"version\":1,\"filterGroups\":[],\"syncTimestamp\":1754916081784}", "queryPlanFiltersBlob": "[]", "tableResultIndex": 0}, "1": {"dataGridStateBlob": "{\"version\":1,\"tableState\":{\"columnPinning\":{\"left\":[\"#row_number#\"],\"right\":[]},\"columnSizing\":{},\"columnVisibility\":{}},\"settings\":{\"columns\":{}},\"syncTimestamp\":1754916035105}", "filterBlob": "{\"version\":1,\"filterGroups\":[{\"enabled\":true,\"op\":\"OR\",\"filterGroupId\":\"fg_6d96f4cd\",\"filters\":[{\"filterId\":\"f_81abbaf5\",\"columnId\":\"ice_code\",\"enabled\":true,\"dataType\":\"string\",\"filterType\":\"eq\",\"filterValue\":\"PMD\",\"filterConfig\":{\"caseSensitive\":false,\"includeMax\":true,\"includeMin\":true}}],\"local\":false,\"updatedAt\":1754918028687},{\"enabled\":true,\"op\":\"OR\",\"filterGroupId\":\"fg_18f604c1\",\"filters\":[{\"filterId\":\"f_bfe1904e\",\"columnId\":\"contract_month\",\"enabled\":true,\"dataType\":\"integer\",\"filterType\":\"eq\",\"filterValue\":8,\"filterValues\":[],\"filterConfig\":{\"caseSensitive\":false,\"includeMax\":true,\"includeMin\":true}}],\"local\":false,\"updatedAt\":1754918028687}],\"syncTimestamp\":1754918028695}", "queryPlanFiltersBlob": "[{\"kind\":\"call\",\"function\":\"and\",\"args\":[{\"kind\":\"call\",\"function\":\"or\",\"args\":[{\"kind\":\"call\",\"function\":\"ilike\",\"args\":[{\"kind\":\"identifier\",\"identifier\":\"ice_code\"},{\"kind\":\"literal\",\"value\":\"PMD\",\"type\":\"string\"}]}]},{\"kind\":\"call\",\"function\":\"or\",\"args\":[{\"kind\":\"call\",\"function\":\"in\",\"args\":[{\"kind\":\"identifier\",\"identifier\":\"contract_month\"},{\"kind\":\"literal\",\"value\":8,\"type\":\"integer\"}]}]}]}]", "tableResultIndex": 1}}, "title": ""}}, "outputs": [], "source": ["silver_pipeline_steps = [\n", "    (\n", "        \"silver_ol_delta_report_power\",\n", "        f\"{ipv_bronze_catalog}.{ipv_bronze_schema}.bronze_ol_delta_report_power\",\n", "        OL_DELTA_REPORT_POWER_PK,\n", "        BRONZE_OL_DELTA_REPORT_POWER_INGESTION_TIMESTAMP_COLUMN,\n", "        f\"{ipv_silver_catalog}.{ipv_silver_schema}.silver_ol_delta_report_power\",\n", "        {\n", "            'reference_power': (\n", "                F.broadcast(reference_power_stacked_index_name).withColumnRenamed('ingestion_timestamp', 'reference_power_ingestion_timestamp'), \n", "                F.col('index_name') == F.col('ol_index'), \n", "                'inner'\n", "            ),\n", "        },\n", "        {\n", "            'is_gas': <PERSON>als<PERSON>,\n", "            'write_balmo': True,\n", "            'write_prompt': True,\n", "            'write_delta': True,\n", "            'balmo_table_name': f\"{ipv_silver_catalog}.{ipv_silver_schema}.silver_ol_delta_report_balmo_power\",\n", "            'prompt_table_name': f\"{ipv_silver_catalog}.{ipv_silver_schema}.silver_ol_delta_report_prompt_power\",\n", "            'balmo_join_table_params': (\n", "                F.broadcast(reference_power_stacked_index_name).withColumnRenamed('ingestion_timestamp', 'reference_gas_ingestion_timestamp'),\n", "                F.col('index_name') == F.col('ol_index'),\n", "                'inner'\n", "            ),\n", "            'prompt_join_table_params': (\n", "                F.broadcast(reference_power_stacked_index_name).withColumnRenamed('ingestion_timestamp', 'reference_gas_ingestion_timestamp'),\n", "                F.col('index_name') == F.col('ol_index'),\n", "                'inner'\n", "            )\n", "        }\n", "    ),\n", "    (\n", "        \"silver_ol_delta_report_gas\",\n", "        f\"{ipv_bronze_catalog}.{ipv_bronze_schema}.bronze_ol_delta_report_gas\",\n", "        OL_DELTA_REPORT_GAS_PK,\n", "        BRONZE_OL_DELTA_REPORT_GAS_INGESTION_TIMESTAMP_COLUMN,\n", "        f\"{ipv_silver_catalog}.{ipv_silver_schema}.silver_ol_delta_report_gas\",\n", "        {\n", "            'reference_gas': (\n", "                F.broadcast(reference_gas_stacked_index_name).withColumnRenamed('ingestion_timestamp', 'reference_gas_ingestion_timestamp'), \n", "                F.col('index_name') == F.col('ref_index_name'),\n", "                'inner'\n", "            ),\n", "        },\n", "        {\n", "            'is_gas': True,\n", "            'write_balmo': True,\n", "            'write_prompt': <PERSON><PERSON><PERSON>,\n", "            'write_delta': True,\n", "            'balmo_table_name': f\"{ipv_silver_catalog}.{ipv_silver_schema}.silver_ol_delta_report_balmo_gas\",\n", "            'balmo_join_table_params': (\n", "                F.broadcast(reference_gas_stacked_index_name).withColumnRenamed('ingestion_timestamp', 'reference_gas_ingestion_timestamp'),\n", "                F.col('index_name') == F.col('ref_index_name'),\n", "                'inner'\n", "            )\n", "        }\n", "    ),\n", "]\n", "\n", "for pipeline_name, bronze_table, bronze_pk, bronze_ingestion_time_column_name, silver_table, join_tables, descriptive_configs  in silver_pipeline_steps:\n", "    process_incremental_bronze_to_silver(\n", "        pipeline_name,\n", "        bronze_table,\n", "        bronze_pk,\n", "        bronze_ingestion_time_column_name,\n", "        silver_table,\n", "        join_tables,\n", "        descriptive_configs\n", "    )"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "f688c35d-ab9b-4b25-84d9-4878015e8b9c", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["# Clean-up commands if needed for dev and debugging"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "d897943a-6cc8-4827-b3bb-44834661414c", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# names_tb_deleted = ['silver_ol_delta_report_power', 'silver_ol_delta_report_gas', \n", "#                     'silver_ol_delta_report_prompt_power', 'silver_ol_delta_report_balmo_power',\n", "#                     'silver_ol_delta_report_prompt_gas', 'silver_ol_delta_report_balmo_gas']\n", "# names_str = ','.join([f\"'{n}'\" for n in names_tb_deleted])\n", "# spark.sql(f\"DELETE FROM {ipv_silver_catalog}.{ipv_silver_schema}.silver_watermark WHERE pipeline_name IN ({names_str})\")\n", "\n", "# tables = spark.sql(\"show tables in mbcl_dev_silver.ipv\").collect()\n", "# tables = [t for t in tables if t.tableName in names_tb_deleted]\n", "\n", "# for t in tables:\n", "#     table_name = t.tableName\n", "#     print(f\"Dropping table {table_name}\")\n", "#     spark.sql(f\"DROP TABLE IF EXISTS mbcl_dev_silver.ipv.{table_name}\")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "866eddcf-6657-4d5e-94bd-0800a77ae516", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": []}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "inputWidgetPreferences": null, "language": "python", "notebookMetadata": {"pythonIndentUnit": 4}, "notebookName": "ipv_ol_delta_bronze_to_silver", "widgets": {"ipv_bronze_catalog": {"currentValue": "mbcl_dev_bronze", "nuid": "e4b4047c-e09f-4aa4-9ce2-147dca5935ca", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "mbcl_bronze", "label": "ipv_bronze_catalog", "name": "ipv_bronze_catalog", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "mbcl_bronze", "label": "ipv_bronze_catalog", "name": "ipv_bronze_catalog", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}, "ipv_bronze_schema": {"currentValue": "ipv", "nuid": "5a2e5aad-8722-4c61-9902-7db470f3f862", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "mbcl_ipv", "label": "ipv_bronze_schema", "name": "ipv_bronze_schema", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "mbcl_ipv", "label": "ipv_bronze_schema", "name": "ipv_bronze_schema", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}, "ipv_silver_catalog": {"currentValue": "mbcl_dev_silver", "nuid": "34d8a44a-9490-46aa-abb7-7eaf66da159e", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "mbcl_silver", "label": "ipv_silver_catalog", "name": "ipv_silver_catalog", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "mbcl_silver", "label": "ipv_silver_catalog", "name": "ipv_silver_catalog", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}, "ipv_silver_schema": {"currentValue": "ipv", "nuid": "8a0cb2fc-cd42-456b-a572-a04b4ead2163", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "mbcl_ipv", "label": "ipv_silver_schema", "name": "ipv_silver_schema", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "mbcl_ipv", "label": "ipv_silver_schema", "name": "ipv_silver_schema", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}, "reference_gas_table": {"currentValue": "bronze_reference_gas", "nuid": "700ca7b2-6bb4-413b-a3d6-737853deb2d1", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "bronze_reference_gas", "label": "reference_gas_table", "name": "reference_gas_table", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "bronze_reference_gas", "label": "reference_gas_table", "name": "reference_gas_table", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}, "reference_months_strips_table": {"currentValue": "bronze_reference_months_strips", "nuid": "fd3c8874-d70d-4a54-bbd6-afaf857472bc", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "bronze_reference_months_strips", "label": "reference_months_strips_table", "name": "reference_months_strips_table", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "bronze_reference_months_strips", "label": "reference_months_strips_table", "name": "reference_months_strips_table", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}, "reference_power_table": {"currentValue": "bronze_reference_power", "nuid": "b3ea5ad4-d80c-48bc-97c0-49966e182165", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "bronze_reference_power", "label": "reference_power_table", "name": "reference_power_table", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "bronze_reference_power", "label": "reference_power_table", "name": "reference_power_table", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}