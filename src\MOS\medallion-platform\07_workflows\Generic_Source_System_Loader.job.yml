resources:
  jobs:
    Generic_Source_System_Loader:
      name: "{{job.parameters.source_system}}_{{job.parameters.environment}}_Loader"
      description: "Generic workflow for loading any source system into medallion architecture"
      email_notifications:
        on_success:
          - <EMAIL>
        on_failure:
          - <EMAIL>
      max_concurrent_runs: 1
      parameters:
        - name: environment
          default: dev
        - name: source_system
          default: MOS
        - name: run_mode
          default: full_pipeline
        - name: catalog_name
          default: ""
        - name: parallel_execution
          default: "false"
        - name: max_parallel_jobs
          default: "3"
      tasks:
        - task_key: Bronze_Full_Load
          notebook_task:
            notebook_path: /Workspace/Shared/medallion-platform/01_bronze_layer/oracle_full_load
            base_parameters:
              environment: "{{job.parameters.environment}}"
              source_system: "{{job.parameters.source_system}}"
              run_id: "{{job.run_id}}"
              catalog_name: "{{job.parameters.catalog_name}}"
            source: WORKSPACE
          timeout_seconds: 7200
          
        - task_key: Bronze_Incremental_Load
          depends_on:
            - task_key: Bronze_Full_Load
          condition_task:
            op: NOT_EQUAL_TO
            left: "{{job.parameters.run_mode}}"
            right: "bronze_only"

        - task_key: Bronze_Incremental_Execute
          depends_on:
            - task_key: Bronze_Incremental_Load
              outcome: "true"
          notebook_task:
            notebook_path: /Workspace/Shared/medallion-platform/01_bronze_layer/oracle_incremental_load
            base_parameters:
              environment: "{{job.parameters.environment}}"
              source_system: "{{job.parameters.source_system}}"
              run_id: "{{job.run_id}}"
              catalog_name: "{{job.parameters.catalog_name}}"
            source: WORKSPACE
          timeout_seconds: 3600
            
        - task_key: Silver_Transformation
          depends_on:
            - task_key: Bronze_Full_Load
            - task_key: Bronze_Incremental_Load
              outcome: "true"
          notebook_task:
            notebook_path: /Workspace/Shared/medallion-platform/02_silver_layer/silver_orchestrator
            base_parameters:
              environment: "{{job.parameters.environment}}"
              source_system: "{{job.parameters.source_system}}"
              run_id: "{{job.run_id}}"
              catalog_name: "{{job.parameters.catalog_name}}"
            source: WORKSPACE
          timeout_seconds: 5400
          condition_task:
            op: NOT_EQUAL_TO
            left: "{{job.parameters.run_mode}}"
            right: "bronze_only"
            
        - task_key: Data_Quality_Check
          depends_on:
            - task_key: Silver_Transformation
          notebook_task:
            notebook_path: /Workspace/Shared/medallion-platform/06_utilities/data_quality_check
            base_parameters:
              environment: "{{job.parameters.environment}}"
              source_system: "{{job.parameters.source_system}}"
              run_id: "{{job.run_id}}"
              catalog_name: "{{job.parameters.catalog_name}}"
            source: WORKSPACE
          timeout_seconds: 1800
          condition_task:
            op: EQUAL_TO
            left: "{{job.parameters.run_mode}}"
            right: "full_pipeline"
            
        - task_key: Final_Verification
          depends_on:
            - task_key: Data_Quality_Check
          notebook_task:
            notebook_path: /Workspace/Shared/medallion-platform/06_utilities/final_verification
            base_parameters:
              environment: "{{job.parameters.environment}}"
              source_system: "{{job.parameters.source_system}}"
              run_id: "{{job.run_id}}"
              catalog_name: "{{job.parameters.catalog_name}}"
            source: WORKSPACE
          timeout_seconds: 900
          condition_task:
            op: EQUAL_TO
            left: "{{job.parameters.run_mode}}"
            right: "full_pipeline"
            
        - task_key: Main_Pipeline_Orchestrator
          notebook_task:
            notebook_path: /Workspace/Shared/medallion-platform/04_orchestration/main_pipeline_orchestrator
            base_parameters:
              environment: "{{job.parameters.environment}}"
              source_system: "{{job.parameters.source_system}}"
              run_id: "{{job.run_id}}"
              run_mode: "{{job.parameters.run_mode}}"
              parallel_execution: "{{job.parameters.parallel_execution}}"
              max_parallel_jobs: "{{job.parameters.max_parallel_jobs}}"
              catalog_name: "{{job.parameters.catalog_name}}"
            source: WORKSPACE
          timeout_seconds: 14400
          condition_task:
            op: EQUAL_TO
            left: "{{job.parameters.run_mode}}"
            right: "orchestrated"
            
      tags:
        Environment: "{{job.parameters.environment}}"
        SourceSystem: "{{job.parameters.source_system}}"
        Version: v2.0
        Type: Generic
      queue:
        enabled: true
