# Databricks notebook source
# MAGIC %md
# MAGIC # Create Metadata Tables
# MAGIC This notebook creates all metadata tables for the medallion architecture using the new configuration system

# COMMAND ----------

# MAGIC %run ../06_utilities/config_manager

# COMMAND ----------

# Create widgets for workflow parameter passing
dbutils.widgets.text("environment", "dev", "Environment (dev/test/prod)")
dbutils.widgets.text("source_system", "MOS", "Source System (MOS/XVA)")
dbutils.widgets.text("catalog_name", "", "Catalog Name (optional - will use environment default if empty)")

# Get parameters
environment = dbutils.widgets.get("environment")
source_system = dbutils.widgets.get("source_system")
catalog_name_param = dbutils.widgets.get("catalog_name")

print(f"🚀 Starting metadata tables creation...")
print(f"Environment: {environment}")
print(f"Source System: {source_system}")
print(f"Catalog Override: {catalog_name_param or 'None (using environment default)'}")
print("="*60)

# COMMAND ----------

# Initialize configuration manager
config_manager = create_config_manager(environment, source_system, catalog_name_param)
runtime_config = config_manager.get_complete_runtime_config()

# Use the utility catalog and metadata schema
utility_catalog = runtime_config['databricks']['catalogs']['utility']
metadata_schema = runtime_config['databricks']['schemas']['metadata']

print(f"✅ Configuration loaded:")
print(f"   Utility Catalog: {utility_catalog}")
print(f"   Metadata Schema: {metadata_schema}")
print(f"   Environment: {environment}")
print(f"   Source System: {source_system}")

# COMMAND ----------

# Create schemas if they don't exist
print("Creating schemas...")
success = config_manager.create_schemas_if_not_exist()
if not success:
    raise Exception("Failed to create schemas")

spark.sql(f"""USE CATALOG `{utility_catalog}`""")
spark.sql(f"USE SCHEMA `{metadata_schema}`")

print(f"✅ Using utility catalog: {utility_catalog}")
print(f"✅ Using metadata schema: {metadata_schema}")

# COMMAND ----------

# Show existing tables
print("Checking existing metadata tables...")
try:
    existing_tables = spark.sql(f"SHOW TABLES IN `{utility_catalog}`.`{metadata_schema}`").collect()
    print(f"Existing tables in {utility_catalog}.{metadata_schema}:")
    for table in existing_tables:
        print(f"  - {table.tableName}")
except:
    print(f"Schema {utility_catalog}.{metadata_schema} is empty or doesn't exist yet")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Create Core Metadata Tables

# COMMAND ----------

# Create pipeline configuration table
print("Creating pipeline_config table...")
spark.sql(f"""
    CREATE TABLE IF NOT EXISTS `{utility_catalog}`.`{metadata_schema}`.pipeline_config (
        pipeline_id STRING,
        source_system STRING,
        source_table STRING,
        target_table STRING,
        oracle_schema STRING,
        ingestion_type STRING,
        watermark_column STRING,
        partition_columns ARRAY<STRING>,
        is_active BOOLEAN,
        created_at TIMESTAMP,
        updated_at TIMESTAMP
    ) USING DELTA
    COMMENT 'Pipeline configuration for bronze layer ingestion'
""")
print("✅ pipeline_config table created")

# COMMAND ----------

# Create pipeline runs log table
print("Creating pipeline_runs table...")
spark.sql(f"""
    CREATE TABLE IF NOT EXISTS `{utility_catalog}`.`{metadata_schema}`.pipeline_runs (
        run_id STRING,
        pipeline_id STRING,
        source_table STRING,
        ingestion_date DATE,
        start_time TIMESTAMP,
        end_time TIMESTAMP,
        status STRING,
        records_read BIGINT,
        records_written BIGINT,
        error_message STRING,
        watermark_value STRING,
        duration_seconds DOUBLE
    ) USING DELTA
    PARTITIONED BY (ingestion_date)
    COMMENT 'Audit log for pipeline execution runs'
""")
print("✅ pipeline_runs table created")

# COMMAND ----------

# Create data quality rules table
print("Creating data_quality_rules table...")
spark.sql(f"""
    CREATE TABLE IF NOT EXISTS `{utility_catalog}`.`{metadata_schema}`.data_quality_rules (
        rule_id STRING,
        source_system STRING,
        table_name STRING,
        column_name STRING,
        rule_type STRING,
        rule_expression STRING,
        error_threshold DOUBLE,
        is_active BOOLEAN,
        created_at TIMESTAMP,
        updated_at TIMESTAMP
    ) USING DELTA
    COMMENT 'Data quality validation rules'
""")
print("✅ data_quality_rules table created")

# COMMAND ----------

# MAGIC %sql
# MAGIC -- Data quality results
# MAGIC CREATE TABLE IF NOT EXISTS data_quality_results (
# MAGIC     run_id STRING,
# MAGIC     rule_id STRING,
# MAGIC     table_name STRING,
# MAGIC     check_time TIMESTAMP,
# MAGIC     records_checked BIGINT,
# MAGIC     records_failed BIGINT,
# MAGIC     failure_percentage DOUBLE,
# MAGIC     status STRING,
# MAGIC     details STRING
# MAGIC ) USING DELTA;

# COMMAND ----------

# Insert pipeline configurations

# Get Oracle schema from environment configuration
oracle_config = config_manager.env_config_manager.get_oracle_config(environment, source_system)
oracle_schema = oracle_config.get("schema", "mosprod")

pipeline_configs = [
    # For each table, format: (source, source_table, target_table, oracle_schema, ingestion_type, watermark_column, partition_columns, is_active)
    ("Lakehouse", "MOS", "VW_NEW_DER", "VW_NEW_DER", oracle_schema, "full", "REPORT_DATE", ["reportDate", "entityId"], True),
    ("Lakehouse", "MOS", "VW_NEW_DER_CSA", "VW_NEW_DER_CSA", oracle_schema, "full", "REPORT_DATE", ["reportDate", "entityId"], True),
    ("Lakehouse", "MOS", "VW_NEW_DER_TMA", "VW_NEW_DER_TMA", oracle_schema, "full", "REPORT_DATE", ["reportDate", "entityId"], True),
    ("Lakehouse", "MOS", "VW_NEW_DER_NONCSA", "VW_NEW_DER_NONCSA", oracle_schema, "full", "REPORT_DATE", ["reportDate", "entityId"], True),
    ("Lakehouse", "MOS", "VW_SUSPENDED_COUNTERPARTIES", "VW_SUSPENDED_COUNTERPARTIES", oracle_schema, "full", "SNAPSHOT_DATE", ["snapshotDate", "entityId"], True),
    ("Lakehouse", "MOS", "VW_ACCOUNT_RECEIVABLE_BY_CLIENT", "VW_ACCOUNT_RECEIVABLE_BY_CLIENT", oracle_schema, "full", "RUN_DATE", ["runDate", "entityId"], True),
    ("Lakehouse", "MOS", "VW_ACCOUNT_RECEIVABLE_DETAILS", "VW_ACCOUNT_RECEIVABLE_DETAILS", oracle_schema, "full", "RUN_DATE", None, True)
]

from pyspark.sql import Row
from datetime import datetime

configs_df = spark.createDataFrame([
    Row(
        pipeline_id=f"data_{cfg[0]}",
        source_system=cfg[1],
        source_table=cfg[2],
        target_table=cfg[3],
        oracle_schema=cfg[4],
        ingestion_type=cfg[5],
        watermark_column=cfg[6],
        partition_columns=cfg[7],
        is_active=cfg[8],
        created_at=datetime.now(),
        updated_at=datetime.now()
    ) for cfg in pipeline_configs
])

configs_df.write.mode("overwrite").saveAsTable(f"`{utility_catalog}`.`{metadata_schema}`.pipeline_config")
print("✅ Pipeline configurations loaded")

# COMMAND ----------

# Insert data quality rules
#dq_rules = [
#    ("null_check_customer_email", "silver.oracle_customers", "email", "null_check", #"email IS NOT NULL", 0.0),
#    ("null_check_customer_name", "silver.oracle_customers", "customerName", #"null_check", "customerName IS NOT NULL", 0.0),
#    ("range_check_order_amount", "silver.oracle_orders", "totalAmount", #"range_check", "totalAmount > 0", 0.0),
#    ("duplicate_check_customers", "silver.oracle_customers", "customerId", #"duplicate_check", "COUNT(1) = 1", 0.0)
#]

#dq_rules_df = spark.createDataFrame([
#    Row(
#        rule_id=rule[0],
#        table_name=rule[1],
#        column_name=rule[2],
#        rule_type=rule[3],
#        rule_expression=rule[4],
#        error_threshold=rule[5],
#        is_active=True,
#        created_at=datetime.now()
#    ) for rule in dq_rules
#])

#dq_rules_df.write.mode("overwrite").saveAsTable("metadata.data_quality_rules")
#print("✅ Data quality rules loaded")

# COMMAND ----------

print("✅ All metadata tables created successfully!")
dbutils.notebook.exit("SUCCESS")

# COMMAND ----------


