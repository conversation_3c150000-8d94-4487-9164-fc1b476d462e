# Databricks notebook source
# MAGIC %md
# MAGIC # Data Quality Check
# MAGIC Comprehensive data quality validation for medallion platform

# COMMAND ----------

# MAGIC %md
# MAGIC ## Setup Widgets for Parameter Passing

# COMMAND ----------

# Create widgets for workflow parameter passing
dbutils.widgets.text("environment", "dev", "Environment (dev/test/prod)")
dbutils.widgets.text("source_system", "MOS", "Source System")
dbutils.widgets.text("run_id", "", "Optional Run ID")
dbutils.widgets.text("catalog_name", "", "Catalog Name Override (optional)")

# Get parameters
environment = dbutils.widgets.get("environment")
source_system = dbutils.widgets.get("source_system")
run_id = dbutils.widgets.get("run_id") or f"data_quality_check_{source_system}_{environment}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
catalog_override = dbutils.widgets.get("catalog_name")

print(f"🚀 Starting Data Quality Check")
print(f"Environment: {environment}")
print(f"Source System: {source_system}")
print(f"Run ID: {run_id}")
print("="*60)

# COMMAND ----------

# MAGIC %md
# MAGIC ## Load Configuration Framework

# COMMAND ----------

# MAGIC %run ../06_utilities/config_manager

# COMMAND ----------

# MAGIC %run ../02_silver_layer/silver_transformation_framework

# COMMAND ----------

import json
from datetime import datetime
from pyspark.sql.functions import col, max as spark_max, sum as spark_sum

# COMMAND ----------

# MAGIC %md
# MAGIC ## Initialize Configuration

# COMMAND ----------

# Create configuration manager
config_manager = create_config_manager(environment, source_system, catalog_override)

# Validate configuration
if not config_manager.env_config_manager.validate_configuration():
    print("❌ Configuration validation failed!")
    dbutils.notebook.exit(json.dumps({"status": "config_error", "message": "Configuration validation failed"}))

# Get complete runtime configuration
runtime_config = config_manager.get_complete_runtime_config()

# Extract configuration values
silver_catalog = runtime_config['databricks']['catalogs']['silver']
utility_catalog = runtime_config['databricks']['catalogs']['utility']
silver_schema = runtime_config['databricks']['schemas']['silver']
metadata_schema = runtime_config['databricks']['schemas']['metadata']

print(f"✅ Configuration loaded successfully!")
print(f"Silver Catalog: {silver_catalog}")
print(f"Utility Catalog: {utility_catalog}")
print(f"Silver Schema: {silver_schema}")
print(f"Metadata Schema: {metadata_schema}")
print(f"Environment: {environment}")
print(f"Source System: {source_system}")
print(f"Run ID: {run_id}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Initialize Data Quality Framework

# COMMAND ----------

# Initialize data quality framework
dq_framework = DataQualityFramework(spark, utility_catalog, environment)

print("✅ Data Quality Framework initialized")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Get Tables for Quality Checks

# COMMAND ----------

# Get tables to check from pipeline configuration
try:
    tables_to_check = spark.sql(f"""
        SELECT DISTINCT
            source_table,
            target_table,
            source_system
        FROM `{utility_catalog}`.`{metadata_schema}`.pipeline_config
        WHERE source_system = '{source_system}'
        AND is_active = true
    """).collect()
    
    print(f"Found {len(tables_to_check)} tables to check for {source_system}")

except Exception as e:
    print(f"⚠️ Could not load pipeline config: {str(e)}")
    print("📋 Using actual silver tables instead...")

    # Get actual tables from silver schema
    try:
        actual_tables = spark.sql(f"SHOW TABLES IN `{silver_catalog}`.`{silver_schema}`").collect()
        tables_to_check = []
        for table in actual_tables:
            table_name = table.tableName
            if not table_name.startswith('_'):  # Skip system tables
                tables_to_check.append(type('obj', (object,), {
                    'source_table': table_name,
                    'target_table': table_name,
                    'source_system': source_system
                })())
        print(f"Found {len(tables_to_check)} actual silver tables to check")
    except Exception as table_error:
        print(f"⚠️ Could not list silver tables: {str(table_error)}")
        # Fallback to default tables
        tables_to_check = [
            type('obj', (object,), {
                'source_table': 'vw_new_der',
                'target_table': 'vw_new_der',
                'source_system': source_system
            })(),
            type('obj', (object,), {
                'source_table': 'vw_account_receivable_by_client',
                'target_table': 'vw_account_receivable_by_client',
                'source_system': source_system
            })()
        ]

# COMMAND ----------

# MAGIC %md
# MAGIC ## Execute Data Quality Checks

# COMMAND ----------

quality_results = []
overall_start_time = datetime.now()

for table_config in tables_to_check:
    table_name = table_config.target_table.lower()  # Convert to lowercase to match actual table names
    print(f"\n{'='*60}")
    print(f"Checking Quality: {table_name}")
    print(f"{'='*60}")

    try:
        # Check if table exists in silver layer
        silver_table_path = f"`{silver_catalog}`.`{silver_schema}`.`{table_name}`"
        
        try:
            # First check if table exists
            try:
                spark.sql(f"DESCRIBE TABLE {silver_table_path}")
            except:
                print(f"⚠️ Table {silver_table_path} does not exist, skipping")
                quality_results.append({
                    "table_name": table_name,
                    "status": "skipped",
                    "reason": "table_not_found",
                    "record_count": 0
                })
                continue

            df = spark.table(silver_table_path)
            record_count = df.count()
            
            if record_count == 0:
                print(f"⚠️ Table {silver_table_path} is empty, skipping quality checks")
                quality_results.append({
                    "table_name": table_name,
                    "status": "skipped",
                    "reason": "empty_table",
                    "record_count": 0
                })
                continue
                
            print(f"📊 Analyzing {record_count} records in {silver_table_path}")
            
            # Run minimal quality checks
            null_count = 0
            for col_name in df.columns:
                if isinstance(col_name, str) and not col_name.startswith('_'):  # Skip audit columns
                    try:
                        null_count += df.filter(col(col_name).isNull()).count()
                    except Exception as col_error:
                        print(f"⚠️ Could not check column {col_name}: {str(col_error)}")
                        continue

            # Calculate basic quality score (use Python's max, not PySpark's)
            import builtins
            quality_score = builtins.max(0, 100 - (null_count / record_count * 100)) if record_count > 0 else 100

            if quality_score >= 95:
                quality_level = "Excellent"
            elif quality_score >= 85:
                quality_level = "Good"
            elif quality_score >= 70:
                quality_level = "Fair"
            else:
                quality_level = "Poor"

            quality_results.append({
                "table_name": table_name,
                "status": "completed",
                "record_count": record_count,
                "quality_score": quality_score,
                "quality_level": quality_level,
                "null_count": null_count
            })

            print(f"✅ Quality Score: {quality_score:.1f}% ({quality_level})")
            print(f"📋 Null Values: {null_count}")
            print(f"📊 Records: {record_count}")
            
        except Exception as table_error:
            print(f"❌ Could not access table {silver_table_path}: {str(table_error)}")
            quality_results.append({
                "table_name": table_name,
                "status": "error",
                "error": str(table_error)
            })
            
    except Exception as e:
        print(f"❌ Error checking {table_name}: {str(e)}")
        quality_results.append({
            "table_name": table_name,
            "status": "error", 
            "error": str(e)
        })

# COMMAND ----------

# MAGIC %md
# MAGIC ## Generate Quality Report

# COMMAND ----------

overall_end_time = datetime.now()
total_duration = (overall_end_time - overall_start_time).total_seconds()

# Calculate summary statistics
completed_checks = [r for r in quality_results if r["status"] == "completed"]
failed_checks = [r for r in quality_results if r["status"] == "error"]
skipped_checks = [r for r in quality_results if r["status"] == "skipped"]

print(f"\n📊 DATA QUALITY CHECK SUMMARY")
print(f"{'='*80}")
print(f"Environment: {environment}")
print(f"Source System: {source_system}")
print(f"Run ID: {run_id}")
print(f"Total Duration: {total_duration:.2f} seconds")
print(f"Total Tables: {len(quality_results)}")
print(f"Completed: {len(completed_checks)}")
print(f"Failed: {len(failed_checks)}")
print(f"Skipped: {len(skipped_checks)}")

if completed_checks:
    try:
        # Calculate summary statistics safely
        quality_scores = [float(r.get("quality_score", 0)) for r in completed_checks if r.get("quality_score") is not None]
        null_counts = [int(r.get("null_count", 0)) for r in completed_checks if r.get("null_count") is not None]
        record_counts = [int(r.get("record_count", 0)) for r in completed_checks if r.get("record_count") is not None]

        if quality_scores:
            avg_quality_score = sum(quality_scores) / len(quality_scores)
            print(f"Average Quality Score: {avg_quality_score:.1f}%")

        if record_counts:
            total_records = sum(record_counts)
            print(f"Total Records Analyzed: {total_records:,}")

        if null_counts:
            total_nulls = sum(null_counts)
            print(f"Total Null Values: {total_nulls:,}")

        # Quality level distribution
        quality_levels = {}
        for result in completed_checks:
            level = result.get("quality_level", "unknown")
            if level:
                quality_levels[level] = quality_levels.get(level, 0) + 1

        if quality_levels:
            print(f"Quality Level Distribution:")
            for level, count in quality_levels.items():
                print(f"  - {level}: {count} tables")

    except Exception as summary_error:
        print(f"⚠️ Error calculating summary statistics: {str(summary_error)}")
        print("Raw completed checks for debugging:")
        for i, check in enumerate(completed_checks):
            print(f"  {i+1}. {check}")
else:
    print("No completed checks to summarize")

print(f"{'='*80}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Display Detailed Results

# COMMAND ----------

# Display detailed results
if quality_results:
    try:
        from pyspark.sql import Row

        results_for_df = []
        for result in quality_results:
            # Ensure all values are proper types
            table_name = str(result.get("table_name", "unknown"))
            status = str(result.get("status", "unknown"))
            record_count = int(result.get("record_count", 0)) if result.get("record_count") is not None else 0
            quality_score = float(result.get("quality_score", 0)) if result.get("quality_score") is not None else 0.0
            quality_level = str(result.get("quality_level", "unknown"))
            null_count = int(result.get("null_count", 0)) if result.get("null_count") is not None else 0
            error = str(result.get("error", ""))[:200]  # Truncate long errors

            results_for_df.append(Row(
                table_name=table_name,
                status=status,
                record_count=record_count,
                quality_score=quality_score,
                quality_level=quality_level,
                null_count=null_count,
                error=error
            ))

        results_df = spark.createDataFrame(results_for_df)
        display(results_df)

    except Exception as df_error:
        print(f"⚠️ Error creating results DataFrame: {str(df_error)}")
        print("Displaying results as text instead:")
        for i, result in enumerate(quality_results):
            print(f"{i+1}. {result.get('table_name', 'unknown')}: {result.get('status', 'unknown')} - {result.get('quality_score', 0):.1f}%")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Log Results and Exit

# COMMAND ----------

# Log data quality check execution
config_manager.log_pipeline_execution(
    f"data_quality_check_{source_system}",
    "success" if len(failed_checks) == 0 else "failed",
    run_id=run_id,
    source_table="data_quality_check",
    start_time=overall_start_time,
    end_time=overall_end_time,
    records_read=len(quality_results),
    records_written=len(completed_checks),
    error_message=f"Failed checks: {[r['table_name'] for r in failed_checks]}" if failed_checks else "",
    duration_seconds=total_duration
)

# Prepare exit result
exit_result = {
    "status": "success" if len(failed_checks) == 0 else "failed",
    "environment": environment,
    "source_system": source_system,
    "run_id": run_id,
    "summary": {
        "total_tables": len(quality_results),
        "completed": len(completed_checks),
        "failed": len(failed_checks),
        "skipped": len(skipped_checks),
        "duration_seconds": total_duration
    },
    "quality_results": quality_results
}

if completed_checks:
    try:
        # Calculate summary statistics safely for exit result
        quality_scores = [float(r.get("quality_score", 0)) for r in completed_checks if r.get("quality_score") is not None]
        null_counts = [int(r.get("null_count", 0)) for r in completed_checks if r.get("null_count") is not None]
        record_counts = [int(r.get("record_count", 0)) for r in completed_checks if r.get("record_count") is not None]

        if quality_scores:
            exit_result["summary"]["average_quality_score"] = sum(quality_scores) / len(quality_scores)
        if null_counts:
            exit_result["summary"]["total_nulls"] = sum(null_counts)
        if record_counts:
            exit_result["summary"]["total_records"] = sum(record_counts)
    except Exception as exit_calc_error:
        print(f"⚠️ Error calculating exit summary: {str(exit_calc_error)}")
        # Set default values
        exit_result["summary"]["average_quality_score"] = 0.0
        exit_result["summary"]["total_nulls"] = 0
        exit_result["summary"]["total_records"] = 0

if failed_checks:
    exit_result["failed_tables"] = [r["table_name"] for r in failed_checks]
    print(f"\n❌ Data quality check completed with {len(failed_checks)} failures")
else:
    print(f"\n✅ Data quality check completed successfully!")

print(f"Exiting with status: {exit_result['status']}")
dbutils.notebook.exit(json.dumps(exit_result))

# COMMAND ----------
