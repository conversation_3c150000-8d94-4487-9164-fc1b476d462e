{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "4e4f4518-6cef-442d-a785-0d4c9275effa", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Query the scope of the current environment\n", "workspace_name = spark.conf.get(\"spark.databricks.workspaceUrl\")\n", "\n", "# Query the scope of Azure Key Vault and secrets\n", "key_vault_scope = \"akv_dataplatform\"\n", "\n", "# dbutils.secrets.get(scope=\"akv_dataplatform\", key=\"test\")\n", "oracle_username = dbutils.secrets.get(scope=\"akv_dataplatform\", key=\"oracle-mos-username\")\n", "oracle_password = dbutils.secrets.get(scope=\"akv_dataplatform\", key=\"oracle-mos-password\")\n"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "02248b6c-e649-4a60-b7b2-77376134f2f2", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# --- Widgets ---\n", "dbutils.widgets.text(\"env\", \"\")\n", "dbutils.widgets.text(\"entity\", \"\")\n", "\n", "env = dbutils.widgets.get(\"env\").lower()\n", "entity = dbutils.widgets.get(\"entity\").upper()"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "a145f67e-3ad2-487c-a8c0-2baf3e318f9a", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# ========================== ingestion_job.py =============================\n", "\"\"\"\n", "Parallel Bronze → Silver ingestion for Databricks\n", "-------------------------------------------------\n", "* Uses Databricks widgets for parameter input\n", "* Loads only the newest dated folder for file feeds\n", "* Records progress in load_log_table\n", "\"\"\"\n", "\n", "from __future__ import annotations\n", "import os, re, time, logging\n", "from dataclasses import dataclass\n", "from datetime import datetime\n", "from concurrent.futures import ThreadPoolExecutor, as_completed\n", "from typing import List, Dict, Optional, Tuple\n", "\n", "import pandas as pd, chardet\n", "from pyspark.sql.types import *\n", "from pyspark.sql import SparkSession, DataFrame\n", "from pyspark.sql.functions import col, lit, current_timestamp\n", "from pyspark.sql.utils import AnalysisException\n", "from delta.tables import DeltaTable\n", "from delta.exceptions import ConcurrentWriteException\n", "\n", "# ───────────────────────── CONFIG ─────────────────────────\n", "@dataclass\n", "class Config:\n", "    env: str\n", "    entity: str\n", "    jdbc_url: str\n", "    jdbc_user: str = oracle_username      # set via secret / widget\n", "    jdbc_pwd:  str = oracle_password      # set via secret / widget\n", "    max_threads: int = 7\n", "    max_retries: int = 3\n", "    backoff_sec: float = 2.0\n", "\n", "    @property\n", "    def jdbc_opts(self):\n", "        return {\n", "            \"url\":       self.jdbc_url,\n", "            \"user\":      self.jdbc_user,\n", "            \"password\":  self.jdbc_pwd,\n", "            \"driver\":    \"oracle.jdbc.OracleDriver\",\n", "            \"fetchsize\": \"1000\",\n", "            \"oracle.net.CONNECT_TIMEOUT\": \"10000\",\n", "            \"oracle.net.READ_TIMEOUT\":    \"10000\"\n", "        }\n", "\n", "    @property\n", "    def ctl_tbl (self): return f\"mbcl_{self.env}_utility.metadata.pipelinecontrol\"\n", "    @property\n", "    def audit_tbl(self): return f\"mbcl_{self.env}_utility.metadata.metadata_pipeline_audit\"\n", "    @property\n", "    def log_tbl (self): return f\"mbcl_{self.env}_utility.metadata.load_log_table\"\n", "\n", "\n", "def get_config(env: str, entity: str) -> Config:\n", "    url = (\"jdbc:oracle:thin:@//************:1526/PLN010A\"\n", "           if env == \"prod\"\n", "           else \"**************************************************\")\n", "    return Config(env=env, entity=entity, jdbc_url=url)\n", "\n", "# ───────────────────────── LOGGING ────────────────────────\n", "_log = logging.getLogger(\"ingest\")\n", "if not _log.handlers:\n", "    h = logging.StreamHandler()\n", "    h.setFormatter(logging.Formatter(\"%(asctime)s [%(levelname)s] %(message)s\",\n", "                                     \"%Y-%m-%d %H:%M:%S\"))\n", "    _log.add<PERSON><PERSON>ler(h)\n", "    _log.setLevel(logging.INFO)\n", "\n", "# ──────────────────── CASE/QUOTE HELPERS ──────────────────\n", "# Updated delimiters: split on whitespace, hyphens, slashes, etc.\n", "_DELIMS = re.compile(r\"[ \\t\\r\\n\\f\\v\\-_/()]+\")\n", "_UPPER  = re.compile(r\"^[A-Z0-9_]+$\")\n", "\n", "def custom_cap(word: str) -> str:\n", "    if _UPPER.match(word):\n", "        return word\n", "    return word[0].upper() + word[1:] if len(word) > 1 else word.upper()\n", "\n", "def to_camel(s: str) -> str:\n", "    \"\"\"Convert column names to camelCase and replace special characters\"\"\"\n", "    s = s.strip()\n", "    # Replace common special characters with safe equivalents\n", "    s = s.replace('%', 'Pct').replace('&', 'n')\n", "    if not _DELIMS.search(s) and not _UPPER.match(s):\n", "        return s[0].lower() + s[1:]\n", "    parts = [p for p in _DELIMS.split(s) if p]\n", "    column_name = parts[0].lower() + ''.join(custom_cap(p) for p in parts[1:])\n", "    return column_name\n", "\n", "def clean_cols(df: DataFrame) -> DataFrame:\n", "    \"\"\"Convert all column names to camelCase and log renames\"\"\"\n", "    seen = {}\n", "    for c in df.columns:\n", "        new = to_camel(c)\n", "        if new not in seen:\n", "            df = df.withColumn<PERSON><PERSON>med(c, new)\n", "            seen[new] = c\n", "        else:\n", "            _log.warning(\"Duplicate column '%s' → '%s' dropped\", c, new)\n", "            df = df.drop(c)\n", "    _log.info(\"Column renaming map: %s\", seen)\n", "    return df\n", "\n", "_QUOTE = re.compile(r\"[^A-Z0-9_]\")\n", "def q(id_: str) -> str:\n", "    return f'\"{id_}\"' if _QUOTE.search(id_) else id_\n", "\n", "def table_exists(spark: SparkSession, ident: str) -> bool:\n", "    try:\n", "        spark.table(ident).limit(0).collect()\n", "        return True\n", "    except AnalysisException:\n", "        return False\n", "\n", "def safe_delete(path: str):\n", "    try:\n", "        dbutils.fs.ls(path)\n", "        dbutils.fs.rm(path, recurse=True)\n", "        _log.info(f\"Deleted '{path}'\")\n", "    except Exception:\n", "        _log.info(f\"Path '{path}' does not exist\")\n", "\n", "def archive_file(base: str):\n", "    # After the ingestion, we need to move the file to archive and delete the directory\n", "\n", "    # Create archive directory if not exists\n", "    archive_directory = f\"/Volumes/mbcl_{env}_bronze/{entity.lower()}/inbound_archive/\"\n", "    if not dbutils.fs.mkdirs(archive_directory):\n", "        _log.error(f\"Failed to create archive directory: {archive_directory}\") \n", "    \n", "    files = dbutils.fs.ls(f\"{base}\")\n", "    for file in files:\n", "        folder_name = file.path.split(\"/\")[-2]\n", "   \n", "        current_date = datetime.now().strftime('%Y/%m/%d/%H/%M')\n", "        archive_path_for_file = f\"{archive_directory}/{entity}/{folder_name}/{current_date}/\"\n", "        \n", "        # if directory exists, use an unique identifier\n", "        try:\n", "            dbutils.fs.ls(archive_path_for_file)\n", "            unique_id = datetime.now().strftime('%Y%m%d%H%M%S')\n", "            archive_path_for_file = f\"{archive_directory}/{entity}/{folder_name}/{current_date}_{unique_id}/\"\n", "        except:\n", "            pass\n", "\n", "        archive_path = f\"{archive_path_for_file}{file.name}\" \n", "        file_to_be_archived = os.path.join(base, file.name) \n", "        try: \n", "            dbutils.fs.mv(file_to_be_archived, archive_path, recurse=True)\n", "            _log.info(f\"Archived file: {file.name} to path: {archive_path}\")\n", "        except Exception as e:\n", "            _log.error(f\"Unable to archive: {file.name} to path: {archive_path}\")\n", "              \n", "    # remove the directory after archinving the files\n", "    safe_delete(base)\n", "# ──────────────────── ENTRY DESCRIPTION ──────────────────\n", "@dataclass\n", "class Entry:\n", "    src_tbl: str\n", "    schema: str\n", "    ref_schema: str\n", "    tgt_tbl: str\n", "    ts_raw: str\n", "    key_raw: List[str]\n", "    use_ts: Optional[bool]\n", "    is_silver: bool\n", "    src_type: str\n", "    file_root: str\n", "    ora_schema: str\n", "\n", "    @property\n", "    def bronze(self): return f\"{self.schema}.{self.tgt_tbl}\"\n", "    @property\n", "    def silver(self): return f\"{self.ref_schema}.{self.tgt_tbl}\"\n", "    @property\n", "    def ts_norm(self): return to_camel(self.ts_raw)\n", "    @property\n", "    def key_norm(self): return [to_camel(k) for k in self.key_raw]\n", "\n", "# ───────────────── AUDIT / LOAD LOG ───────────────────────\n", "def audit(spark, cfg, src, field, old, new):\n", "    if str(old) != str(new):\n", "        spark.createDataFrame(\n", "            [(src, field, str(old), str(new), datetime.now())],\n", "            \"src STRING, field STRING, old STRING, new STRING, ts TIMESTAMP\"\n", "        ).write.mode(\"append\").saveAsTable(cfg.audit_tbl)\n", "\n", "def log_load(spark, cfg, entry, rows, status, msg):\n", "    spark.createDataFrame(\n", "        [(cfg.entity, entry.src_tbl, entry.bronze, entry.silver,\n", "          status, rows, datetime.now(), msg)],\n", "        \"\"\"entity STRING, source_table STRING, bronze_table STRING, silver_table STRING,\n", "           status STRING, rows_ingested INT, ts TIMESTAMP, message STRING\"\"\"\n", "    ).write.mode(\"append\").option(\"mergeSchema\", \"true\").saveAsTable(cfg.log_tbl)\n", "    _log.info(\"%s | %s rows | %s | %s\", entry.src_tbl, rows, status, msg)\n", "\n", "# ──────────────────── FILE INGEST HELPERS ─────────────────\n", "def resolve_files(entry: Entry, cfg: Config) -> Tuple[str, List[str]]:\n", "    base = os.path.join(entry.file_root.format(env=cfg.env), cfg.entity, entry.src_tbl)\n", "    if not os.path.isdir(base):\n", "        raise FileNotFoundError(base)\n", "    files =[f for f in dbutils.fs.ls(f\"{base}\") if not f.isDir()]\n", "    latest_file = sorted(files, key=lambda x:x.modificationTime, reverse=True)[0]\n", "    latest_file = [os.path.join(base, latest_file.name)]\n", "    return base, latest_file\n", "\n", "def load_file(p: str, typ: str):\n", "    try:\n", "        if typ == \"csv\":\n", "            # Detect encoding using chardet\n", "            with open(p, \"rb\") as h:\n", "                raw = h.read(1_000_000)\n", "                enc = chardet.detect(raw).get(\"encoding\") or \"utf-8\"\n", "                if enc.lower() in {\"ascii\", \"unknown\", \"none\"}:\n", "                    enc = \"utf-8\"\n", "            try:\n", "                df = pd.read_csv(p, encoding=enc, on_bad_lines=\"skip\", dtype=str, na_values=[\"\", \"null\", \"NaN\"])\n", "            except UnicodeDecodeError as e:\n", "                # Fallback encodings if utf-8 fails\n", "                _log.warning(\"Encoding '%s' failed for %s, retrying with latin1\", enc, p)\n", "                try:\n", "                    df = pd.read_csv(p, encoding=\"latin1\", on_bad_lines=\"skip\")\n", "                except Exception as e2:\n", "                    return None, (p, f\"Encoding fallback failed: {str(e2)}\")\n", "\n", "        elif typ == \"parquet\":\n", "            df = pd.read_parquet(p)\n", "\n", "        elif typ == \"xlsx\":\n", "            df = pd.read_excel(p)\n", "\n", "        else:\n", "            return None, (p, f\"Unsupported file type: {typ}\")\n", "\n", "        df[\"filename\"] = os.path.basename(p)\n", "        return df, None\n", "\n", "    except Exception as e:\n", "        return None, (p, str(e))\n", "\n", "def load_raw_batch(spark, cfg, entry):\n", "    try:\n", "        base, paths = resolve_files(entry, cfg)\n", "    except Exception as e:\n", "        return None, [(entry.file_root, str(e))], None\n", "    dfs, errs = [], []\n", "    with ThreadPoolExecutor(max_workers=4) as ex:\n", "        futs = {ex.submit(load_file, p, entry.src_type): p for p in paths}\n", "        for f in as_completed(futs):\n", "            d, e = f.result()\n", "            (dfs.append(d) if d is not None else errs.append(e))\n", "    if not dfs:\n", "        return None, errs, base\n", "    if len(dfs) == 0:\n", "        return None, [(\"No data files found\", \"Empty DataFrame list\")], base\n", "    if all(df.empty for df in dfs):\n", "        return None, [(\"All data files are empty\", \"Empty DataFrame list\")], base\n", "\n", "    res = pd.concat(dfs, ignore_index=True)\n", "    spark_df = spark.createDataFrame(res)\n", "    for column in spark_df.schema.fields:\n", "        if column.dataType.typeName() == \"void\":\n", "            spark_df = spark_df.withColumn(column.name, col(column.name).cast(StringType()))\n", "    return spark_df, errs, base\n", "\n", "\n", "# ─────────────────── DELTA HELPERS ────────────────────────\n", "def align_schema(schema, incoming):\n", "    have = set(incoming.columns)\n", "    for f in schema:\n", "        if f.name not in have:\n", "            incoming = incoming.withColumn(f.name, lit(None).cast(f.dataType))\n", "    return incoming\n", "\n", "def delta_retry(df, mode, table, cfg):\n", "    _log.info(\"Columns present in the csv file: %s\", df.columns)\n", "    for i in range(cfg.max_retries + 1):\n", "        try:\n", "            (df.write.format(\"delta\")\n", "               .mode(mode).saveAsTable(table))\n", "            return\n", "        except Exception as e:\n", "            error_str = str(e)\n", "            if \"ConcurrentWriteException\" in error_str or \"ConcurrentAppendException\" in error_str:\n", "                if i == cfg.max_retries:\n", "                    raise\n", "                wait = cfg.backoff_sec * (2 ** i)\n", "                _log.warning(\"delta clash %s – %.1fs\", table, wait)\n", "                time.sleep(wait)\n", "            else:\n", "                raise\n", "   \n", "# ───────────────── TIMESTAMP RESOLVER ─────────────────────\n", "def get_timestamp_column(schema, entry: Entry) -> Optional[str]:\n", "    cols = [f.name for f in schema]\n", "    if entry.ts_raw  in cols: return entry.ts_raw\n", "    if entry.ts_norm in cols: return entry.ts_norm\n", "    _log.warning(\"TS column '%s' absent in %s\", entry.ts_raw, cols)\n", "    return None\n", "\n", "# ────────────────── WORKER FUNCTION ───────────────────────\n", "def worker(entry: Entry, cfg: Config, cache: Dict[str, bool]):\n", "    spark = SparkSession.builder.getOrCreate()\n", "    msg_path = \"\"\n", "\n", "    # Determine timestamp filtering on first run\n", "    if entry.use_ts is None:\n", "        cols = (spark.read.format(\"jdbc\").options(**cfg.jdbc_opts)\n", "                .option(\"dbtable\",\n", "                        f\"(SELECT * FROM {entry.ora_schema}.{entry.src_tbl} WHERE ROWNUM=1) t\")\n", "                ).load().columns\n", "        entry.use_ts = any(c.upper() == entry.ts_raw.upper() for c in cols)\n", "        audit(spark, cfg, entry.src_tbl, \"use_last_ts\", None, entry.use_ts)\n", "        spark.sql(\n", "            f\"\"\"\n", "            UPDATE {cfg.ctl_tbl}\n", "               SET use_last_ts={str(entry.use_ts).lower()}\n", "             WHERE SourceTableName='{entry.src_tbl}'\n", "               AND entity='{cfg.entity}'\n", "            \"\"\"\n", "        )\n", "\n", "    bronze_exists = cache.get(entry.bronze) or table_exists(spark, entry.bronze)\n", "    bronze_schema = spark.table(entry.bronze).schema if bronze_exists else None\n", "   \n", "    if bronze_exists:\n", "        cache[entry.bronze] = True\n", "\n", "    # Build WHERE clause if timestamp filtering is enabled\n", "    where = \"\"\n", "    if entry.use_ts and bronze_schema:\n", "        tscol = get_timestamp_column(bronze_schema, entry)\n", "        if tscol:\n", "            default_start = datetime(1970, 1, 1)\n", "            last_val = (spark.table(entry.bronze)\n", "                        .agg({tscol: \"max\"})\n", "                        .collect()[0][0] or default_start)\n", "            where = (\n", "                \"WHERE {col} > TO_TIMESTAMP('{val}', 'YYYY-MM-DD HH24:MI:SS.FF')\"\n", "                .format(col=q(entry.ts_raw),\n", "                        val=last_val.strftime(\"%Y-%m-%d %H:%M:%S.%f\")[:-3])\n", "            )\n", "\n", "    # Load data\n", "    if entry.src_type in {\"csv\", \"parquet\", \"xlsx\"}:\n", "        df, errs, base = load_raw_batch(spark, cfg, entry)\n", "        msg_path = base or \"\"\n", "        _log.info(\"[%s] folder %s\", entry.src_tbl, base)\n", "        if df is None:\n", "            if errs == [('All data files are empty', 'Empty DataFrame list')]:\n", "                archive_file(base)\n", "            log_load(spark, cfg, entry, 0, \"FAILED\", f\"{base} | {errs}\")\n", "            return\n", "    else:\n", "        df = (spark.read.format(\"jdbc\").options(**cfg.jdbc_opts)\n", "              .option(\"dbtable\",\n", "                      f\"(SELECT * FROM {entry.ora_schema}.{entry.src_tbl} {where}) t\")\n", "              ).load()\n", "        _log.info(\"[%s] Oracle view %s.%s\", entry.src_tbl,\n", "                  entry.ora_schema, entry.src_tbl)\n", "    \n", "    # Clean/align\n", "    df = clean_cols(df).withColumn(\"ingestionTimestamp\", current_timestamp())\n", "    if bronze_schema:\n", "        df = align_schema(bronze_schema, df)\n", "\n", "    # Write bronze\n", "    rows = df.count()\n", "    write_mode = \"overwrite\" if entry.src_type in {\"csv\", \"parquet\", \"xlsx\"} else \"append\"\n", "    try:\n", "        delta_retry(df, write_mode, entry.bronze, cfg)\n", "        cache[entry.bronze] = True\n", "        # archive file after ingestion\n", "        archive_file(base)\n", "        \n", "        log_load(spark, cfg, entry, rows, \"SUCCESS\", msg_path)\n", "    except Exception as e:\n", "        _log.warning(f\"File for {entry.src_tbl} cannot be processed due to the following error: {e}\")\n", "   \n", "    \n", "    # Write silver\n", "    if entry.is_silver:\n", "        ds = df\n", "        for k in entry.key_norm:\n", "            ds = ds.filter(col(k).isNotNull())\n", "        ds = ds.dropDuplicates(entry.key_norm)\n", "\n", "        if table_exists(spark, entry.silver):\n", "            cond = \" AND \".join([f\"target.{k}=source.{k}\" for k in entry.key_norm])\n", "            for i in range(cfg.max_retries + 1):\n", "                try:\n", "                    (DeltaTable.forName(spark, entry.silver).alias(\"target\")\n", "                     .merge(ds.alias(\"source\"), cond)\n", "                     .whenMatchedUpdateAll()\n", "                     .whenNotMatchedInsertAll()\n", "                     .execute())\n", "                    break\n", "                except ConcurrentWriteException:\n", "                    if i == cfg.max_retries:\n", "                        raise\n", "                    wait = cfg.backoff_sec * (2 ** i)\n", "                    _log.warning(\"merge clash %s – %.1fs\", entry.silver, wait)\n", "                    time.sleep(wait)\n", "        else:\n", "            try:\n", "                delta_retry(ds, \"overwrite\", entry.silver, cfg)\n", "                log_load(spark, cfg, entry, rows, \"SUCCESS\", msg_path)\n", "            except Exception as e:\n", "                _log.warning(f\"File for {entry.src_tbl} cannot be processed due to the following error: {e}\")\n", "\n", "# ───────────────────── MAIN DRIVER ────────────────────────\n", "def main(cfg: Config):\n", "    spark = SparkSession.getActiveSession()\n", "    rows = (spark.table(cfg.ctl_tbl)\n", "            .filter((col(\"entity\") == cfg.entity) & (col(\"IsActive\") == True))\n", "            .collect())\n", "\n", "    entries = [Entry(\n", "        src_tbl    = r[\"SourceTableName\"],\n", "        schema     = r[\"Schema<PERSON>ame\"],\n", "        ref_schema = r[\"RefinedSchemaName\"],\n", "        tgt_tbl    = r[\"TargetTableName\"],\n", "        ts_raw     = r[\"last_update_column\"],\n", "        key_raw    = r[\"KeyColumn\"] if r[\"KeyColumn\"] is None else r[\"KeyColumn\"].split(\",\"),\n", "        use_ts     = r[\"use_last_ts\"],\n", "        is_silver  = r[\"IsSilver\"],\n", "        src_type   = r[\"SourceType\"].lower(),\n", "        file_root  = r[\"Input_FilePath\"],\n", "        ora_schema = r[\"oracle_schema\"]\n", "    ) for r in rows]\n", "\n", "    if not entries:\n", "        _log.info(\"nothing to ingest\")\n", "        return\n", "\n", "    n_threads = max(1, min(len(entries), cfg.max_threads))\n", "    _log.info(\"processing %d tables with %d threads\", len(entries), n_threads)\n", "    cache: Dict[str, bool] = {}\n", "\n", "    with ThreadPoolExecutor(max_workers=n_threads) as pool:\n", "        futs = [pool.submit(worker, e, cfg, cache) for e in entries]\n", "        for f in as_completed(futs):\n", "            f.result()       # re-raise exceptions\n", "\n", "    _log.info(\"all tables processed\")\n", "\n", "# ──────────────────── ENTRYPOINT ──────────────────────────\n", "if __name__ == \"__main__\":\n", "    if not env or not entity:\n", "        raise ValueError(\"Both 'env' and 'entity' parameters must be provided\")\n", "\n", "    _log.info(\"Starting ingestion job for env=%s, entity=%s\", env, entity)\n", "    try:\n", "        cfg = get_config(env, entity)\n", "        main(cfg)\n", "        _log.info(\"Ingestion job completed successfully\")\n", "    except Exception as exc:\n", "        _log.error(\"Job failed: %s\", exc)\n", "        raise"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "inputWidgetPreferences": null, "language": "python", "notebookMetadata": {"mostRecentlyExecutedCommandWithImplicitDF": {"commandId": -1, "dataframes": ["_sqldf"]}, "pythonIndentUnit": 4}, "notebookName": "PC With FileLoader", "widgets": {"entity": {"currentValue": "xva_refactored", "nuid": "1af07a4a-e23f-4438-8445-de81ea119cc9", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "", "label": null, "name": "entity", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "", "label": null, "name": "entity", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}, "env": {"currentValue": "dev", "nuid": "482e5bae-a50d-40f3-984b-26a0d756164c", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "", "label": null, "name": "env", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "", "label": null, "name": "env", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}